#!/bin/bash

# Define app name variable
APP_NAME="ROOO-Driver-60"
# APP_NAME="ROOO-Driver-dev"

echo "Building $APP_NAME release APK..."
echo "This will build the APK and copy it to /home/<USER>/projects/APKs/"

# Navigate to the project directory (if running from elsewhere)
cd "$(dirname "$0")"
flutter build apk -v
echo "Build completed. Copying APK..."
cd "build/app/outputs/apk/release/"

# Move the APK file to the specified location
mv app-release.apk "/home/<USER>/projects/APKs/$APP_NAME.apk"
echo "APK moved to /home/<USER>/projects/APKs/"

cd ..

echo "Build process completed!"
