import UIKit
import Flutter
import GoogleMaps
import workmanager_apple




@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> <PERSON><PERSON> {
    GMSServices.provideAPIKey("AIzaSyBkjTXbH4inE0qL2Ew8CrmdkgKrmbx5HMQ")
   
    WorkmanagerPlugin.registerPeriodicTask(
      withIdentifier: "app.rooo.driver.background_driver_status_update",
      frequency: NSNumber(value: 20 * 60) // 20 minutes in seconds
    )
 // This is required to make any communication available in the action isolate.
    // FlutterLocalNotificationsPlugin.setPluginRegistrantCallback { (registry) in
    //     GeneratedPluginRegistrant.register(with: registry)
    // }

    // if #available(iOS 10.0, *) {
    //   UNUserNotificationCenter.current().delegate = self as UNUserNotificationCenterDelegate
    // }

  
    GeneratedPluginRegistrant.register(with: self)
    
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
