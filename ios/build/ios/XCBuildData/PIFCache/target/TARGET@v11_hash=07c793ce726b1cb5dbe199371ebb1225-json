{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b329e613050d0acc0d55e78bfc498bb5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "mapbox_maps_flutter", "PRODUCT_NAME": "mapbox_maps_flutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.8", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988c854a0a2dfec57754e1065fbcb48b70", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984a9a990c09b9764ee8204a22f4d0cf24", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "mapbox_maps_flutter", "PRODUCT_NAME": "mapbox_maps_flutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.8", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ddc4edce0e0e6afcfce329b655679ae2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984a9a990c09b9764ee8204a22f4d0cf24", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "mapbox_maps_flutter", "PRODUCT_NAME": "mapbox_maps_flutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.8", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9847cd8db457913989752c8c1b17f8cf75", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f2cb49ed4bf2a324033e0ceed8c4d375", "guid": "bfdfe7dc352907fc980b868725387e98ead8d576faaa5d53becf115c6ecef0bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a228f1e0a7c2b8f0b75cd0c152ee001", "guid": "bfdfe7dc352907fc980b868725387e9864e590a33980c7380fb8e41bff33dfac", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987b57e8a53278fc20a0ad064265ef0112", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98710371db37ce93c21ff4c546ab45e844", "guid": "bfdfe7dc352907fc980b868725387e98b96b019fd4f98253f8fcd0c440e4b094"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981761ff43ac238267d64ccd5c0b949cfc", "guid": "bfdfe7dc352907fc980b868725387e989e99f28810ccbf93c2380993fc62e1f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a06c0b9e8fe88b3a9dddee416fec38ec", "guid": "bfdfe7dc352907fc980b868725387e9832dd12521881f87f24d067b34333375d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f51d5973576f747c0f0d6d525c2ace2", "guid": "bfdfe7dc352907fc980b868725387e98b4085c8e8450189ddc55434d9f2bdaee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bb4ed3b3ae7cf6724800d4a3703a506", "guid": "bfdfe7dc352907fc980b868725387e9863da2a7e025703c540992b759047ae3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bc7ad4174c666111237bdc55a1954d3", "guid": "bfdfe7dc352907fc980b868725387e9824cf4e106302dc9816ee5d3f19150d2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9a228dd7667e56db5ca85c22f11192d", "guid": "bfdfe7dc352907fc980b868725387e982d7ae74f1749c5bd179f327a3418a64a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858f244acdbe675ab19963bb24318bce2", "guid": "bfdfe7dc352907fc980b868725387e983594a37eae0a7158b8372f2b9150e555"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a891cc01bab7bdcc7b65f8578eb209b2", "guid": "bfdfe7dc352907fc980b868725387e98fc49ebf4c626b4e8153eb5359c6df585"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cde23d10450169bb539f028d4c7e8e0", "guid": "bfdfe7dc352907fc980b868725387e9833c20a494650a7a3cb8853b81e97d919"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98681364bb95b798a628e9ca854f47e1d8", "guid": "bfdfe7dc352907fc980b868725387e98825db3987178e696e9edc8a413ee96d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae7c79ac5149ac951d43bd390251cb0d", "guid": "bfdfe7dc352907fc980b868725387e98a571ef11e47fe20802901f3139c583df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e3820c9ad02849c1fdf4b5365971741", "guid": "bfdfe7dc352907fc980b868725387e98b8b049b6fc648baf280b3e6f9b045766"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c68aa85272d8fb496e92aed5b95d00f", "guid": "bfdfe7dc352907fc980b868725387e984993d29ea68bdb656dc3b3d4daf46e46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d07acf750caaed3857e57fc0d25c05af", "guid": "bfdfe7dc352907fc980b868725387e98c398509defbae7446985a73c26673af7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2d58f74a6be367fd828e8a25213608a", "guid": "bfdfe7dc352907fc980b868725387e981053e71edfee085f11f3a6a441390826"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98332cb42280ead885e818cb8e1abec5a4", "guid": "bfdfe7dc352907fc980b868725387e985aa8ae34fae37d761f204b35e2f2ae5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98472d3c59c8242181e3a74ce03f84029b", "guid": "bfdfe7dc352907fc980b868725387e98a546d3b79725c22415fb97729d282769"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6c4adbc69e64e91937c3955038e2179", "guid": "bfdfe7dc352907fc980b868725387e98745c90e9cf8827acbed3893c0735f27b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fb343a4df4139b7f82bad939ed1b4d2", "guid": "bfdfe7dc352907fc980b868725387e98155b0d980c16b472d497951fbdfdcaaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbf5a2d85bb03c0c7924f4d081221569", "guid": "bfdfe7dc352907fc980b868725387e989447b8acda6502e6520a2f16a865b0da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832780666824f3c81af3e6861cc6c92eb", "guid": "bfdfe7dc352907fc980b868725387e988a7eeda4d24abe89fb41334d1c94213b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871e1aaf1262364b326439e842f33d50f", "guid": "bfdfe7dc352907fc980b868725387e987219f7b0755593af758850742d8720ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c21877e852f4a328dc6a868bbd70065e", "guid": "bfdfe7dc352907fc980b868725387e9847a8cee68af6b129d01d160a533a43b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3af9db15567c80d0fac890b667ea29b", "guid": "bfdfe7dc352907fc980b868725387e98514338f53997b4d87a95ee76e918b923"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98301b5b4136fdcd6658b655409f7dbc7b", "guid": "bfdfe7dc352907fc980b868725387e98036e7af8800099747e4498b89aa1f514"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a91a50b91a3c103f0ad87579f46c2cc", "guid": "bfdfe7dc352907fc980b868725387e985b4b2f77aec54822ffc41faf321e491c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984245f00da7dd31388108a27c5f8a0849", "guid": "bfdfe7dc352907fc980b868725387e9811f1d6ef9e863a529ba423e924307caa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989aa9ca11b7eefbe55db00d62c9e375ea", "guid": "bfdfe7dc352907fc980b868725387e98ce6e19c0b82bee16f5054309b33809fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98200670cd2c54d6c2ce75c3a9a17fd3c8", "guid": "bfdfe7dc352907fc980b868725387e98bc145eb1c6201507d01a42809549b893"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98248e5be7a5d635c803e8c0feeacfe516", "guid": "bfdfe7dc352907fc980b868725387e98dab0bd2b0ea6ec7caf6457edd7af058e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fe33fee5f18de059aee4fecf60287ff", "guid": "bfdfe7dc352907fc980b868725387e98485a31de8ff12f6160a82e8d1dc1b33b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d4e046507eb9a8e1e44e4daad31bb24", "guid": "bfdfe7dc352907fc980b868725387e9873d17f2761cd1340fe9113b210ef2191"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abdc4cc3e02adf63aed54f85fa8a6bb7", "guid": "bfdfe7dc352907fc980b868725387e987eeb0fcb75201972b7f1e2c0c9cbf975"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986466ba0518c656d85594b517e2e5af35", "guid": "bfdfe7dc352907fc980b868725387e98a097fe42c9948435339a3ccb0169df93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd5a9f42216e5775238ad6522dc3fcf4", "guid": "bfdfe7dc352907fc980b868725387e983468cda5f59dce16c2ac0bd15928ab6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830cf1202993cabf90504cc209320c1db", "guid": "bfdfe7dc352907fc980b868725387e9804125b6e6d111326b15d2d693101b802"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b30807d8fc64bf527944de91b0c66664", "guid": "bfdfe7dc352907fc980b868725387e98b1bc161a83c1628f1ea83407245dd04c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98116834c7214ba6a26b7269270c5c2e5e", "guid": "bfdfe7dc352907fc980b868725387e9815c95f755d8ca64a5c83d24df5f61bb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9751a2fd2e5f6132af55f37de8f8811", "guid": "bfdfe7dc352907fc980b868725387e987daf8ac6edfc20c78751f8f4b34b78a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f3661ecea77b5aa93e2f5c90ff926af", "guid": "bfdfe7dc352907fc980b868725387e9829b0ed29fa70536ac4982ca6a65c514e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860baefe34dce2ec23c8ce819bb77b0f9", "guid": "bfdfe7dc352907fc980b868725387e98cf322fd09e59d5989cae5987e5d2e721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac9ace61af91c79cb362c5b25bc04942", "guid": "bfdfe7dc352907fc980b868725387e986d29c5edc8aa3a59de5edcb57cb6fa6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983be855861aad765ba63f30ca9004fd2b", "guid": "bfdfe7dc352907fc980b868725387e985977c720848ba6780fb96252bd8c210e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983426dc44a7e9e3405bbc0e4fc5336344", "guid": "bfdfe7dc352907fc980b868725387e98f9ad2988adf0a101f95d56a4ffffb878"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848792525a399f2e09618416eb439a051", "guid": "bfdfe7dc352907fc980b868725387e987cb08a35bbb75ffa28731f1e12ed29c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98222d841678b4cb848b0ee9f9662f46fa", "guid": "bfdfe7dc352907fc980b868725387e986c6fb33c18385297abd93a9db401bc71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1d0eeddc876fce360b2a1eba16f1faf", "guid": "bfdfe7dc352907fc980b868725387e980e47a6aacda22b63262eb27b47e092df"}], "guid": "bfdfe7dc352907fc980b868725387e985feda367edc8c6dc0f3b50f6b5eee22a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e987eb581da3d62d83dd3adf1fd81a1c793"}], "guid": "bfdfe7dc352907fc980b868725387e9820e32b5413ab04e95cd27cf95a1ab475", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e71135d6b208ed88d5f42b7950cde36c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98dfb03d8133a4c4d0ce289957deff5e40", "name": "MapboxMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e986fc9ace0c8808d101bf5c63f54197cca", "name": "<PERSON><PERSON>"}], "guid": "bfdfe7dc352907fc980b868725387e98d1b046a0c587f9fad447e9b5f311f327", "name": "mapbox_maps_flutter", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98874ae0603cd389f3468cc23a44b3f238", "name": "mapbox_maps_flutter.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}