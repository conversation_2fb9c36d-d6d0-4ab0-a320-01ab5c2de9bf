{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9857a119ec3bf6c93a2565e14b7744c5e7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981dbd5809b4ad8bdb5af93271b69924c8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987093d5167b19c5b98eab776e262c828e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9838a236736514cde9f8368618ff5f0233", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987093d5167b19c5b98eab776e262c828e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b530f75cb6e394c53fc5df5e1d9a378a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980bfc105465923ba826c7edf7f1885c92", "guid": "bfdfe7dc352907fc980b868725387e989a4090b9fd71016889f2325f486ab790", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98418f480beca941a06de7dbd06142182a", "guid": "bfdfe7dc352907fc980b868725387e9840f551dfc4c0d4368be90481dc1a4cf6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5b4498a8a86dedb369694a0cf1dd2fb", "guid": "bfdfe7dc352907fc980b868725387e98d0150285620a40e3782c668932a97f08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bb57912ab87264c248ebea8fc1f17ef", "guid": "bfdfe7dc352907fc980b868725387e98c97e7f9db04c34622938d528d5ff1b05", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2c78aa1caf0401f50808edde2799c90", "guid": "bfdfe7dc352907fc980b868725387e986ccaf4aedb00a72e83332657df95b271", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fb406e79436614b60bf0fd4904979d4", "guid": "bfdfe7dc352907fc980b868725387e98a8550070badbe93753009993376bf0c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d3aadb6db8ff5917892bf3fa4ec79ce", "guid": "bfdfe7dc352907fc980b868725387e9878a4a2fb40595983008b60cd484c2522", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840f0a0ecb75091941a597246d2da8bfa", "guid": "bfdfe7dc352907fc980b868725387e980497887bd46dc92c383e9bf0b1c93e5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981df6c3338cb456b29a13437ca93c39d1", "guid": "bfdfe7dc352907fc980b868725387e98901501da1de32cbcd23538efe3edf249", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981351e8f5f4cee36bee3baa179fa219d7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982abf764e43bc7aea449dc4e4d3d28866", "guid": "bfdfe7dc352907fc980b868725387e98f4bc9b7b9e0d881ac4b7c8629aa8286b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f2640a0449233335b3cccc761f4333", "guid": "bfdfe7dc352907fc980b868725387e98e707185df28cfbb6872f525c9b505fd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d644466581f7398d854ed194d5e3a09", "guid": "bfdfe7dc352907fc980b868725387e98b955bae7dce67556261bf8f941aa60c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd1163d26aee3de45ad73cb1de672b5a", "guid": "bfdfe7dc352907fc980b868725387e984152b61fe861c7e29e0a58046ad7fcfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874a738314805b470d3d42198179ebbaf", "guid": "bfdfe7dc352907fc980b868725387e9838fde63fc4c3d3d88a37013c63066521"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c387929030d35b4aec6a4c423c55e663", "guid": "bfdfe7dc352907fc980b868725387e98c38d527317676d048bd42a762d1dc806"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b95b54d72b694543b72c1900ffcb7903", "guid": "bfdfe7dc352907fc980b868725387e980c04a86d83e3430fda3354a2a75dd38e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898e7c762c55ca1d71c9ff392d0eedacd", "guid": "bfdfe7dc352907fc980b868725387e983a7af592b90d96eec9489ef6c68aabe0"}], "guid": "bfdfe7dc352907fc980b868725387e98db08afd6361e119c2580190abbb5742c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e98f6aeda9508b24c20e80837500cc11288"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f6815db8b12f96ce7ef9e16aa9f5816", "guid": "bfdfe7dc352907fc980b868725387e9829661dc4ec84b6ccce59166ee1a573dd"}], "guid": "bfdfe7dc352907fc980b868725387e9831bc6672147b3bf7f5c42235e1d46fe6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a8f6a86795d149e67f3eec8121b0ce25", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e982cd7d6328b1df16e751b6314c743deb4", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e984deb9b84b63868be3404d82085608d86", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}