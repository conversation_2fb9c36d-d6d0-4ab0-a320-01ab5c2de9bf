{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987e798ba7b83abc3774cee8f1e8ef832b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98926985a4684bbc8a54f14b3b3ce9661b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ab6778ce69b4e1c1bc05f900e9787ce", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839fae81d86b8e8fcc32640c84fcd34ff", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ab6778ce69b4e1c1bc05f900e9787ce", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989901e3bde4b071a42a05e59c75312a01", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a795c336521d6d311e52afd9c6a7255c", "guid": "bfdfe7dc352907fc980b868725387e9859429ead7216af2fd14e39d611d7ef11", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850effed6b8927708f8181358f9216eec", "guid": "bfdfe7dc352907fc980b868725387e98e229668279b8db2f3af76951474eb8c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842f7a02a15a01de1f39374e066adf79b", "guid": "bfdfe7dc352907fc980b868725387e98ff1165cf14e16ba0b77438eec53d1839", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833a04812880464b4b9fd088ab3020f73", "guid": "bfdfe7dc352907fc980b868725387e981eb35b823842eddc5bddc4f5fc84ee64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dd036ac5edd43855ed61b316c19ef30", "guid": "bfdfe7dc352907fc980b868725387e989a15a0f081dec978250daf8e21f19c80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894c98fc9b288b21bcacd426170462efc", "guid": "bfdfe7dc352907fc980b868725387e98c62c4ea25b07936f83249c84f9e8bafc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3cfad73f1bc0dfab4e0c091648b6ab4", "guid": "bfdfe7dc352907fc980b868725387e98c7b7d712159298a7973504a8143a5754", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd17e711a18c36d2bed4dce79c4c90b5", "guid": "bfdfe7dc352907fc980b868725387e988e9b75dde449c31d843155246e8ff45d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a61f010d11e0ddc5680e3e1ffa21a060", "guid": "bfdfe7dc352907fc980b868725387e9833e37aed2e641489ff8cdd28f60cf315", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caa3f7f28e1ed675a2016c314a225e4c", "guid": "bfdfe7dc352907fc980b868725387e98180258c4cd9634c47d8e32edc56ac86e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5ae1cff975b0dfcb38b738a37598000", "guid": "bfdfe7dc352907fc980b868725387e981ad687bc2ff64da1316e0b79d5e12809", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dcdaa14df3eb3bdbe8d983c49500a7b", "guid": "bfdfe7dc352907fc980b868725387e98e695d1eeb9e698d9602d43a05b0897c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98021dfa836916391801db4b0b3147cc92", "guid": "bfdfe7dc352907fc980b868725387e9838690a30ae302fa612a5a1c37b8fff36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c6bb04b02256f917b5944b46758538f", "guid": "bfdfe7dc352907fc980b868725387e98588665c18bcfadfe1c40626952d37ad4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848bd7269c79aa8b0ad1d1b5458509c8d", "guid": "bfdfe7dc352907fc980b868725387e98f511f723c1bad7b1c29ad5f1c35f9cd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813b5247f98b501b324a838acd534e1d4", "guid": "bfdfe7dc352907fc980b868725387e98df32a911f31b0898509173014edc50f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ea99a31cff0e4b1cbc95abc50dd4f12", "guid": "bfdfe7dc352907fc980b868725387e98ac19e7d244bd69e8db816772bd3ab9d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fc0e582b9e3d5ac6138edcf53825b19", "guid": "bfdfe7dc352907fc980b868725387e9876e060c69de2790d01c24d7970981e75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e8b69751a000b8c44e68ccafa85b0ab", "guid": "bfdfe7dc352907fc980b868725387e98fe336254630923b082703dea5497bec1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ae5b35bdc90fd9d12d07bee5ae49ac3", "guid": "bfdfe7dc352907fc980b868725387e98cb006c82f464f617ca002a2cf0916837", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3e6c4ac701b80bca2e83ec44135a35e", "guid": "bfdfe7dc352907fc980b868725387e98e7a670e8fe0a03b189b6bd37ff79d09f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847e63daa183689b5c76dd00b64b0276c", "guid": "bfdfe7dc352907fc980b868725387e98e67c7e6bcf1c1eb536cef7f6c56812e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae22f37324354dd853c26c4269e6932e", "guid": "bfdfe7dc352907fc980b868725387e98b29b9137eec27560824c39595d78c976", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98328647b7f108cc57576d641746086af9", "guid": "bfdfe7dc352907fc980b868725387e982b3bf004aecc6bf53421a989e3305b9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98488e1256ebeb784d876ecfaee21d8e99", "guid": "bfdfe7dc352907fc980b868725387e98b8f46a73d66a12bddd684ed1c70a92aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a6e6ab0067e09e6d60b8676da859985", "guid": "bfdfe7dc352907fc980b868725387e98eb743d9e16f2d8523541e94ac6ed6346", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2e88dea553d765813aac21814cb968e", "guid": "bfdfe7dc352907fc980b868725387e98aa452a3019c0a5cee2ce46ba861d3649", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811685dc822e6871798e21d47c9d0b6e9", "guid": "bfdfe7dc352907fc980b868725387e98d0ade107876cafc91410403281fba26d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2c69f8b5bcb119b85d4ce8dffe32e46", "guid": "bfdfe7dc352907fc980b868725387e98d4466030dbfaeda625ffbb0b8baf4f5a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98474380a9e6ba0a3b2161bd2f7edc1b70", "guid": "bfdfe7dc352907fc980b868725387e9830b6e3b9db2cb3ecea5adcfa24404f05", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9891b7cafb877524f9976d2288bb3fd8fe", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c05f318be757d07c9d12a44d66222423", "guid": "bfdfe7dc352907fc980b868725387e987c34d4230a45565b063c590685ae4efd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a7f8ed7ceacdf5b16cfa990a817cf3f", "guid": "bfdfe7dc352907fc980b868725387e98e7df7c1a1b64a9be1f7fc9598ec211bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e997bf981b8ddda08f97ec31115717e", "guid": "bfdfe7dc352907fc980b868725387e98956040e3352f84dd7f90c7beff5fc1ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b24bc98a7a1128fd3d561e23da9ff02", "guid": "bfdfe7dc352907fc980b868725387e980552822f59bf073b3a1ebbf80ba2ee92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bf2b7473ea8d63a0cca3222284b20ae", "guid": "bfdfe7dc352907fc980b868725387e98ce2d8f9a97cfdc2aa3091c31e883f9f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa85710c2f3bde39fb0f0bad6b86e0af", "guid": "bfdfe7dc352907fc980b868725387e9874037bdae32a01485707264bab1a4d76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98373ff628f7587405f7fe0de181b6533b", "guid": "bfdfe7dc352907fc980b868725387e981db143ce96dfe2b24fb3e18dcb1597bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eca4c467ad5a000e41eb7b5100a99e44", "guid": "bfdfe7dc352907fc980b868725387e9826437af8670026bb702e27580cdbd796"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988365d604ce0935e5233f31332ef728af", "guid": "bfdfe7dc352907fc980b868725387e98796ca61c3e6af9989b6bfa5447400ac3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844fcb290eed12ae1385e128bbd6f8917", "guid": "bfdfe7dc352907fc980b868725387e98f025ef30144b48a93836b9fc9d0d43e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd0ea5124e6e3ddaf404458bd62f0694", "guid": "bfdfe7dc352907fc980b868725387e984dabd0cb1ff407007ddfa0d4e83acdae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98489ede9d9a0d1d415f3cb29527e551f7", "guid": "bfdfe7dc352907fc980b868725387e9863238a203630fd00632efad4be679656"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98689c99efe7aef81f93bfced2970186b0", "guid": "bfdfe7dc352907fc980b868725387e98a9c0dcb9ac0f64902c3d9a8524e110c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3388b39850345475763ad9128a8c709", "guid": "bfdfe7dc352907fc980b868725387e987a436587ba5fb93505f69ac83317063f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbf127f556adac15e10d130b4a13a3dd", "guid": "bfdfe7dc352907fc980b868725387e98ebce6eecb83f807abc99b0e19258b27b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f2f5687498aafd9566f3879535a53c3", "guid": "bfdfe7dc352907fc980b868725387e98bc3cce5658f6f45d2f88df89510c6c2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b062fed7755a2ffae3a6d1856c117f4", "guid": "bfdfe7dc352907fc980b868725387e982e9098458b305383b9243ec2db7adfb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885e8f219e4b42c4458998c026e0608f3", "guid": "bfdfe7dc352907fc980b868725387e98b5ad47b59f5565eb819b6b4051913bb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f51e5c4169bd31db26e0707c3883c31e", "guid": "bfdfe7dc352907fc980b868725387e98d7093b69af0723a491909235622ec6e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffc07d5af156ab724fd398d218678457", "guid": "bfdfe7dc352907fc980b868725387e9875b35ec2d3dede00339a5e644ea8a3b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce6ee753c0a71c6d3bad64cb5f89e5b6", "guid": "bfdfe7dc352907fc980b868725387e98b8c0ef00d8301b4ad8789b09b99b9196"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e4da70e2310265f719305b461a4902c", "guid": "bfdfe7dc352907fc980b868725387e98a787d08a0fd22ad3c48abdfa94357a76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a49c4f55f810835121ec6fbf668b2910", "guid": "bfdfe7dc352907fc980b868725387e98e05091d5ab1090bd219c3e890ea6a627"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844dcc059815cd2508ad5def0fa7e7ca7", "guid": "bfdfe7dc352907fc980b868725387e983ecb25c70d476ccc69894c081a43b235"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b589caa33b01eb03e10d5c8c017ddbff", "guid": "bfdfe7dc352907fc980b868725387e9896660d99417c542a3b64002268c9b030"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813d189fea04776f684730b6801b7a78a", "guid": "bfdfe7dc352907fc980b868725387e9808f7bd938e186c0e1ddf7124b4d24b36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b92dcdef14a9d9fd639406aa9bc0f8d", "guid": "bfdfe7dc352907fc980b868725387e984aefff822c3fff858140d40f35882b35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a851ab658b0eecaae8d4bdc10030e742", "guid": "bfdfe7dc352907fc980b868725387e987cab96112912b7652349bf633a071bbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b75712232a61cb6f44d15987b8694342", "guid": "bfdfe7dc352907fc980b868725387e9856215f3c46934808955e90af3785a4e5"}], "guid": "bfdfe7dc352907fc980b868725387e9812506956a26c98ed2001bf776e9f7a16", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e9888a336367add650f173eb4e72d9c2b47"}], "guid": "bfdfe7dc352907fc980b868725387e982fe1b654ab680a5b23b648702158a359", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a876f29eda22d530420bb4ad4eadb374", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e981203a398492d9a0b40aec53c3e1f411c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}