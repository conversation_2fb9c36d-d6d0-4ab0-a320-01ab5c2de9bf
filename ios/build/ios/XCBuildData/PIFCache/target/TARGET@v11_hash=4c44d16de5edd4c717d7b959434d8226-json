{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984ef1f1a12ae58bcefdb9f85c49559de6", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832f07e3611e1cd4202d5dcef2d33891e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9852bc28f2e36172eaf232cbc2dc33cb1c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe2765f0b0bca1c79efbef9097587fa", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9852bc28f2e36172eaf232cbc2dc33cb1c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984fe9281b56eef94ea17cbbe720807df4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815467cb11c4b184c514f4c9be4d4b22f", "guid": "bfdfe7dc352907fc980b868725387e984374c5929f93b58326de248a0af0e08e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988cd46bae394cf19ea3bb6fa9cf3bc30f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981a70b610e6969368abd02b328035a15e", "guid": "bfdfe7dc352907fc980b868725387e98cdb0d144103ed4143c96e504e8b1d579"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f55f387e74fd4758a2d1742fcfab3ef", "guid": "bfdfe7dc352907fc980b868725387e986b437f6e398474a4590a548a121b8069"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a248189137fe3352d524b40ab78328e", "guid": "bfdfe7dc352907fc980b868725387e98db2fefd8129138c40db11403a5bfe81d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc6185273018f1ad4fe3c82e8d68dd91", "guid": "bfdfe7dc352907fc980b868725387e98e08a6679f47a2c2b62f46551447022ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acdeebcb3442cb89ec5528f33958540d", "guid": "bfdfe7dc352907fc980b868725387e9879f81259637f407bac584aa628bbd8da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807879c19c09ff45c80efde541407871a", "guid": "bfdfe7dc352907fc980b868725387e98bd7072f7753032a76fe0641a011e8c88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853896ff5135ce1d83c9b6d2cd42a5003", "guid": "bfdfe7dc352907fc980b868725387e984e1e03417478c8590617ec85b166f463"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c128444d108f7aace74268312fc7d28", "guid": "bfdfe7dc352907fc980b868725387e9853eb335452f4015e784c5261cfebe3e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e715c3b0544509049b12f3cfa2456c4a", "guid": "bfdfe7dc352907fc980b868725387e989b9d7a484734d69adf29e7b1ce09efdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9177662c535cb93e06caf419575b7f6", "guid": "bfdfe7dc352907fc980b868725387e9858f2ab01379395b2a3dc63f605a85d87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98787a8b33c4dca05d3727765053e8dc4d", "guid": "bfdfe7dc352907fc980b868725387e98c9dd63c8be1ddfca000af528790d6dbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878046c48a861f7b4116d425287a9f75e", "guid": "bfdfe7dc352907fc980b868725387e989b792c09e20dd79f4687659e1a154d6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a72b2a095f378f53dc451ed42ded89fe", "guid": "bfdfe7dc352907fc980b868725387e985ec01d70abd150e48450f5829216829b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c0bee7705085feb4cb658222c18c694", "guid": "bfdfe7dc352907fc980b868725387e98752cad510cfdb161ec55d796415bf70e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9dcee4674171668c5e8df173a467694", "guid": "bfdfe7dc352907fc980b868725387e989e08a847cd344fbe4606da21f1fb489d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98705767f85a56b4c53d4e16ca851401c8", "guid": "bfdfe7dc352907fc980b868725387e980570c93c6891707aa7544359884adfec"}], "guid": "bfdfe7dc352907fc980b868725387e980052c51b8717f62ce2f5ff4458b396f9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98580b44084421fdbd3b6d09468cbf510b", "guid": "bfdfe7dc352907fc980b868725387e98095dc869b774ef60b122ffc4340f531d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e98e4b0cbb88ebf714845641dd3d28fafba"}], "guid": "bfdfe7dc352907fc980b868725387e982e51bd129cf78dc9a0dead1ca579d6ad", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9884aecf368c657f89e21dc1897948515b", "guid": "bfdfe7dc352907fc980b868725387e98e88453312ad1e627434f92c5923e171c"}], "guid": "bfdfe7dc352907fc980b868725387e9892a77a5c5ca2cb2880bece9778388d81", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e985c9de23706ec6ffbd0536cc5484b3896", "name": "PhoneNumberKit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f3bf2315f331e6c08a312dfd64b37cff", "name": "PhoneNumberKit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}