{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f009cb0e7b0c6ae08de4490cc725c407", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d396e8c471c666d79add58380de23c99", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984a6599bccc7cb097e31411350cc37f2a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98575a0f7ad8d28f920bd29db935efd698", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984a6599bccc7cb097e31411350cc37f2a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cc344ade34688adb481afa3719eec130", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98451639936498bfb54dcd9d8cb4e54cb0", "guid": "bfdfe7dc352907fc980b868725387e98ab6649c62f1b9c948bf57c80e2cdf02e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9819c939767a8aeddf7b8ebd61c35c0cdb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f4d09e7ce004d54ec4102750af6bf647", "guid": "bfdfe7dc352907fc980b868725387e98a056afb1b43de82efd31fae50e7701cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845a75824c12a67f46faf0ad5e96fed67", "guid": "bfdfe7dc352907fc980b868725387e9823d9af531fd5d74df20a82b2c877b69a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835d73fe4fb7b2941c555fad6b0c21eb7", "guid": "bfdfe7dc352907fc980b868725387e9881d26664e25d09da8e0eac3476d423aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888ed3c5839f98077127c13ad22d4387f", "guid": "bfdfe7dc352907fc980b868725387e988ba73fb66c4ded02069191b5207871de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98990beba9d7390d764bab7aa9e0d08e4e", "guid": "bfdfe7dc352907fc980b868725387e986ab6bb4c969dd9b30cbb8c02fd4597e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eea9ed3af6a527d15e914d16204e263d", "guid": "bfdfe7dc352907fc980b868725387e985272b7678b16180b00ac758cb8b0027d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876b3b2d5c2b07b934594038f778b8e95", "guid": "bfdfe7dc352907fc980b868725387e98348be07e150f32a33f651c5d53906423"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831c220ca0c467ec057c8d3a65337acba", "guid": "bfdfe7dc352907fc980b868725387e980a22e8cd0c5ec1184403015dc50e8db9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f62bccf178d1f76d302b3a799dd744f6", "guid": "bfdfe7dc352907fc980b868725387e9857897aced68302571537c16d21308102"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1cde3830dd770184d77ecb807ed56e8", "guid": "bfdfe7dc352907fc980b868725387e985e042ea4a7dfd1dc68d04ad1fc66ac69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817cdd93365f8ac2d75179c0d2d9849db", "guid": "bfdfe7dc352907fc980b868725387e98bff859ee2e017f34b0c50dbd176b2e3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac9be77ff3c49aba1fc3ca31f8f7a510", "guid": "bfdfe7dc352907fc980b868725387e98b4ab7b3fd10736ef2d71ffdeb2ff5419"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ead26a9897d52f72f1a595dac73eeb31", "guid": "bfdfe7dc352907fc980b868725387e98c31094354f23c3a04fe80d18aa182169"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987da59d39307b1775894d53910e95666b", "guid": "bfdfe7dc352907fc980b868725387e98e12b6bad51e36e69c21a221895994655"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98037f689ebf8c0335bdaec1693f5a70f6", "guid": "bfdfe7dc352907fc980b868725387e9801a697269ad0b34937fb19754cea8992"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0e9474cd3013f57662a6776c5e8de27", "guid": "bfdfe7dc352907fc980b868725387e98d821913921804d720fade8d38899eac7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd2d967a13d2be014fba504b6f681ca3", "guid": "bfdfe7dc352907fc980b868725387e98b6b2a58ac6b05ac84a6cbf41080f5101"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d96a1c2e8a46183de921006456a97cd3", "guid": "bfdfe7dc352907fc980b868725387e9841af3ffe2f641882e254bde0e8962e8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e65aa1b0f8c8b7b0752fd74e830fe29", "guid": "bfdfe7dc352907fc980b868725387e98af302e9db53bb0615cab391e3d5a848b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989338df7e7e075ae901a3aa68c6412879", "guid": "bfdfe7dc352907fc980b868725387e98bdb852931c74a860c57612ea447a80bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98016db84f3db2b1b273605c193bef7fb3", "guid": "bfdfe7dc352907fc980b868725387e98a69606068501a0f17a414956a5ccd2ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f73cfc183181d40a39b5052cb30c3952", "guid": "bfdfe7dc352907fc980b868725387e9884f6775e751b06e4202c68e59547995d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829a536e958baecba7bd72ba75a22c920", "guid": "bfdfe7dc352907fc980b868725387e988c5de6796cb3ea7f24cccfb5892d3397"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887f376dd7999fb061a65a932bed8153f", "guid": "bfdfe7dc352907fc980b868725387e98906b27713de9bc82bc4e756f3319214e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaba6c620b3b4e79c52f69f4ec6b8f25", "guid": "bfdfe7dc352907fc980b868725387e98cf1922ddb5c1906dbb20dcfdffd07fe3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1b32305be06b241a1e077d42b0141da", "guid": "bfdfe7dc352907fc980b868725387e98eac6c0a61eb54adbdf2d1d15c2989eea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c1ddc7b9292212989c11bc11fca0875", "guid": "bfdfe7dc352907fc980b868725387e98c851acfe91c2ad7f09e84a93769fb93d"}], "guid": "bfdfe7dc352907fc980b868725387e98bb9aa572e99c897ebd3c150e12112757", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eab614c2665412b3623d533980704485", "guid": "bfdfe7dc352907fc980b868725387e98f255babc3ab04648665cb12ea300f743"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cca1dc9714c22653cf87328d9618c0a5", "guid": "bfdfe7dc352907fc980b868725387e982afc8bf0f7fb48a8c4a318fa16ec08ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e985215c40ce11e8759b7ba0a76163e5772"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a56ee73bf2b4a9ebe8786ad0da92c10", "guid": "bfdfe7dc352907fc980b868725387e98e19d8bdb9efd87fbc739455572b34026"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b16d38f5a7d519c077fa06220df9cf84", "guid": "bfdfe7dc352907fc980b868725387e982e983e5439a341425db2d1c120ee47a6"}], "guid": "bfdfe7dc352907fc980b868725387e98087f9cd810aa530a7a91d9cecd85756e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98703f49829d4f5c0cd830a0f0780864c4", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e984f2ac221494ce5c84ab89eb7086d6144", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}