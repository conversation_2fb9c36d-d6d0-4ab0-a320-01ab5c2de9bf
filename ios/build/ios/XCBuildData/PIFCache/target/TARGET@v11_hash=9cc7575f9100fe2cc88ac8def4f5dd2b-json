{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c04742aac5142104c9406b4f19bcaea1", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Masonry/Masonry-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Masonry/Masonry-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Masonry/Masonry.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Masonry", "PRODUCT_NAME": "Masonry", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9888810d14ce2ebfbdac2ed74db0f16bd4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c15403c125be3f1bb19219832ea85ab8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Masonry/Masonry-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Masonry/Masonry-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Masonry/Masonry.modulemap", "PRODUCT_MODULE_NAME": "Masonry", "PRODUCT_NAME": "Masonry", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981f6b91e56a75fadae9c52ecc526e38bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c15403c125be3f1bb19219832ea85ab8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Masonry/Masonry-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Masonry/Masonry-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Masonry/Masonry.modulemap", "PRODUCT_MODULE_NAME": "Masonry", "PRODUCT_NAME": "Masonry", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98343b936a849d8c7f44d1edcde8e2f51b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983f488a802017ae6793b3fafcf6409724", "guid": "bfdfe7dc352907fc980b868725387e98063f7878a6c49bccfb27e0922ae80ce5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea28d2baf339139533a2f2ae7eba8acb", "guid": "bfdfe7dc352907fc980b868725387e98e7e65dc6f62275b79ed7507b00e8e01e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a16cd1ad3a3d2e356aefce65dfbf8fe", "guid": "bfdfe7dc352907fc980b868725387e9887faecc14d75f728cee3234a4dbde6c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985571814ec5326890e5c78d87da76f170", "guid": "bfdfe7dc352907fc980b868725387e98a7d9c3be4901defd8247a1e2e185df3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dac3dd49cf05340be9aa7bcdd1c3118c", "guid": "bfdfe7dc352907fc980b868725387e9879eedbf7dd76651fead8d6d3516424ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd4d262feb98e6123b5d9d1daf1d51b", "guid": "bfdfe7dc352907fc980b868725387e982539391fb360ca46df62bbcf298351aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1dcdac1444ff4acf9697b19f02d084a", "guid": "bfdfe7dc352907fc980b868725387e98c896b860e12f216e8746f2e0e1a45085", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896c8b1b9d4a95e488984012f7ffc93cc", "guid": "bfdfe7dc352907fc980b868725387e987473d9adb37cb5aaf06ad701b1a665f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c74849857e32b41349c863483b5682ed", "guid": "bfdfe7dc352907fc980b868725387e9866357b9891df691bb64006ea88522966", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e9d4cc7f9084977472bf7e1bc76bdea", "guid": "bfdfe7dc352907fc980b868725387e987c2f3cb17a71467d951892b2fc35e5f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0d1a9949b35a0c71f308d5cd9b4a835", "guid": "bfdfe7dc352907fc980b868725387e98ded8712f6a06403dbb4e1d03794bdf56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98486ed57c498f2e4815941c268bc52844", "guid": "bfdfe7dc352907fc980b868725387e988b206964c93ab6f9d934bcd78e39cea6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98725357f85c0e15e3c99e409c95e37133", "guid": "bfdfe7dc352907fc980b868725387e98f8395731d4b28568de6f82dbc88d9325", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984997bd4cad045f98e655cb1c1f5a7a62", "guid": "bfdfe7dc352907fc980b868725387e989106de51707b3dc27ab82648b1744367", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988af5b4cb0bbd8c149274389d3500010d", "guid": "bfdfe7dc352907fc980b868725387e980d6f91b37c45138e9c4ab97a6e9eef50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4a25ca7953f7efe654fbc0653768899", "guid": "bfdfe7dc352907fc980b868725387e98e1c81caa7c23341aeebe4ba11ff7d4b2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c34dd8f86c37c8a063461e3f5a02ceb7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987ac7a4d1836746ae77043269a96381ee", "guid": "bfdfe7dc352907fc980b868725387e98861feda59fd48c4ac11a6f9d1835947a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815c0df4242c6dcd294452d8615367305", "guid": "bfdfe7dc352907fc980b868725387e9878d8cdee6dfc864ab782c9f55f2c22d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813ef80b0bc39c903bfb5f1712b76e8b2", "guid": "bfdfe7dc352907fc980b868725387e98e7fb6e086b373edcc1e5bdd0fcbe403c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98626f70194926baad078649731152c576", "guid": "bfdfe7dc352907fc980b868725387e988e06492b39d05806e6515366cc8de996"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98999fbabe35e623522a2a8e44b8aa5cc9", "guid": "bfdfe7dc352907fc980b868725387e98d96b8ec7ee20c4c3a1ef1b6ebb3f44e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98981026db9b9f480c3bfb43ed0a8b834b", "guid": "bfdfe7dc352907fc980b868725387e98fba3df6c120d65c4a8ad3836c1b3b65b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832a74b1d86e1cede46f07334632efa79", "guid": "bfdfe7dc352907fc980b868725387e987dc4860f6c31f686d059e88dda952558"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98985459bedcae212041d533248bb063b9", "guid": "bfdfe7dc352907fc980b868725387e987c9fb2a32249b05d82c545b8dac7d191"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b710ca08440af9538f37510fc4b66c8a", "guid": "bfdfe7dc352907fc980b868725387e981d14c954b00492e9492f9988551184e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d5703880a7932ff0505d4b74b684f95", "guid": "bfdfe7dc352907fc980b868725387e98c1406d7535b15403efef52291afea1c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c7afcf07d51ee67e75a3ca9b8d4311a", "guid": "bfdfe7dc352907fc980b868725387e98e5f8490a66213e94add9293732fd7069"}], "guid": "bfdfe7dc352907fc980b868725387e98de026a844c120ccdb67a7627808be9f7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e984f70dbefd0e25e7d5404facd1b065aa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b16d38f5a7d519c077fa06220df9cf84", "guid": "bfdfe7dc352907fc980b868725387e9895d8e5ce56eeadfa14b4a992750ad64e"}], "guid": "bfdfe7dc352907fc980b868725387e9852ed3f8ac9dee5a23e6a443699923a69", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b05126febce7785b32f28b42780cf931", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98e3be389a010182c334a5c542b48f8dc5", "name": "Masonry", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98df6836b9870d920dfe4f848ea264a4aa", "name": "Masonry.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}