{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9829e07dc0c39a74510d1774114313291a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983676733518e948b360b585de2b65ffb1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a48f877f0054a3c4e9a0c5f7291e229b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827de5d7882acfa8dac4e1d9448d990b8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a48f877f0054a3c4e9a0c5f7291e229b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fd9a8e5e6b72665d7607be147f2d32a2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f20fa462dfb35f8a9988925d7472f3db", "guid": "bfdfe7dc352907fc980b868725387e9870b88727ac7e22fdbb98f02712ee1f8d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983475db3ebb962a4e2cb787a97de58f11", "guid": "bfdfe7dc352907fc980b868725387e98d7e58df6c5f59682342118f4ba094948", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3c14794658a112b0f151cfb942869d6", "guid": "bfdfe7dc352907fc980b868725387e983c62550ddeafcea4f9b62f055098c6f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888f60419ed9dbd47f9bd1459aeb7b9d2", "guid": "bfdfe7dc352907fc980b868725387e98fbd15e58aa819b6f21c3351a77aaa25a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800f660c18070e9e490e2521a30312253", "guid": "bfdfe7dc352907fc980b868725387e98ad88366520967773d964b970ae2d8317", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98407ab437bbf51c9e7fc68031f42c7d89", "guid": "bfdfe7dc352907fc980b868725387e9841ca73bd50f4cead0d5cd9160cff622f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b04eeabf2867905bf62aca8535077f94", "guid": "bfdfe7dc352907fc980b868725387e985dd4773be22587036702a6a5f8fbd88a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d58a047c578d452c6ea17c3a2880daf", "guid": "bfdfe7dc352907fc980b868725387e986526f611349aed36ea5a94fa065b93d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987902143038ddc900120bd041ef3c67b2", "guid": "bfdfe7dc352907fc980b868725387e980eb991e13503117fb7b7138153a5285c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfda116aa8ce198d1f148cfe2ebe2fd3", "guid": "bfdfe7dc352907fc980b868725387e98559e3618d7b7dd5b7fa5ba56acab6c78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b787c4e8d3ae4f7764bfd31c7ea7126", "guid": "bfdfe7dc352907fc980b868725387e98b93e93b3b0d1cadfce3757ce4e26051b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a68ee4ed468f4859efe96a4d07ee8aec", "guid": "bfdfe7dc352907fc980b868725387e983362e053f08b145a77672d7c45f05390", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac745cac94d55d08dd0ddd744623e3ef", "guid": "bfdfe7dc352907fc980b868725387e980225763c9c7dd5230de5371e2aab288e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eaef856360971b95a8b377009eecc81", "guid": "bfdfe7dc352907fc980b868725387e98e031e38c59acb09896d7712655e8fe40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d14827b83988d6806312a48b30837dbc", "guid": "bfdfe7dc352907fc980b868725387e980817119aab3cff80973950e400dc6be9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b29020935d6236286f2ac0eb75805316", "guid": "bfdfe7dc352907fc980b868725387e9870b1e947624a039f4d5617e6a6846987", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981af1b00118387a0aba290c6218789848", "guid": "bfdfe7dc352907fc980b868725387e987c47e024c957111786578b92fccc28fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813d818f8cbc9f798b72b6a2007a4ef1f", "guid": "bfdfe7dc352907fc980b868725387e98c56959019855a931232624bf56d4ba85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f95514dbcc13a7b61c8b445c247d69a", "guid": "bfdfe7dc352907fc980b868725387e98cd050b40efe4057debbf2a74ffa54875", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985295f1b13f2fd39c388cbdfcfcd043e9", "guid": "bfdfe7dc352907fc980b868725387e98eddeb21162784f26742e35a62eb3da8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985128a905dbd2a8c38667ed502d3011b4", "guid": "bfdfe7dc352907fc980b868725387e98b1a23e84b92286f49efec5c08210832b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849b7188c18bd930c42506eb0f476e42a", "guid": "bfdfe7dc352907fc980b868725387e983022b87b6fe1e77a0723d169f090bf90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f22e1e32911154df2a5cc04d4742b446", "guid": "bfdfe7dc352907fc980b868725387e986b9af8f952415063d7fa388d52a72a45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835069f86835c2fcf3d026a0752d19fbc", "guid": "bfdfe7dc352907fc980b868725387e98e3c8e33fb518c22b35387dd4bf871385"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98791fd3bf5788be36afbf6e5961144a4b", "guid": "bfdfe7dc352907fc980b868725387e982e83033683588f242fe4bebc141d1ceb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814c78c92391424f8b89eff208a299274", "guid": "bfdfe7dc352907fc980b868725387e98f108bbcd06262b8dc4a6f4da31057a71"}], "guid": "bfdfe7dc352907fc980b868725387e98d53539bfd07daa80eb26ea7234fe2218", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c670d98389553f8e172bceec4ce9c45f", "guid": "bfdfe7dc352907fc980b868725387e98d11a71dd3ad268f15d37856582eb9329"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828975637214a54410b35283585c76d1a", "guid": "bfdfe7dc352907fc980b868725387e98ff57b6457f064f0aa61327240c1dbb12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b52c53e30eaec625b4b53cfe434fa135", "guid": "bfdfe7dc352907fc980b868725387e98a4ea989bc7ec090038e2a66461944391"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6ccee7dbf9f80984e196f4a6b586527", "guid": "bfdfe7dc352907fc980b868725387e98f72f23c93b7b0961ce4721d5f64f3452"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d54997a33103181281645b1bbb5d994", "guid": "bfdfe7dc352907fc980b868725387e98c8bda150e506082dd3616acc6c81caf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1ae6a96f47842cfd210df6620c7bfea", "guid": "bfdfe7dc352907fc980b868725387e9879e96d1f83d4e41e1a6de5a1d31085d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff99ec6cc7f1ee885aa90c1f993353dc", "guid": "bfdfe7dc352907fc980b868725387e98c95119726ea02a3234a0a1b69c10fe22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eff8895fd57034b159b8b961dde05ce2", "guid": "bfdfe7dc352907fc980b868725387e987b30bc332be75fa0bca89fa0f0f821c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983db0205e34864c8bf4fd178d222e81a8", "guid": "bfdfe7dc352907fc980b868725387e980892e4b401ee3442f023b7f9a483228d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987657f01877d2d922fba7f75c21ca2aae", "guid": "bfdfe7dc352907fc980b868725387e984c64c2e0ebec4ceb85e173c172d07901"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c645f11f34d1cbf451de9c558b33cd15", "guid": "bfdfe7dc352907fc980b868725387e9885220e4dd0ddd056dfd6bbacae9495c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de977c6a67ef36d2c1842b5e55121d2d", "guid": "bfdfe7dc352907fc980b868725387e9898d7979f5513e871d12c3c09dc4b29c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f633a97255999c475d6e00843ecc25f", "guid": "bfdfe7dc352907fc980b868725387e98c90809727640928844ee28189045eca5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806de0deb7d556a6f621c2c649bcec52c", "guid": "bfdfe7dc352907fc980b868725387e983a6407f49d23783d0d7674033dd8f61b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9186fb732db4646fd82e30b53dc151b", "guid": "bfdfe7dc352907fc980b868725387e98c8ab07a3377ebe4853820136a82a8513"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffcb033755f6b492dbda908415086359", "guid": "bfdfe7dc352907fc980b868725387e98ea97b3d1299dc22c9e06d3690876ea6f"}], "guid": "bfdfe7dc352907fc980b868725387e980cd9d2f821c618b9c67ed60e0f2405c9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e98a8973ce36a4b7429b13f06ab1f43d1ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f6815db8b12f96ce7ef9e16aa9f5816", "guid": "bfdfe7dc352907fc980b868725387e98e5ba3a645056472c9a868bf0f82b8864"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98875e206812b734f1cf349363e49beb99", "guid": "bfdfe7dc352907fc980b868725387e9889d606d20d12f1f04ac6f3e6a37f1f3c"}], "guid": "bfdfe7dc352907fc980b868725387e98027d7236edb628b475a46b1a26b7b9ff", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988190ad299508a42355e474e660943eb2", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98c5ed545e521c2bfb9086de2b1b4cf714", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}