{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9896dddc4a0172503f565d9d774069283e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/ZIPFoundation/ZIPFoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/ZIPFoundation/ZIPFoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/ZIPFoundation/ZIPFoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "ZIPFoundation", "PRODUCT_NAME": "ZIPFoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e4ba4e08f53dd833f6c61b8d90199b58", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9870795916c52f20fbdef35c57ce718b3d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/ZIPFoundation/ZIPFoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/ZIPFoundation/ZIPFoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/ZIPFoundation/ZIPFoundation.modulemap", "PRODUCT_MODULE_NAME": "ZIPFoundation", "PRODUCT_NAME": "ZIPFoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e1e374476e7fcfd1446041e1822e4596", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9870795916c52f20fbdef35c57ce718b3d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/ZIPFoundation/ZIPFoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/ZIPFoundation/ZIPFoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/ZIPFoundation/ZIPFoundation.modulemap", "PRODUCT_MODULE_NAME": "ZIPFoundation", "PRODUCT_NAME": "ZIPFoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e02c5f953916bb3cd2470d195ccad9eb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bf162550f836bf082c9bb4eba9a316e3", "guid": "bfdfe7dc352907fc980b868725387e98a1d195e11039fa00118406904f14bdb1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988fdbd66e1e3281962d212a29335e0ff7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989f7a21cdba290b80133281cfe384f1d7", "guid": "bfdfe7dc352907fc980b868725387e9872546fe702a16eb96b7fb4d097f6acdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d89592f5568b39a9fdcae855647bf86", "guid": "bfdfe7dc352907fc980b868725387e98bf2c00a1c97dfb1bb4969a376cb965ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a827da2c7b47cabb2af962ffa23c2756", "guid": "bfdfe7dc352907fc980b868725387e988bd836c9d15734627fbd295ffbebc36c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98702a895ccc5cd45ff6bf8790a132b9a5", "guid": "bfdfe7dc352907fc980b868725387e98a09b4a9700ed41332e0cdd4b417d5d56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bff92eeec8989f20728de2386baa26ed", "guid": "bfdfe7dc352907fc980b868725387e9855cfbf17db885209430a8bd943746a03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb35188fad375f3a5eb7a3e4f9017099", "guid": "bfdfe7dc352907fc980b868725387e981192818ef1fd6ab2d787938a0bcddd2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98506ad481b7bef1ca1417749bb6fff424", "guid": "bfdfe7dc352907fc980b868725387e9834cb239007ca9b99b6b7003a59913d08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c67a60223ec889b0e40b7756a240c85", "guid": "bfdfe7dc352907fc980b868725387e98210f42295f282178dd6af4455b7f41c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2da2824409510679cb984fc37c071c6", "guid": "bfdfe7dc352907fc980b868725387e98cb61f33afb2c782918ace5a9527b0bb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9ba182b4fc97e92d7d917a148bc1a12", "guid": "bfdfe7dc352907fc980b868725387e98e82dc48adb0a58f15d7b6a1b8ed1875c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ad1d66d6003b5f54eb1ac17cdbe71eb", "guid": "bfdfe7dc352907fc980b868725387e98c3f4d46c316bb68106704b67b57e2673"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98496b75ed93d91809e7cc3e83cc1f2149", "guid": "bfdfe7dc352907fc980b868725387e98a947f51ccdeb591081307cd27c56b535"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98722ddc111c852eba1adec16a6d6469a9", "guid": "bfdfe7dc352907fc980b868725387e981d9db104c09854f9d26c5f58f9535e5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824567eae527bebe4922c71fc25bbb726", "guid": "bfdfe7dc352907fc980b868725387e98c3ee983800589d3d8439c31abdb8ddae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e9a86ae56acb8ce6ddaf7610d3ed5e7", "guid": "bfdfe7dc352907fc980b868725387e9820ca77789f064cf3a52de5d4b0b55677"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a62fdbc310b6891fd487c0fc8c9018db", "guid": "bfdfe7dc352907fc980b868725387e981d2c091a5bcaba33328aab5c223c5e2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8c336d1d83fe9b25351318e4123b761", "guid": "bfdfe7dc352907fc980b868725387e98ca2e14fc4e8c586a8ddf2e7351d866b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8a90a983706ec9c806f77f142e9eab4", "guid": "bfdfe7dc352907fc980b868725387e988235a2ecb2960a6e6d549e2b9f7595e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c4a2eaa5c92e4405e7ab6b48631ab1c", "guid": "bfdfe7dc352907fc980b868725387e98eac2d8e0c62e168d8d8a6d7690e124ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827e90825f78d96fa6d3f415a604ab4e2", "guid": "bfdfe7dc352907fc980b868725387e984cf77757aecbe46f2da54e8c103fdfe3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841eebc283fccd57656e8d38076016b61", "guid": "bfdfe7dc352907fc980b868725387e98ffdd067267439534b1aa377aa23c658c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e718d54ad5e4d19e40cf864bf374133b", "guid": "bfdfe7dc352907fc980b868725387e982f590802c81c4d96958a365dde7bcbad"}], "guid": "bfdfe7dc352907fc980b868725387e98b331bbe863c0faabdfac3bcdbd77c535", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e9826f00ba86619ace8f73f28fe829cec32"}], "guid": "bfdfe7dc352907fc980b868725387e9899f45b158102128f583d4d953d8a8f48", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982631cb71a5f5df5392acf4709c1cdc0e", "targetReference": "bfdfe7dc352907fc980b868725387e98d5f04766c1f13418203923b4c884e6a9"}], "guid": "bfdfe7dc352907fc980b868725387e98f9c03c7b55282746db4cf63138f940d5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d5f04766c1f13418203923b4c884e6a9", "name": "ZIPFoundation-ZIPFoundation_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9880d1b2aab04aef5eb91394017eba5f52", "name": "ZIPFoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a444602dfcae48e5c9fe80206c5b7c8c", "name": "ZIPFoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}