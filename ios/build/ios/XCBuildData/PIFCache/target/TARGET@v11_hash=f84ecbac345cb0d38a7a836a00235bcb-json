{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980b86aa66c1ed6b332dbf6cb93edf2ddd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f5d40037d0f76ffd2c83c4e6439bac1d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d7a00568ff353353adf94852960d65fc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989ae5ae7029609bf8ca2fdec8f37b8247", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d7a00568ff353353adf94852960d65fc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986d571714f9859f8bd5d256d878adec7f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ff0a767e14267a4770f9b492da12d8c0", "guid": "bfdfe7dc352907fc980b868725387e98c2cbe7d82b144120cb5964f1b1e47c0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98927446aa006e305fbe8e940a692b7a68", "guid": "bfdfe7dc352907fc980b868725387e981be99c66cae5ffbdbe3399c2a4f83447", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1483cc2727201e576586964a4c8eb29", "guid": "bfdfe7dc352907fc980b868725387e985fd707c598de1eb3970c25feddc60ce0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b055a08de3d8b3b6b366b6f5c4081454", "guid": "bfdfe7dc352907fc980b868725387e981317422b950dc5b2f206b815a3003030"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817e9eb4724c1d93ac7d7f99e9f509be0", "guid": "bfdfe7dc352907fc980b868725387e98bcb2e894ba23afd348afe1b4cb590121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981655d570af5af7ac831fae26ea7a36e1", "guid": "bfdfe7dc352907fc980b868725387e98f9804acfc2eb0204a07a12cbc9386e24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816f3b49d91f1762445b852350482c82e", "guid": "bfdfe7dc352907fc980b868725387e98a1279f4a62dbbfaa826be5c71d533499"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db803c4769032e2dcd31d4a7891224ed", "guid": "bfdfe7dc352907fc980b868725387e9800a01df11c78926ff26b950eb5d22778"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f9efe4b1c26ed2880ddb92699d49615", "guid": "bfdfe7dc352907fc980b868725387e9865867b66aa3b74bba05992b00c650d54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b8f69e34a4c774465ca8d6647292e95", "guid": "bfdfe7dc352907fc980b868725387e982b7744ab65046b8a8051d7faada83c0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985de16e82c5aef0f852fa071e50663a89", "guid": "bfdfe7dc352907fc980b868725387e981e56f783629b4b9ad9cbd169a02cc587", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4c67e9d1a2a090749056a6af131f066", "guid": "bfdfe7dc352907fc980b868725387e981b28d5dfb7310212180af6a698be311d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985002d26e4bb6d13b2b71f75e8439a640", "guid": "bfdfe7dc352907fc980b868725387e9835d87b59433586509f58b4e32219cddf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891615d6b423b248f6ee50f0f204ca173", "guid": "bfdfe7dc352907fc980b868725387e983f12540e857ac6fa68a579fd35984b74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c590e2acd138f21e16ecd91a5d8b4bc8", "guid": "bfdfe7dc352907fc980b868725387e9886e236f04502902cde044ba4db7df8ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a09aac32748440f53f6881e52793f744", "guid": "bfdfe7dc352907fc980b868725387e9873b21ba92dabf69c14d82fd2cbb394aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b34d3049ec32a50c5d15e4e73ad193b", "guid": "bfdfe7dc352907fc980b868725387e98dcb35fd7c389301c5ed3da4bec7b6ea3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98673a94b3ee3a2f56a3f582c128769813", "guid": "bfdfe7dc352907fc980b868725387e982c76b4d10c7ca6ce5d932b69029ed3fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e988f0791455869de629286ec610ab8", "guid": "bfdfe7dc352907fc980b868725387e986a91d1b3fc17ec2aca313bf46cb532c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fafdba5e330147d40438e66adb9eac03", "guid": "bfdfe7dc352907fc980b868725387e98b1775841a4cf3a98e1f4c35f2bbea1bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982af980298eb4f83effd4bbbb8215622e", "guid": "bfdfe7dc352907fc980b868725387e98813cb8911abec4fc37abf3eaed8a9f4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b05ed18b0e22fd93ade4c66123c368c1", "guid": "bfdfe7dc352907fc980b868725387e98dc14f346ce22abbd8ae4dad0770f8f92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7f231dd0a3c009dd0bcb68d3662ec6a", "guid": "bfdfe7dc352907fc980b868725387e985c08ef0900910de3e6374a4158e2c866", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98485d86f412f850d13bec7b225a652cc8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981746f2fce60945ee43e1736c3ff0e50a", "guid": "bfdfe7dc352907fc980b868725387e9823dc7de3a1734252da6ca9dd500da051"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831688e4e7bd99b6b43fa62e61705b350", "guid": "bfdfe7dc352907fc980b868725387e98db5b0e27402452991ea55136b11fca8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c24b1502a01541a69e56a1bb39f7b5f2", "guid": "bfdfe7dc352907fc980b868725387e9813256d93d1ab5474d5b47a3b836a4b6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fd7fe9c358763b99a366e80d7498e5f", "guid": "bfdfe7dc352907fc980b868725387e988580676dff76c5c69f88c38e59c996ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d5641edecdb785d1494f579f7f0868a", "guid": "bfdfe7dc352907fc980b868725387e989249cf139dac6a45fe099e3cdc20711d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98486a78fe84023026b2eec8bb291858ff", "guid": "bfdfe7dc352907fc980b868725387e98cb568aec2a5ae0b5e88724a375230765"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98021ce8f7e0f1e9e55f56be185a7dd7be", "guid": "bfdfe7dc352907fc980b868725387e98b91046c0e02ab1026b390645da50408c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f45dfa49234b87a10896192bb6e9ce74", "guid": "bfdfe7dc352907fc980b868725387e98ed63be6f96dec4c332c47fc2d3390453"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b93e2714bda63b726365a6ec3294dae", "guid": "bfdfe7dc352907fc980b868725387e986bfaf44f992d0ed1d1856f9648f705d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983286ba0d7a006a7734797c9988e61410", "guid": "bfdfe7dc352907fc980b868725387e983ba1ceae53cd3249699b43c168a5422c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832891532861e46a6ddcf6476af1f40c3", "guid": "bfdfe7dc352907fc980b868725387e98231c2e0ae96d0086f767d84a304fada9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896e33574a58a43daf418108a4b535fa3", "guid": "bfdfe7dc352907fc980b868725387e98ec46ef10e97baca96d7c7189f2bf6e85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d84d09e3f7b11087dc98d6ac6cc8917", "guid": "bfdfe7dc352907fc980b868725387e98154865c562a581e0e6444f00b42d7a1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e358551171ccf5821dba56e7b4a75e88", "guid": "bfdfe7dc352907fc980b868725387e98f12023a11575a160007823b09bf831b8"}], "guid": "bfdfe7dc352907fc980b868725387e981bfce310f7569b620966f4d4b97baa7b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e98080131bcb5d9dedf4ba7743d0ed2d7ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b16d38f5a7d519c077fa06220df9cf84", "guid": "bfdfe7dc352907fc980b868725387e982c05949e08a3a62c8071a786f0c84923"}], "guid": "bfdfe7dc352907fc980b868725387e9834e431cd6e65971a6c4a89f865680969", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982c8600326c4971ea4399595f0f9c8b81", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98134d8e883ab5d844d7f876dbc8f9e2b4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}