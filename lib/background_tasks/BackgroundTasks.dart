import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:rooo_driver/background_tasks/LocalNotifications.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:workmanager/workmanager.dart';

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await BackgroundTasks.handleBackgroundRide(notification: message);
  await BackgroundTasks.updateDriverStatus(message);
}

@pragma('vm:entry-point')
Future<bool> backgroundDriverStatusManager(String task, Map<String, dynamic>? inputData) async {
  print("Background task started: $task");
  try {
    // Check if driver is online before making API call
    var instance = await SharedPreferences.getInstance();
    bool isDriverOnline = instance.getBool('IS_DRIVER_ONLINE') ?? false;

    if (!isDriverOnline) {
      print("Driver is offline, skipping heartbeat");
      return true;
    }

    await BackgroundTasks.updateDriverStatus2();
    print("Background heartbeat completed successfully");
    return true;
  } catch (e) {
    print("Background task error: $e");
    return false;
  }
}

abstract class BackgroundTasks {
  static const NEW_RIDE_KEY = "BACKGROUND_NEW_RIDE";
  static const NEW_RIDE_TIME_KEY = "BACKGROUND_NEW_RIDE_TIME";
  static const MAX_SECONDS = 30;

  static Future<void> handleBackgroundRide({
    required RemoteMessage notification,
  }) async {
    /* check type */
    if (notification.data['type'] != NotificationType.new_ride_requested.name) {
      return;
    }
    /* prepare new ride */
    OnRideRequest ride = OnRideRequest();
    ride.id = 1;

    /* save to shared pref */
    var instance = await SharedPreferences.getInstance();

    /* data futures */
    List<Future<bool>> futures = [
      instance
          .setString(NEW_RIDE_KEY, jsonEncode(notification.data))
          .then((value) => value)
          .catchError((onError) => false),
      instance
          .setString(NEW_RIDE_TIME_KEY, DateTime.now().toString())
          .then((value) => value)
          .catchError((onError) => false),
    ];
    bool isSaved = await Future.wait(futures, eagerError: true)
        .then((value) {
          return value[0] && value[1];
        })
        .catchError((onError) => false);
    if (isSaved) {
      await LocalNotifications.showNewRideNotification(notification);
    }
  }

  static Future<BackgroundRideData> getBackgroundRide() async {
    var instance = sharedPref;
    await sharedPref.reload();
    var savedRide = instance.getString(NEW_RIDE_KEY);
    var ride = savedRide != null
        ? OnRideRequest.fromJson(jsonDecode(savedRide))
        : null;
    String? savedTime = instance.getString(NEW_RIDE_TIME_KEY);
    bool isValidRide = false;
    int remainingSeconds = 0;
    if (savedTime != null) {
      /* valid if the saved time is not greater than 30 seconds from the current time */
      remainingSeconds = DateTime.now()
          .difference(DateTime.parse(savedTime))
          .inSeconds;
      isValidRide = remainingSeconds <= MAX_SECONDS;
    }
    instance.remove(NEW_RIDE_KEY);
    instance.remove(NEW_RIDE_TIME_KEY);

    return BackgroundRideData(
      ride: isValidRide ? ride : null,
      seconds: MAX_SECONDS - remainingSeconds,
    );
  }

  static Future<void> handleNewRideDataNotification(
    RemoteMessage message,
  ) async {
    if (message.data['type'] == NotificationType.new_ride_requested.name) {
      await BlocProvider.of<RideFlowCubit>(
        navigatorKey.currentContext!,
      ).newRiderAlertEmitter(
        onRideRequest: OnRideRequest.fromJson(message.data),
      );
    }
  }

  static Future<void> updateDriverStatus(RemoteMessage message) async {
    if (!GlobalState.isDriverOnline &&
        message.data['type'] != NotificationType.update_driver_status.name) {
      return;
    }
    var instance = await SharedPreferences.getInstance();
    String token = instance.getString(TOKEN) ?? "";
    String driverId = (instance.getInt(USER_ID) ?? "").toString();
    await updateDriverOnlineStatus(driverId, token);
  }

  static Future<void> updateDriverStatus2() async {
    if (!GlobalState.isDriverOnline) {
      return;
    }
    var instance = await SharedPreferences.getInstance();
    String token = instance.getString(TOKEN) ?? "";
    String driverId = (instance.getInt(USER_ID) ?? "").toString();
    await updateDriverOnlineStatus(driverId, token);
  }
}

class BackgroundRideData {
  OnRideRequest? ride;
  int seconds;

  BackgroundRideData({required this.ride, required this.seconds});
}
