import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

abstract class LocalNotifications {
  static List<int> newRidesIds = [];

  static final FlutterLocalNotificationsPlugin instance =
      FlutterLocalNotificationsPlugin()..initialize(
        const InitializationSettings(
          // android: AndroidInitializationSettings('@drawable/ic_launcher',
          android: AndroidInitializationSettings(
            '@drawable/ic_stat_onesignal_default',
          ),
          iOS: DarwinInitializationSettings(),
        ),
      );

  static const AndroidNotificationDetails androidNotificationDetails =
      AndroidNotificationDetails(
        'new_ride_channel',
        'New Ride',
        channelDescription: 'Get new ride request alerts',
        importance: Importance.max,
        priority: Priority.max,
        sound: RawResourceAndroidNotificationSound('new_ride'),
        playSound: true,
      );
  static const DarwinNotificationDetails iosNotificationDetails =
      DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: false,
        presentSound: true,
        sound: "new_ride.wav",
      );

  static Future<void> showNewRideNotification(
    RemoteMessage notification,
  ) async {
    int rideId = int.parse(notification.data['id'].toString());
    await instance.show(
      rideId,
      'New Ride',
      'You have a new ride request',
      NotificationDetails(
        android: androidNotificationDetails,
        iOS: iosNotificationDetails,
      ),
    );
    newRidesIds.add(rideId);
  }

  static Future<void> cancelNewRideNotifications() async {
    newRidesIds.forEach((element) {
      instance.cancel(element);
    });
    newRidesIds.clear();
  }

  static Future<void> showSimpleRideNotification(
    int rideId, {
    String? title,
    String? body,
  }) async {
    newRidesIds.add(rideId);
    await instance.show(
      rideId,
      title ?? "New Ride",
      body ?? "You have a new ride request",
      NotificationDetails(
        android: androidNotificationDetails,
        iOS: iosNotificationDetails,
      ),
    );
  }
}
