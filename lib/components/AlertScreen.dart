import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:rooo_driver/features/emergency_contacts/screens/emergency_contact_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:url_launcher/url_launcher.dart';

import '../model/ContactNumberListModel.dart';

class AlertScreen extends StatefulWidget {
  final int? rideId;
  final int? regionId;

  AlertScreen({this.rideId, this.regionId});

  @override
  AlertScreenState createState() => AlertScreenState();
}

class AlertScreenState extends State<AlertScreen> {
  List<ContactModel> sosListData = [];
  LatLng? sourceLocation;

  bool sendNotification = false;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    getCurrentUserLocation();
    appStore.setLoading(true);
    await getSosList(regionId: widget.regionId).then((value) {
      appStore.setLoading(false);

      sosListData.addAll(value.data!);
      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);
    });
  }

  Future<void> getCurrentUserLocation() async {
    final geoPosition = await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
      forceAndroidLocationManager: true,
    );
    setState(() {
      sourceLocation = LatLng(geoPosition.latitude, geoPosition.longitude);
    });
  }

  Future<void> adminSosNotify() async {
    sendNotification = false;
    setState(() {
      appStore.setLoading(true);
    });
    Map req = {
      "ride_request_id": widget.rideId,
      "latitude": sourceLocation!.latitude,
      "longitude": sourceLocation!.longitude,
    };
    await adminNotify(request: req).then((value) {
      sendNotification = true;
      setState(() {
        appStore.setLoading(false);
      });
    }).catchError((error) {
      setState(() {
        appStore.setLoading(false);
      });

    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: BorderRadius.circular(12),
      clipBehavior: Clip.antiAlias,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(0),
                        topRight: Radius.circular(0))),
                padding: EdgeInsets.symmetric(vertical: 20, horizontal: 16),
                width: double.infinity,
                // color: dangerColor,
                child: CircleAvatar(
                    radius: 20,
                    backgroundColor: Colors.black,
                    child: Icon(Icons.warning_amber,
                        color: Colors.white, size: 40)),
              ),
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                ),
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(language.useInCaseOfEmergency,
                        style: TextStyle(color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                        )),
                        height5,
                    SizedBox(
                      height: 35,
                      child: AppButton(
                          icon: Icon(
                            Icons.phone,
                            size: 20,
                          ),
                          text: "Call 000",
                          onPressed: () async {
                            launchUrl(
                              Uri.parse('tel: 000'),
                              mode: LaunchMode.externalApplication,
                            );
                          }),
                    ),
                    SizedBox(height: 10),
                    sourceLocation == null
                        ? const SizedBox()
                        : InkWell(
                            onTap: () {
                              adminSosNotify();
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(language.notifyAdmin,
                                        style: TextStyle(color: Colors.black,
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,

                                        )),
                                    if (sendNotification) SizedBox(height: 2),
                                    if (sendNotification)
                                      Text(language.notifiedSuccessfully,
                                          style: TextStyle(
                                              color: AppColors.greenColor,
                                              fontSize: 12,
                                              )),
                                  ],
                                ),
                                Container(
                                  padding: EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                      border: Border.all(),
                                      color: Colors.white,
                                      shape: BoxShape.circle),
                                  child: Icon(
                                    Icons.notification_add_outlined,
                                    color: AppColors.primaryBlackColor,
                                    size: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                    Divider(),
                    inkWellWidget(
                      onTap: () {
                        closeScreen(context);
                        GlobalMethods.pushScreen(
                            context: context,
                            screen: EmergencyContactScreen(),
                            screenIdentifier:
                                ScreenIdentifier.EmergencyContactScreen);
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                              language.AddTxt + " " + language.emergencyContact,
                              style: TextStyle(color: Colors.black,fontSize: 14,fontWeight: FontWeight.bold,)),
                          Container(
                            padding: EdgeInsets.all(8),
                            decoration: BoxDecoration(
                                border: Border.all(),
                                color: Colors.white,
                                shape: BoxShape.circle),
                            child: Icon(
                              Icons.add,
                              color: AppColors.primaryBlackColor,
                              size: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  'Emergency Contacts',
                  style: boldTextStyle(size: 16),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: BouncingScrollPhysics(),
                    itemCount: sosListData.isEmpty ? 1 : sosListData.length,
                    separatorBuilder: (_, index) => Divider(),
                    itemBuilder: (_, index) {
                      if (sosListData.isEmpty) {
                        return Padding(
                          padding: const EdgeInsets.only(top: 16),
                          child: Center(
                            child: Text(
                              "No emergency contacts saved yet",
                              style: primaryTextStyle(color: Colors.grey),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        );
                      }

                      return Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(8),
                          onTap: () {
                            launchUrl(
                                Uri.parse(
                                    'tel:${sosListData[index].contactNumber}'),
                                mode: LaunchMode.externalApplication);
                          },
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      sosListData[index].title.validate(),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(fontSize: 14),
                                    ),
                                    const SizedBox(height: 6),
                                    Text(
                                      sosListData[index].contactNumber.validate(),
                                      style: TextStyle(

                                      fontSize: 10,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.green.withOpacity(0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.call,
                                  color: Colors.green,
                                  size: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
          Visibility(
            visible: appStore.isLoading,
            child: Container(
              color: Colors.black.withOpacity(0.3),
              child: Center(
                child: AppLoader(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
