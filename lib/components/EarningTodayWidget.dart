import 'package:flutter/material.dart';

import '../main.dart';
import '../network/RestApis.dart';
import '../utils/Common.dart';
import '../utils/Extensions/app_common.dart';

class EarningTodayWidget extends StatefulWidget {
  @override
  EarningTodayWidgetState createState() => EarningTodayWidgetState();
}

class EarningTodayWidgetState extends State<EarningTodayWidget> {
  num totalRide = 0;
  num totalReferrals = 0;
  num TotalEarnings = 0;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    appStore.setLoading(true);
    Map req = {
      "type": "today",
    };
    await earningList(req: req).then((value) {
      appStore.setLoading(false);

      totalRide = value.totalRideFare ?? 0.0;
      totalReferrals = value.totalOfferReferral ?? 0.0;
      TotalEarnings = value.totalEarnings ?? 0.0;

      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);

    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SingleChildScrollView(
          padding: EdgeInsets.only(top: 32, bottom: 16, right: 16, left: 16),
          child: Column(
            children: [
              Text('${formatDate('${DateTime.now()}')}', style: TextStyle()),
              SizedBox(height: 16),
              SizedBox(height: 16),
              earningText(title: language.totalRide, amount: totalRide),
              SizedBox(height: 16),
              earningText(
                  title: language.totalReferralsTxt, amount: totalReferrals),
              SizedBox(height: 16),
              Divider(),
              earningText(title: language.totalEarning, amount: TotalEarnings),
              Divider(),
              SizedBox(height: 16),
            ],
          ),
        ),
        Visibility(
          visible: appStore.isLoading,
          child: loaderWidget(),
        )
      ],
    );
  }
}
