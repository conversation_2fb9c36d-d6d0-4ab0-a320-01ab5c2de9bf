import 'package:rooo_driver/global/models/UserDetailModel.dart';
import 'package:rooo_driver/global/models/ride_model.dart';
import 'package:rooo_driver/global/widgets/app_button.dart';
import 'package:rooo_driver/screens/dashboard/widgets/rider_contact_widget.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:rooo_driver/utils/Extensions/app_common.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';
import 'package:flutter_vector_icons/flutter_vector_icons.dart';

class OpportunityRideCard extends StatefulWidget {
  final String? datetime;
  final String? text;
  final String startAddress;
  final String endAddress;
  final bool isButton;
  final void Function()? onPressed;
  final void Function()? onCancel;
  final String serviceName;
  final String riderName;
  final num riderRating;
  final String? bookingTime;
  const OpportunityRideCard({
    super.key,
    required this.text,
    required this.datetime,
    required this.endAddress,
    required this.startAddress,
    required this.isButton,
    this.onPressed,
    this.onCancel,
    required this.riderName,
    required this.riderRating,
    required this.serviceName,
    required this.bookingTime,
  });

  @override
  State<OpportunityRideCard> createState() => _OpportunityRideCardState();
}

class _OpportunityRideCardState extends State<OpportunityRideCard> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Align(alignment: Alignment.center),
        Column(crossAxisAlignment: CrossAxisAlignment.start, children: []),
        SizedBox(height: 8),
        Container(
          padding: EdgeInsets.only(top: 8, bottom: 8),
          margin: EdgeInsets.only(top: 8, bottom: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.white),
            borderRadius: BorderRadius.circular(defaultRadius),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.4),
                blurRadius: 10,
                spreadRadius: 0,
                offset: Offset(0.0, 0.0),
              ),
            ],
          ),
          child: Container(
            padding: EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(Ionicons.calendar, size: 16),
                        SizedBox(width: 8),
                        Padding(
                          padding: EdgeInsets.only(top: 2),
                          child: Text(
                            "Pickup time: " +
                                '${formatDate(widget.datetime.toString(), showTime: true)}',
                            style: TextStyle(fontSize: 14),
                          ),
                        ),
                      ],
                    ),

                    // Text('${language.lblRide} #${data.id}',
                    //     style: IS_DARKMODE
                    //         ? TextStyle(
                    //             color: Colors.black, fontSize: 14)
                    //         : boldTextStyle(size: 14)),
                  ],
                ),
                Row(
                  children: [
                    Icon(Ionicons.calendar, size: 16),
                    SizedBox(width: 8),
                    Padding(
                      padding: EdgeInsets.only(top: 2),
                      child: Text(
                        "Booking time: " +
                            '${formatDate(widget.bookingTime.toString(), showTime: true)}',
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
                Divider(height: 24, thickness: 1),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      children: [
                        Icon(Icons.near_me, color: Colors.green, size: 18),
                        SizedBox(height: 2),
                        SizedBox(
                          height: 56,
                          child: DottedLine(
                            dashColor:
                                Theme.of(context).brightness == Brightness.dark
                                ? Colors.white
                                : Colors.black,
                            direction: Axis.vertical,
                            lineLength: double.infinity,
                            lineThickness: 1,
                            dashLength: 2,
                          ),
                        ),
                        SizedBox(height: 2),
                        Icon(Icons.location_on, color: Colors.red, size: 18),
                      ],
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 58,
                            child: Text(
                              widget.startAddress.toString(),
                              overflow: TextOverflow.ellipsis,
                              style: primaryTextStyle(size: 14),
                              maxLines: 3,
                            ),
                          ),
                          SizedBox(height: 10),
                          SizedBox(
                            height: 58,
                            child: Text(
                              widget.endAddress.toString(),
                              overflow: TextOverflow.ellipsis,
                              style: primaryTextStyle(size: 14),
                              maxLines: 3,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),

                RideContactAndDetailsWidget(
                  rider: UserData(
                    firstName: widget.riderName,
                    lastName: "",
                    rating: widget.riderRating,
                  ),
                  showActions: false,
                  onRideRequest: OnRideRequest(),
                  onRefreshRideDetails: () {},
                  onCancelRide: (p) {},
                ),
                const SizedBox(height: 6),
                Row(
                  children: [
                    const Text("Service Name"),
                    width10,
                    Text(
                      widget.serviceName,
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                Column(
                  children: [
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        widget.isButton
                            ? AppButton(
                                text: widget.text ?? '',
                                onPressed: widget.onPressed == null
                                    ? null
                                    : () async {
                                        widget.onPressed!();
                                      },
                              )
                            : SizedBox(),
                        widget.onCancel != null
                            ? AppButton(
                                text: "Cancel",
                                onPressed: () async {
                                  widget.onCancel!();
                                },
                              )
                            : SizedBox(),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

        // Container(
        //   margin: EdgeInsets.only(
        //       top: 8,
        //       bottom: 8,
        //       left: 16,
        //       right: 16),
        //   width: MediaQuery.of(context).size.width,
        //   decoration: BoxDecoration(
        //     color: Colors.white,
        //     borderRadius: BorderRadius.circular(
        //         defaultRadius),
        //     boxShadow: [
        //       BoxShadow(
        //           color:
        //               Colors.black.withOpacity(0.2),
        //           blurRadius: 3),
        //     ],
        //   ),
        //   child: Padding(
        //     padding: EdgeInsets.all(8),
        //     child: Column(
        //       crossAxisAlignment:
        //           CrossAxisAlignment.start,
        //       children: [
        //         Row(
        //           crossAxisAlignment:
        //               CrossAxisAlignment.start,
        //           children: [
        //             ClipRRect(
        //               borderRadius:
        //                   BorderRadius.circular(
        //                       defaultRadius),
        //               child: commonCachedNetworkImage(
        //                   _scheduledRides[index]
        //                       .riderProfileImage
        //                       .validate(),
        //                   // servicesListData!
        //                   //     .riderProfileImage
        //                   //     .validate(),
        //                   height: 35,
        //                   width: 35,
        //                   fit: BoxFit.cover),
        //             ),
        //             SizedBox(width: 12),
        //             Expanded(
        //               child: Column(
        //                 crossAxisAlignment:
        //                     CrossAxisAlignment
        //                         .start,
        //                 children: [
        //                   Text(
        //                       '${_scheduledRides[index].riderName}',
        //                       style: boldTextStyle(
        //                           size: 14)),
        //                   SizedBox(height: 4),
        //                   Text(
        //                       '${_scheduledRides[index].riderEmail.validate()}',
        //                       style:
        //                           secondaryTextStyle()),
        //                 ],
        //               ),
        //             ),
        //             // Container(
        //             //   decoration: BoxDecoration(
        //             //       ,
        //             //       borderRadius:
        //             //           BorderRadius.circular(
        //             //               defaultRadius)),
        //             //   padding: EdgeInsets.all(6),
        //             //   child: Text("$duration",
        //             //       style: boldTextStyle(
        //             //           color: Colors.white)),
        //             // )
        //           ],
        //         ),
        //         height5,
        //         Divider(
        //             color: Colors.grey
        //                 .withOpacity(0.5),
        //             height: 0,
        //             indent: 15,
        //             endIndent: 15),
        //         Padding(
        //           padding: const EdgeInsets.only(
        //             left: 35,
        //             top: 10,
        //             bottom: 10,
        //           ),
        //           child: Column(
        //             crossAxisAlignment:
        //                 CrossAxisAlignment.start,
        //             children: [
        //               Text(
        //                   '${_scheduledRides[index].datetime.toString().substring(0, 10)}',
        //                   style:
        //                       secondaryTextStyle()),
        //               Text(
        //                   '${_scheduledRides[index].datetime.toString().substring(11)}',
        //                   style:
        //                       secondaryTextStyle()),
        //             ],
        //           ),
        //         ),
        //         Divider(
        //             color: Colors.grey
        //                 .withOpacity(0.5),
        //             height: 0,
        //             indent: 15,
        //             endIndent: 15),

        //         SizedBox(height: 16),
        //         Row(
        //           children: [
        //             Column(
        //               children: [
        //                 Icon(Icons.near_me,
        //                     color: Colors.green,
        //                     size: 18),
        //                 SizedBox(height: 2),
        //                 SizedBox(
        //                   height: 34,
        //                   child: DottedLine(
        //                     direction:
        //                         Axis.vertical,
        //                     lineLength:
        //                         double.infinity,
        //                     lineThickness: 1,
        //                     dashLength: 2,
        //                     dashColor:
        //                          ,
        //                   ),
        //                 ),
        //                 SizedBox(height: 2),
        //                 Icon(Icons.location_on,
        //                     color: Colors.red,
        //                     size: 18),
        //               ],
        //             ),
        //             SizedBox(width: 16),
        //             Expanded(
        //               child: Column(
        //                 crossAxisAlignment:
        //                     CrossAxisAlignment
        //                         .start,
        //                 children: [
        //                   SizedBox(height: 2),
        //                   Text(
        //                       _scheduledRides[index]
        //                           .startAddress
        //                           .validate(),
        //                       style:
        //                           primaryTextStyle(
        //                               size: 14),
        //                       maxLines: 2),
        //                   SizedBox(height: 22),
        //                   Text(
        //                       _scheduledRides[index]
        //                           .endAddress
        //                           .validate(),
        //                       style:
        //                           primaryTextStyle(
        //                               size: 14),
        //                       maxLines: 2),
        //                 ],
        //               ),
        //             ),
        //           ],
        //         ),
        //         SizedBox(height: 8),

        //         // Row(
        //         //   children: [
        //         //     Expanded(
        //         //       child: inkWellWidget(
        //         //         onTap: () {
        //         //           showConfirmDialogCustom(
        //         //               dialogType: DialogType.DELETE,
        //         //               primaryColor:
        //         //                    ,
        //         //               title: language
        //         //                   .areYouSureYouWantToCancelThisRequest,
        //         //               positiveText: language.yes,
        //         //               negativeText: language.no,
        //         //               context, onAccept: (v) {
        //         //             rideReqRespTimer!.cancel();
        //         //             sharedPref
        //         //                 .remove(ON_RIDE_MODEL);
        //         //             sharedPref.remove(IS_TIME2);
        //         //             rideRequestAccept(
        //         //                 deCline: true);
        //         //           });
        //         //         },
        //         //         child: Container(
        //         //           padding: EdgeInsets.all(8),
        //         //           decoration: BoxDecoration(
        //         //               borderRadius:
        //         //                   BorderRadius.circular(
        //         //                       defaultRadius),
        //         //               border: Border.all(
        //         //                   color: Colors.red)),
        //         //           child: Text(language.decline,
        //         //               style: boldTextStyle(
        //         //                   color: Colors.red),
        //         //               textAlign: TextAlign.center),
        //         //         ),
        //         //       ),
        //         //     ),
        //         //     SizedBox(width: 16),
        //         //     Expanded(
        //         //       child: AppButtonWidget(
        //         //         padding: EdgeInsets.zero,
        //         //         text: language.accept,
        //         //         shapeBorder: RoundedRectangleBorder(
        //         //             borderRadius:
        //         //                 BorderRadius.circular(
        //         //                     defaultRadius)),
        //         //  
        //         //         textStyle: boldTextStyle(
        //         //             color: Colors.white),
        //         //         onTap: () {
        //         //           showConfirmDialogCustom(
        //         //               primaryColor:
        //         //                    ,
        //         //               dialogType: DialogType.ACCEPT,
        //         //               positiveText: language.yes,
        //         //               negativeText: language.no,
        //         //               title: language
        //         //                   .areYouSureYouWantToAcceptThisRequest,
        //         //               context, onAccept: (v) {
        //         //             rideReqRespTimer!.cancel();
        //         //             sharedPref.remove(IS_TIME2);
        //         //             sharedPref
        //         //                 .remove(ON_RIDE_MODEL);
        //         //             rideRequestAccept();
        //         //           });
        //         //         },
        //         //       ),
        //         //     ),
        //         //   ],
        //         // )
        //       ],
        //     ),
        //   ),
        // ),