import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/ComplaintModel.dart';
import 'package:rooo_driver/model/DriverRatting.dart';
import 'package:rooo_driver/model/RiderModel.dart';

class ComplaintScreen extends StatefulWidget {
  final DriverRatting driverRatting;
  final RiderModel? riderModel;
  final ComplaintModel? complaintModel;
  final bool isFromHistory;

  const ComplaintScreen({
    super.key,
    required this.driverRatting,
    required this.isFromHistory,
    this.complaintModel,
    this.riderModel,
  });

  @override
  ComplaintScreenState createState() => ComplaintScreenState();
}

class ComplaintScreenState extends State<ComplaintScreen> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  TextEditingController subController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();

  bool _isLoadigData = false;

  // Image upload variables
  File? _selectedImage;
  final ImagePicker _picker = ImagePicker();
  bool _isUploadingImage = false;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    if (widget.complaintModel != null) {}
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      setState(() {
        _isUploadingImage = true;
      });

      XFile? image = await _picker.pickImage(
        source: source,
        imageQuality: 70,
        maxWidth: 800,
        maxHeight: 800,
      );

      if (image != null) {
        // Check image size
        File imageFile = File(image.path);
        int fileSizeInBytes = await imageFile.length();
        int fileSizeInMB = fileSizeInBytes ~/ (1024 * 1024);

        if (fileSizeInMB > 5) {
          if (mounted) {
            GlobalMethods.errorToast(context, "Image size is too large. Please select an image up to 5 MB.");
          }
          setState(() {
            _isUploadingImage = false;
          });
          return;
        }

        // Verify the file exists and is readable
        if (await imageFile.exists()) {
          setState(() {
            _selectedImage = imageFile;
            _isUploadingImage = false;
          });
        } else {
          if (mounted) {
            GlobalMethods.errorToast(context, "Error: Image file not found");
          }
          setState(() {
            _isUploadingImage = false;
          });
        }
      } else {
        setState(() {
          _isUploadingImage = false;
        });
      }
    } catch (e) {
      setState(() {
        _isUploadingImage = false;
      });
      if (mounted) {
        GlobalMethods.errorToast(context, "Error selecting image: $e");
      }
    }
  }

  void _showImageSourceBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "Select Image Source",
                style: boldTextStyle(size: 18),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        Navigator.pop(context);
                        _pickImage(ImageSource.camera);
                      },
                      child: Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.camera_alt,
                              size: 40,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            "Camera",
                            style: primaryTextStyle(),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        Navigator.pop(context);
                        _pickImage(ImageSource.gallery);
                      },
                      child: Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.photo_library,
                              size: 40,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            "Gallery",
                            style: primaryTextStyle(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  void _removeImage() {
    setState(() {
      _selectedImage = null;
    });
  }

  Future<void> saveComplainDriver() async {
    if (formKey.currentState!.validate()) {
      setState(() {
        _isLoadigData = true;
      });

      try {
        Map req = {
          "driver_id": widget.riderModel!.driverId,
          "rider_id": widget.riderModel!.riderId,
          "ride_request_id": widget.riderModel!.id,
          "complaint_by": "driver",
          "subject": subController.text.trim(),
          "description": descriptionController.text.trim(),
          "status": "pending",
        };

        // Verify image file still exists before uploading
        File? imageFileToUpload = _selectedImage;
        if (imageFileToUpload != null) {
          if (!await imageFileToUpload.exists()) {
            setState(() {
              _isLoadigData = false;
            });
            GlobalMethods.errorToast(context, "Selected image file not found. Please select the image again.");
            setState(() {
              _selectedImage = null;
            });
            return;
          }

          // Check file size
          int fileSizeInBytes = await imageFileToUpload.length();
          int fileSizeInMB = fileSizeInBytes ~/ (1024 * 1024);
          if (fileSizeInMB > 5) {
            setState(() {
              _isLoadigData = false;
            });
            GlobalMethods.errorToast(context, "Image file is too large. Please select an image under 5 MB.");
            return;
          }
        }

        StatusMessageModel value = await saveComplainWithImage(
          request: req,
          imageFile: imageFileToUpload,
        );

        setState(() {
          _isLoadigData = false;
        });

        if (value.status) {
          GlobalMethods.succesToast(context, value.message);
          Navigator.pop(context);
          if (widget.isFromHistory) {
            Navigator.pop(context);
          }
        } else {
          GlobalMethods.errorToast(context, value.message);
        }
      } catch (error) {
        setState(() {
          _isLoadigData = false;
        });
        GlobalMethods.errorToast(context, "Something went wrong");
      }
    }
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard(context);
      },
      child: Scaffold(
        appBar: RoooAppbar(
          title: "Raise Complaint",
          isDarkOverlay: false,
        ),
        body: Stack(
          children: [
            Form(
              key: formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: MediaQuery.sizeOf(context).width,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          (widget.riderModel?.driverName ?? "").isEmpty
                              ? const SizedBox()
                              : Padding(
                                  padding: const EdgeInsets.only(bottom: 20.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(35),
                                        child: commonCachedNetworkImage(
                                            widget
                                                .riderModel!.driverProfileImage,
                                            height: 70,
                                            width: 70,
                                            fit: BoxFit.cover),
                                      ),
                                      const SizedBox(width: 16),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const SizedBox(height: 8),
                                          Text(
                                              widget.riderModel!.driverName
                                                  .validate(),
                                              style: boldTextStyle()),
                                          const SizedBox(height: 8),
                                          if (widget.driverRatting.rating !=
                                              null)
                                            RatingBar.builder(
                                              direction: Axis.horizontal,
                                              glow: false,
                                              allowHalfRating: false,
                                              ignoreGestures: true,
                                              wrapAlignment:
                                                  WrapAlignment.spaceBetween,
                                              itemCount: 5,
                                              itemSize: 20,
                                              initialRating: double.parse(widget
                                                  .driverRatting.rating
                                                  .toString()),
                                              itemPadding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 0),
                                              itemBuilder: (context, _) =>
                                                  const Icon(Icons.star,
                                                      color: Colors.amber),
                                              onRatingUpdate: (rating) {
                                                //
                                              },
                                            ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                        ],
                      ),
                    ),
                    TextFormField(
                      maxLength: 100,
                      validator: (value) {
                        if ((value ?? "").trim().isEmpty) {
                          return "Please enter subject";
                        }
                        return null;
                      },
                      controller: subController,
                      decoration: const InputDecoration(
                          labelText: "Please enter subject"),
                      // readOnly: widget.complaintModel != null ? true : false,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: descriptionController,
                      validator: (value) {
                        if ((value ?? "").trim().isEmpty) {
                          return "Please enter description";
                        }
                        return null;
                      },
                      // readOnly: widget.complaintModel != null ? true : false,
                      decoration: const InputDecoration(
                          labelText: "Please enter message"),
                      minLines: 2,
                      maxLines: 5,
                    ),
                    const SizedBox(height: 16),

                    // Image attachment section
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Attachment (Optional)",
                          style: boldTextStyle(size: 16),
                        ),
                        const SizedBox(height: 8),
                        if (_selectedImage != null) ...[
                          // Show selected image preview
                          Container(
                            width: double.infinity,
                            height: 200,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.grey.shade300),
                            ),
                            child: Stack(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: Image.file(
                                    _selectedImage!,
                                    width: double.infinity,
                                    height: 200,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                Positioned(
                                  top: 8,
                                  right: 8,
                                  child: GestureDetector(
                                    onTap: _removeImage,
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: Colors.red,
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: const Icon(
                                        Icons.close,
                                        color: Colors.white,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // const SizedBox(height: 8),
                          // Text(
                          //   "Tap the X to remove image",
                          //   style: secondaryTextStyle(size: 12),
                          // ),
                        ] else ...[
                          // Show upload button
                          GestureDetector(
                            onTap: _showImageSourceBottomSheet,
                            child: Container(
                              width: double.infinity,
                              height: 120,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.grey.shade300,
                                  style: BorderStyle.solid,
                                ),
                              ),
                              child: _isUploadingImage
                                  ? const Center(
                                      child: CircularProgressIndicator(),
                                    )
                                  : Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.add_photo_alternate_outlined,
                                          size: 40,
                                          color: Theme.of(context).primaryColor,
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          "Tap to upload image",
                                          style: TextStyle(
                                            color: Theme.of(context).primaryColor,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          "Camera or Gallery",
                                          style: secondaryTextStyle(size: 12),
                                        ),
                                      ],
                                    ),
                            ),
                          ),
                        ],
                      ],
                    ),

                    const SizedBox(height: 24),
                    // if (widget.complaintModel == null)
                    AppButton(
                      text: "Save",
                      width: MediaQuery.sizeOf(context).width,
                      onPressed: () async {
                        saveComplainDriver();
                      },
                    ),
                  ],
                ),
              ),
            ),
            _isLoadigData ? const AppLoader() : const SizedBox(),
          ],
        ),
      ),
    );
  }
}
