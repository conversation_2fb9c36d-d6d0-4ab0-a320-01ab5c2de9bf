import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:rooo_driver/features/care/cubit/care_cubit.dart';
import 'package:rooo_driver/features/care/models/carelist_models.dart';
import 'package:rooo_driver/features/care/screens/new_care_detail_screen.dart';
import 'package:rooo_driver/global/constants/app_enums.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:rooo_driver/utils/Extensions/app_common.dart';

class CareClosedScreen extends StatefulWidget {
  const CareClosedScreen({super.key});

  @override
  State<CareClosedScreen> createState() => _CareClosedScreenState();
}

class _CareClosedScreenState extends State<CareClosedScreen> {
  List<CareModel> _careCloasedList = [];
  String _apiMessage = "";
  int _currentPage = 1;
  int _totalPage = 1;

  ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    getNextPageData();
    getClosedCareList();
  }

  @override
  void dispose() {
    super.dispose();
    _scrollController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: screenPadding,
        child: BlocConsumer<CareCubit, CareState>(
          listener: (context, state) {
            if (state is CareLoadedState) {
              _careCloasedList = state.careResponseModel.data ?? [];
              _currentPage = state.careResponseModel.pagination!.currentPage!;
              _totalPage = state.careResponseModel.pagination!.totalPages!;
              _apiMessage = state.careResponseModel.message.toString();
            }
            if (state is CareErrorState) {
              GlobalMethods.infoToast(context, errorMessage);
            }
          },
          builder: (context, state) {
            return ScreenBody(
              isLoading: state is CareLoadingState,
              isEmpty: _careCloasedList.isEmpty,
              emptyMessage: _apiMessage,
              child: ListView.builder(
                itemCount: _careCloasedList.length,
                itemBuilder: (context, index) {
                  CareModel data = _careCloasedList[index];
                  return AnimationConfiguration.staggeredList(
                    delay: Duration(milliseconds: 200),
                    position: index,
                    duration: Duration(milliseconds: 375),
                    child: SlideAnimation(
                      child: InkWell(
                        onTap: () {
                          GlobalMethods.pushScreen(
                              context: context,
                              screen: NewCareDetailsScreen(
                                id: data.id,
                                isClosed: true,
                              ),
                              screenIdentifier:
                                  ScreenIdentifier.NewCareDetailsScreen);
                        },
                        child: Card(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor,
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(4),
                                    topRight: Radius.circular(4),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Issue #${data.id}',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                        color: Colors.white,
                                      ),
                                    ),
                                    Text(
                                      formatDate(data.created_at.toString(),showTime: true),
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.all(12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      data.subject,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      data.message ?? '',
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                width: double.infinity,
                                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).brightness == Brightness.dark 
                                    ? Colors.grey[800] 
                                    : Colors.grey[100],
                                  borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(4),
                                    bottomRight: Radius.circular(4),
                                  ),
                                ),
                                child: Text(
                                  'Closed on: ${data.updated_at != null ? DateTime.parse(data.updated_at!).toLocal().toString().substring(0, 16).replaceAll('T', ' ') : ''}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Theme.of(context).brightness == Brightness.dark 
                                      ? Colors.grey[300]
                                      : Colors.grey[600],
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }

  getClosedCareList() {
    BlocProvider.of<CareCubit>(context).getCareClosedList(page: _currentPage);
  }

  getNextPageData() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (_currentPage < _totalPage) {
          _currentPage++;
        }
      }
    });
  }
}
