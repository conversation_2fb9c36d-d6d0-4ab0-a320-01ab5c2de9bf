//
// import 'package:empty_widget/empty_widget.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
// import 'package:rooo_driver/components/custom_text.dart';
// import 'package:rooo_driver/components/ios_padding_screen.dart';
// import 'package:rooo_driver/features/care/cubit/care_cubit.dart';
// import 'package:rooo_driver/features/care/models/carelist_models.dart';
// import 'package:rooo_driver/features/care/screens/add_care_screen.dart';
// import 'package:rooo_driver/features/care/screens/new_care_detail_screen.dart';
// import 'package:rooo_driver/features/care/utils/care_utils.dart';
// import 'package:rooo_driver/global/widgets/empty_widget.dart';
// import 'package:rooo_driver/global/widgets/loader.dart';
// import 'package:rooo_driver/utils/Constants.dart';
// import 'package:rooo_driver/utils/Extensions/app_common.dart';

// class CarePendingScreen extends StatefulWidget {
//   const CarePendingScreen({super.key});

//   @override
//   State<CarePendingScreen> createState() => _CarePendingScreenState();
// }

// class _CarePendingScreenState extends State<CarePendingScreen> {
//   List<CareModel> carePendingList = [];
//   String apiMessage = "";
//   int currentPage = 1;
//   int totalPage = 1;

//   ScrollController scrollController = ScrollController();

//   @override
//   void initState() {
//     //
//     super.initState();

//     getNextPageData();
//     getPendingCareList();
//   }

//   @override
//   void dispose() {
//
//     super.dispose();
//     scrollController.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       floatingActionButton:

//       IosPadding(
//         child: FloatingActionButton(
//           onPressed: () async {
//             bool? result = await launchScreen(context, AddCareScreen());
//             if (result != null) {
//               getPendingCareList();
//             }
//           },
//           child: Icon(
//             Icons.add,
//           ),
//         ),
//       ),
//       body: Padding(
//         padding: screenPadding,
//         child: BlocConsumer<CareCubit, CareState>(
//           listener: (context, state) {
//             if (state is CareLoadedState) {
//               carePendingList = state.careResponseModel.data ?? [];
//               currentPage = state.careResponseModel.pagination!.currentPage!;
//               totalPage = state.careResponseModel.pagination!.totalPages!;
//               apiMessage = state.careResponseModel.message.toString();
//               care_counts.value = [];

//               carePendingList.forEach((element) {
//                 if (element.care_count != null) {
//                   care_counts.value.add(element.care_count!);
//                 }
//               });
//             }
//             if (state is CareErrorState) {
//               GlobalMethods.infoToast(context,  errorMessage);
//             }
//           },
//           builder: (context, state) {
//             if (state is CareLoadingState) {
//               return RooLoader();
//             } else if (carePendingList.isEmpty) {
//               return RooEmptyWidegt(title: "No care");
//             }
//             return ListView.builder(
//               itemCount: carePendingList.length,
//               itemBuilder: (context, index) {
//                 CareModel data = carePendingList[index];
//                 return AnimationConfiguration.staggeredList(
//                   delay: Duration(milliseconds: 100),
//                   position: index,
//                   child: SlideAnimation(
//                     child: Card(
//                         color: Colors.white.withOpacity(0.5),
//                         child: ListTile(
//                           shape: RoundedRectangleBorder(borderRadius: radius()),
//                           onTap: () {

//                             launchScreen(
//                               context,
//                               NewCareDetailsScreen(
//                                 index: index,
//                                 id: data.id,
//                                 isClosed: false,
//                               ),
//                             );
//                           },
//                           title: Text(data.subject),

//                           trailing: ValueListenableBuilder<List<int>>(
//                             valueListenable: care_counts,
//                             builder: (context, value, child) {
//                               if (value[index] > 0) {
//                                 return CircleAvatar(
//                                     radius: 12,
//                                     backgroundColor: Colors.red,
//                                     child: CustomText(
//                                       data: care_counts.value[index].toString(),
//                                       size: 10,
//                                       color: Colors.white,
//                                     ));
//                               }
//                               return SizedBox();
//                             },
//                           ),
//                           // trailing:data.care_count==null?SizedBox():  Visibility(
//                           //   visible: data.care_count!>0,
//                           //   child: CircleAvatar(
//                           //     radius: 12,
//                           //     backgroundColor: Colors.red,
//                           //     child: CustomText(data:care_counts.value[index] .toString(),size: 10,
//                           //     color: Colors.white,)),
//                           // ),
//                         )),
//                   ),
//                 );
//               },
//             );
//           },
//         ),
//       ),
//     );
//   }

//   getPendingCareList() {
//     BlocProvider.of<CareCubit>(context).getCarePendingList(page: currentPage);
//   }

//   getNextPageData() {
//     scrollController.addListener(() {
//       if (scrollController.position.pixels ==
//           scrollController.position.maxScrollExtent) {
//         if (currentPage < totalPage) {
//           currentPage++;
//         }
//       }
//     });
//   }
// }

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:rooo_driver/features/ride_history/screens/ride_history_completed_screen.dart';
import 'package:rooo_driver/features/ride_history/screens/ride_history_screen.dart';
import 'package:rooo_driver/global/constants/app_enums.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/widgets/ios_padding.dart';
import 'package:rooo_driver/features/care/cubit/care_cubit.dart';
import 'package:rooo_driver/features/care/models/carelist_models.dart';
import 'package:rooo_driver/features/care/models/carelist_response_model.dart';
import 'package:rooo_driver/features/care/screens/add_care_screen.dart';
import 'package:rooo_driver/features/care/screens/new_care_detail_screen.dart';
import 'package:rooo_driver/features/care/utils/care_utils.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';

import 'package:rooo_driver/global/constants/Colors.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Constants.dart';

class PendingCareScreen2 extends StatefulWidget {
  const PendingCareScreen2({super.key});

  @override
  State<PendingCareScreen2> createState() => _PendingCareScreen2State();
}

class _PendingCareScreen2State extends State<PendingCareScreen2> {
  int _currentPage = 1;
  int _totalPage = 1;
  ScrollController _scrollController = ScrollController();

  List<CareModel> _pendingcareList = [];
  String _emptyMesssage = "";

  _onScrolling() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (_currentPage < _totalPage) {
          _currentPage++;

          _getPendingCareList(currentPage: _currentPage);
        }
      }
    });
  }

  _getPendingCareList({required int currentPage}) {
    BlocProvider.of<CareCubit>(context)
        .getCarePendingList(currentPage: currentPage);
  }

  _onDataLoaded({required CareResponseModel pendingcareResponseModel}) {
    _currentPage = pendingcareResponseModel.pagination?.currentPage ?? 1;
    _totalPage = pendingcareResponseModel.pagination?.totalPages ?? 1;

    _pendingcareList = pendingcareResponseModel.data ?? [];
    _emptyMesssage = pendingcareResponseModel.message.toString();
  }

  _onPullToRefresh() {
    _currentPage = 1;
    _init();
  }

  _init() {
    _onScrolling();
    _getPendingCareList(currentPage: _currentPage);
  }

  _dispose() {
    _scrollController.dispose();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: IosPadding(
        child: FloatingActionButton(
          backgroundColor: AppColors.primaryColor(context),
          onPressed: () async {
            bool? individual = await GlobalMethods.showConfirmationDialog(
              upDownButtons : true,
                context: context,
                title:
                    "Please specify whether you want to create a general care request or create a ride related issue.",
                negativeText: "Ride issue",
                onNegativeAction: () {},
                positiveText: "General care request",
                onPositiveAction: () {});

            if (individual == false) {
              Navigator.of(context).push(MaterialPageRoute(
                builder: (context) => RideHistoryScreen()
              ));
            } else if (individual == true) {
              bool? result = await GlobalMethods.pushScreen(
                  context: context,
                  screen: AddCareScreen(),
                  screenIdentifier: ScreenIdentifier.AddCareScreen);
              if (result != null) {
                _getPendingCareList(currentPage: _currentPage);
              }
            }
          },
          child: Icon(
            Icons.add,
          ),
        ),
      ),
      body: BlocConsumer<CareCubit, CareState>(
        listener: (context, state) {
          if (state is CareLoadedState) {
            _onDataLoaded(pendingcareResponseModel: state.careResponseModel);
          }
          if (state is CareErrorState) {
            GlobalMethods.errorToast(context, state.message);
          }
        },
        builder: (context, state) {
          return ScreenBody(
              onPullToRefresh: () async => await _onPullToRefresh(),
              isLoading: state is CareLoadingState,
              isEmpty: _pendingcareList.isEmpty,
              emptyMessage: _emptyMesssage,
              child: AnimationLimiter(
                child: ListView.separated(
                  padding: screenPadding,
                  shrinkWrap: true,
                  separatorBuilder: (context, index) => height10,
                  itemCount: _pendingcareList.length,
                  itemBuilder: (BuildContext context, int index) {
                    CareModel data = _pendingcareList[index];
                    return AnimationConfiguration.staggeredList(
                        position: index,
                        duration: const Duration(milliseconds: 1000),
                        child: SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: InkWell(
                              onTap: () {
                                GlobalMethods.pushScreen(
                                    context: context,
                                    screen: NewCareDetailsScreen(
                                        id: data.id,
                                        isClosed: false,
                                        ),
                                    screenIdentifier:
                                        ScreenIdentifier.NewCareDetailsScreen);
                              },
                              child: Card(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).primaryColor,
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(4),
                                          topRight: Radius.circular(4),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Issue #${data.id}',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                              color: Colors.white,
                                            ),
                                          ),
                                          Text(
                                            formatDate(data.created_at.toString(),showTime: true),
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(12),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            data.subject,
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          Text(
                                            data.message ?? '',
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).brightness == Brightness.dark 
                                          ? Colors.grey[800] 
                                          : Colors.grey[100],
                                        borderRadius: BorderRadius.only(
                                          bottomLeft: Radius.circular(4),
                                          bottomRight: Radius.circular(4),
                                        ),
                                      ),
                                      child: Text(
                                        'Status: '+data.status.toUpperCase(),
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Theme.of(context).brightness == Brightness.dark 
                                            ? Colors.grey[300]
                                            : Colors.grey[600],
                                          fontStyle: FontStyle.italic,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ));
                  },
                ),
              ));
        },
      ),
    );
  }
}

//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////
