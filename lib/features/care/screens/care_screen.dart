import 'package:flutter/material.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/features/care/screens/RideHelpScreen.dart';
import 'package:rooo_driver/features/care/screens/care_closed_screen.dart';
import 'package:rooo_driver/features/care/screens/care_pending_screen.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/utils/Common.dart';

class NewCareScreen extends StatefulWidget {
  final bool isPendingCare;
  final int? rideId;

  const NewCareScreen({
    super.key,
    required this.isPendingCare,
    this.rideId,
  });
  @override
  NewCareScreenState createState() => NewCareScreenState();
}

class NewCareScreenState extends State<NewCareScreen> {
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      initialIndex: widget.isPendingCare ? 0 : 1,
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: Text("ROOO Care"),
          centerTitle: false,
          actions: [
            Padding(
              padding: const EdgeInsets.only(right:4.0),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.black,
                ),
                child: Text("Ride Issues"),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => RideHelpScreen(indicatorUpdater: () {}),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        body: Column(
          children: [
            tabContainer(
              tabs: [language.pending, language.closedTxt],
            ),
            Expanded(
              child: TabBarView(
                  children: [PendingCareScreen2(), CareClosedScreen()]),
            ),
          ],
        ),
      ),
    );
  }
}
