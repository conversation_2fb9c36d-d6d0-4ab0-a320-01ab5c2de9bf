import 'package:rooo_driver/features/care/models/care_details_response_model.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class CareChatItemWidget extends StatefulWidget {
  final CareComment data;

  CareChatItemWidget({required this.data});

  @override
  _CareChatItemWidgetState createState() => _CareChatItemWidgetState();
}

class _CareChatItemWidgetState extends State<CareChatItemWidget> {
  String? images;

  void initState() {
    super.initState();
    init();
  }

  init() async {}

  @override
  Widget build(BuildContext context) {
  

    Widget chatItem(String? messageTypes) {
      switch (messageTypes) {
        case TEXT:
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: !(widget.data.addedBy != "driver")
                ? CrossAxisAlignment.end
                : CrossAxisAlignment.start,
            children: [
              Text(widget.data.comment,
                  style: primaryTextStyle(
                      color: !(widget.data.addedBy == "driver")
                          ? Colors.white
                          : Colors.black),
                  maxLines: null),
              SizedBox(height: 8),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    formatDate(widget.data.createdAt.toString(), showTime: true),
                    style: primaryTextStyle(
                      color: _getTimeColor(widget.data.addedBy),
                      size: 10,
                    ),
                  ),
                  // SizedBox(height: 2),
                  // !(widget.data.isFromAdmin ?? true)
                  //     ? !widget.data!.isMessageRead!
                  //         ? Icon(Icons.done, size: 12, color: Colors.white60)
                  //         : Icon(Icons.done_all,
                  //             size: 12, color: Colors.white60)
                  //     : Offstage()
                ],
              ),
            ],
          );
        // case IMAGE:
        //   if (widget.data!.photoUrl.validate().isNotEmpty ||
        //       widget.data!.photoUrl != null) {
        //     return Stack(
        //       children: [
        //         ClipRRect(
        //           borderRadius: radius(16),
        //           child: commonCachedNetworkImage(
        //               widget.data!.photoUrl.validate(),
        //               fit: BoxFit.contain,
        //               width: 250),
        //         ),
        //         Positioned(
        //             bottom: 8,
        //             right: 8,
        //             child: Row(
        //               mainAxisSize: MainAxisSize.min,
        //               children: [
        //                 Text(
        //                   time,
        //                   style: primaryTextStyle(
        //                     color: !widget.data!.isMe.validate()
        //                         ? Colors.blueGrey.withOpacity(0.6)
        //                         : Colors.white.withOpacity(0.6),
        //                     size: 10,
        //                   ),
        //                 ),
        //                 SizedBox(height: 2),
        //                 widget.data!.isMe!
        //                     ? !widget.data!.isMessageRead!
        //                         ? Icon(Icons.done,
        //                             size: 12, color: Colors.white60)
        //                         : Icon(Icons.done_all,
        //                             size: 12, color: Colors.white60)
        //                     : Offstage()
        //               ],
        //             ))
        //       ],
        //     );
        //   } else {
        //     return Container(child: Loader(), height: 250, width: 250);
        //   }
        default:
          return Container();
      }
    }

    EdgeInsetsGeometry customPadding(String? messageTypes) {
      switch (messageTypes) {
        case TEXT:
          return EdgeInsets.symmetric(horizontal: 12, vertical: 8);
        case IMAGE:
          return EdgeInsets.symmetric(horizontal: 4, vertical: 4);
        case VIDEO:
          return EdgeInsets.symmetric(horizontal: 4, vertical: 4);
        case AUDIO:
          return EdgeInsets.symmetric(horizontal: 4, vertical: 4);
        default:
          return EdgeInsets.symmetric(horizontal: 4, vertical: 4);
      }
    }

    return GestureDetector(
      onLongPress: !(widget.data.addedBy == "Super Admin") ? null : () async {},
      child: Container(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: !(widget.data.addedBy == "Super Admin")
              ? CrossAxisAlignment.end
              : CrossAxisAlignment.start,
          mainAxisAlignment: !(widget.data.addedBy == "driver")
              ? MainAxisAlignment.end
              : MainAxisAlignment.start,
          children: [
            Container(
              margin: !(widget.data.addedBy == "Super Admin")
                  ? EdgeInsets.only(
                      top: 0.0,
                      bottom: 0.0,
                      left: isRTL ? 0 : MediaQuery.sizeOf(context).width * 0.25,
                      right: 8)
                  : EdgeInsets.only(
                      top: 2.0,
                      bottom: 2.0,
                      left: 8,
                      right:
                          isRTL ? 0 : MediaQuery.sizeOf(context).width * 0.25),
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(
                boxShadow:  defaultBoxShadow(),
                color: !(widget.data.addedBy == "Super Admin")
                  
                        ? AppColors.primaryColor(context)
                        : Colors.grey.shade800,
                borderRadius: !(widget.data.addedBy == "driver")
                    ? BorderRadius.only(
                        bottomLeft: radiusCircular(12),
                        topLeft: radiusCircular(12),
                        bottomRight: radiusCircular(0),
                        topRight: radiusCircular(12))
                    : BorderRadius.only(
                        bottomLeft: radiusCircular(0),
                        topLeft: radiusCircular(12),
                        bottomRight: radiusCircular(12),
                        topRight: radiusCircular(12)),
              ),
              child: chatItem(TEXT),
            ),
            const SizedBox(
              height: 10,
            ),
          ],
        ),
        margin: EdgeInsets.only(top: 2, bottom: 2),
      ),
    );
  }

  Color _getTimeColor(String addedBy) {
    return !(addedBy == "Super Admin") ? Colors.white : Colors.black;
  }
}
