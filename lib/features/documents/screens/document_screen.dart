import 'package:rooo_driver/features/ride_flow/screen/ride_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/documents/models/document_model.dart';
import 'package:rooo_driver/features/documents/models/driver_document_model.dart';
import 'package:rooo_driver/features/documents/widgets/document_card.dart';
import 'package:rooo_driver/global/widgets/bottom_button.dart';

class DocumentScreen extends StatefulWidget {
  final bool canGoToDashboard;
  const DocumentScreen({super.key, required this.canGoToDashboard});

  @override
  State<DocumentScreen> createState() => _DocumentScreenState();
}

class _DocumentScreenState extends State<DocumentScreen> {
  ValueNotifier<DocumentModel> _selectedDocument = ValueNotifier(
    DocumentModel(name: "select here"),
  );
  List<DocumentModel> _documentList = [];
  ValueNotifier<List<DriverDocumentModel>> _uploadedDocumentList =
      ValueNotifier([]);
  Set<int> _uploadedDocumentIdList = {};

  ValueNotifier<XFile> imageFile = ValueNotifier(XFile(''));

  _init() async {
    await _getDocumentList();
    await _getUploadedDocumentList();
  }

  Future _getDocumentList() {
    return BlocProvider.of<DocumentCubit>(context).getDocumentList();
  }

  Future _getUploadedDocumentList() async {
    await BlocProvider.of<DocumentCubit>(context).getUploadedDocumentList();
  }

  _deleteDocument({required int document_id}) {
    BlocProvider.of<DocumentCubit>(context).deleteDocument(id: document_id);
  }

  Future<void> updateDocument({
    required int documentId,
    required String userId,
    required String? imagePath,
    required String? expiryDate,
    required String? docText,
  }) async {
    await BlocProvider.of<DocumentCubit>(context).updateDocument(
      documentId: documentId,
      expiryDate: expiryDate,
      imagePath: imagePath,
      userId: userId,
      docText: docText,
    );
  }

  Future<void> uploadDocument({
    required int documentId,
    required String userId,
    required String? imagePath,
    required String? expiryDate,
    required String? docText,
  }) async {
    await BlocProvider.of<DocumentCubit>(context).uploadDocument(
      documentId: documentId,
      userId: userId,
      imagePath: imagePath,
      expiryDate: expiryDate,
      docText: docText,
    );
  }

  @override
  void initState() {
    GlobalState.isDocumentsScreenOpened = true;
    super.initState();
    _init();
  }

  @override
  void dispose() {
    GlobalState.isDocumentsScreenOpened = false;
    super.dispose();
  }

  Future<void> _updateDocument({
    required DriverDocumentModel document,
    required DRIVER_DOCUMENT_TYPE type,
  }) async {
    if (type == DRIVER_DOCUMENT_TYPE.text) {
      if ((document.docText?.trim().length ?? 0) != 11) {
        GlobalMethods.errorToast(context, "Please enter correct value");
        return;
      } else {
        await updateDocument(
          documentId: document.id!,
          userId: sharedPref.getInt(USER_ID).toString(),
          imagePath: imageFile.value.path,
          expiryDate: document.expiryDate,
          docText: document.docText,
        );

        imageFile.value = XFile('');
      }
    } else {
      if (document.expiryDate == null) {
        GlobalMethods.errorToast(context, "Please select expiry date");
        return;
      }

      showModalBottomSheet(
        showDragHandle: true,
        context: context,
        builder: (context) {
          return Container(
            height: 150,
            child: Row(
              children: [
                MaterialButton(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  height: 100,
                  onPressed: () async {
                    Navigator.pop(context);
                    XFile? result = await ImagePicker().pickImage(
                      source: ImageSource.camera,
                      imageQuality: 30,
                    );

                    if (result != null) {
                      bool value = await GlobalMethods.checkImageSize(
                        result: result,
                        context: context,
                      );

                      if (value) {
                        imageFile.value = result;
                        await updateDocument(
                          documentId: document.id!,
                          userId: sharedPref.getInt(USER_ID).toString(),
                          imagePath: imageFile.value.path,
                          expiryDate: document.expiryDate,
                          docText: document.docText,
                        );
                        imageFile.value = XFile('');
                      }
                    }
                  },
                  child: Column(
                    children: [
                      Icon(Icons.camera, size: 35),
                      height10,
                      CustomText(data: "From Camera"),
                    ],
                  ),
                ),
                MaterialButton(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  height: 100,
                  onPressed: () async {
                    Navigator.pop(context);
                    XFile? result = await ImagePicker().pickImage(
                      source: ImageSource.gallery,
                      imageQuality: 30,
                    );

                    if (result != null) {
                      bool isValid = await GlobalMethods.checkImageSize(
                        result: result,
                        context: context,
                      );

                      if (isValid) {
                        imageFile.value = result;

                        await updateDocument(
                          documentId: document.id!,
                          userId: sharedPref.getInt(USER_ID).toString(),
                          imagePath: imageFile.value.path,
                          expiryDate: document.expiryDate,
                          docText: document.docText,
                        );
                        imageFile.value = XFile('');
                      }
                    }
                  },
                  child: Column(
                    children: [
                      Icon(Icons.image, size: 35),
                      height10,
                      CustomText(data: "From Gallery"),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      );
    }
  }

  Future<void> _addNewDocument({required DocumentModel document}) async {
    if (document.type == DRIVER_DOCUMENT_TYPE.text) {
      if ((document.docText?.trim().length ?? 0) != 11) {
        GlobalMethods.errorToast(context, "Please enter correct value");
        return;
      } else {
        await uploadDocument(
          documentId: document.id!,
          userId: sharedPref.getInt(USER_ID).toString(),
          imagePath: imageFile.value.path,
          expiryDate: document.expiryDate,
          docText: document.docText,
        );
        imageFile.value = XFile('');
      }
    } else {
      if (document.expiryDate == null) {
        GlobalMethods.errorToast(context, "Please select expiry date");
        return;
      }

      showModalBottomSheet(
        showDragHandle: true,
        context: context,
        builder: (context) {
          return Container(
            height: 150,
            child: Row(
              children: [
                MaterialButton(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  height: 100,
                  onPressed: () async {
                    Navigator.pop(context);
                    XFile? result = await ImagePicker().pickImage(
                      source: ImageSource.camera,
                      imageQuality: 30,
                    );

                    if (result != null) {
                      bool value = await GlobalMethods.checkImageSize(
                        result: result,
                        context: context,
                      );

                      if (value) {
                        imageFile.value = result;
                        await uploadDocument(
                          documentId: document.id!,
                          userId: sharedPref.getInt(USER_ID).toString(),
                          imagePath: imageFile.value.path,
                          expiryDate: document.expiryDate,
                          docText: document.docText,
                        );

                        imageFile.value = XFile('');
                      }
                    }
                  },
                  child: Column(
                    children: [
                      Icon(Icons.camera, size: 35),
                      height10,
                      CustomText(data: "From Camera"),
                    ],
                  ),
                ),
                MaterialButton(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  height: 100,
                  onPressed: () async {
                    Navigator.pop(context);
                    XFile? result = await ImagePicker().pickImage(
                      source: ImageSource.gallery,
                      imageQuality: 30,
                    );

                    if (result != null) {
                      bool isValid = await GlobalMethods.checkImageSize(
                        result: result,
                        context: context,
                      );

                      if (isValid) {
                        imageFile.value = result;

                        await uploadDocument(
                          documentId: document.id!,
                          userId: sharedPref.getInt(USER_ID).toString(),
                          imagePath: imageFile.value.path,
                          expiryDate: document.expiryDate,
                          docText: document.docText,
                        );
                        imageFile.value = XFile('');
                      }
                    }
                  },
                  child: Column(
                    children: [
                      Icon(Icons.image, size: 35),
                      height10,
                      CustomText(data: "From Gallery"),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext scaffoldContext) {
    return Scaffold(
      bottomNavigationBar: BottomButton(
        text: language.goDashBoard,
        onPressed: () {
          GlobalMethods.pushAndRemoveAll(
            context: context,
            screen: RideScreen(),
            screenIdentifier: ScreenIdentifier.InitialScreen,
          );
        },
        notVisible: !widget.canGoToDashboard,
      ),
      appBar: RoooAppbar(title: language.document),
      body: BlocConsumer<DocumentCubit, DocumentState>(
        listener: (context, state) async {
          if (state is DocumentLoadedState) {
            _documentList = state.documentList;
            // DateTime oneDayAfter = DateTime.now().add(Duration(days: 1));
            // _documentList.forEach((element) {
            //   element.expiryDate = GlobalMethods.dateToUIString(oneDayAfter);
            // });
            if (_documentList.isNotEmpty) {
              _selectedDocument.value = _documentList[0];
            }
            setState(() {
              //
            });
          } else if (state is UploadedDocumentLoadedState) {
            _uploadedDocumentList.value = state.documentList;
            _uploadedDocumentIdList.clear();
            _uploadedDocumentList.value.forEach((element) {
              _uploadedDocumentIdList.add(element.documentId!);
            });

            _uploadedDocumentList.notifyListeners();
          } else if (state is DocumentDeleltedState) {
            await _getUploadedDocumentList();
          } else if (state is DocumentUploadedState) {
            await _getUploadedDocumentList();
          } else if (state is DocumentUpdatedState) {
            BlocProvider.of<RideFlowCubit>(context).changeOnlineOfflineStatus2(
              makeOnline: 0,
              userId: sharedPref.getInt(USER_ID)!,
            );
            await _getUploadedDocumentList();
          }
        },
        builder: (context, state) {
          return ScreenBody(
            isLoading: state is DocumentLoadingState,
            isEmpty: false,
            emptyMessage: "emptyMessage",
            child: Padding(
              padding: screenPadding,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Padding(
                      padding: screenPadding / 2,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Profile picture uploaded",
                            style: AppTextStyles.title(),
                          ),
                          Icon(
                            Icons.verified_user,
                            color: AppColors.greenColor,
                          ),
                        ],
                      ),
                    ),
                    height10,
                    Padding(
                      padding: screenPadding / 2,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Terms and conditions accepted",
                            style: AppTextStyles.title(),
                          ),
                          Icon(
                            Icons.verified_user,
                            color: AppColors.greenColor,
                          ),
                        ],
                      ),
                    ),
                    height10,
                    Divider(),
                    height10,
                    ValueListenableBuilder<List<DriverDocumentModel>>(
                      valueListenable: _uploadedDocumentList,
                      builder: (context, value, child) {
                        return ListView.separated(
                          separatorBuilder: (context, index) => height10,
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: _documentList.length,
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            DocumentModel data = _documentList[index];

                            if (_uploadedDocumentIdList.contains(data.id)) {
                              DriverDocumentModel uploadDocument = value
                                  .firstWhere(
                                    (element) => element.documentId == data.id,
                                  );
                              return UploadedDocumentCard(
                                data: data,
                                uploadedDocument: uploadDocument,
                                editDocument: () {
                                  _updateDocument(
                                    document: uploadDocument,
                                    type: data.type,
                                  );
                                },
                                onExpiryDateSelection: () {
                                  _selectExpiryDateForExistingDocument(
                                    uploadDocument,
                                  );
                                },
                              );
                            } else {
                              return DocumentCard(
                                data: data,
                                onExpiryDateSelection: () {
                                  _selectExpiryDateForNewDocument(data);
                                },
                                uploadDocument: () {
                                  _addNewDocument(document: data);
                                },
                              );
                            }
                          },
                        );
                      },
                    ),
                    _documentList.isEmpty ||
                            _documentList.length !=
                                _uploadedDocumentList.value.length
                        ? const SizedBox()
                        : Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.check_circle,
                                        color: Colors.green,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        "All documents uploaded",
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  height10,
                                  Center(
                                    child: Text(
                                      "Please wait for the admin approval, if not yet approved",
                                      style: TextStyle(
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  ),
                                  // SizedBox(height: 16),
                                  // Center(
                                  //   child: Text("All done", style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                                  // ),
                                ],
                              ),
                            ),
                          ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _selectExpiryDateForExistingDocument(DriverDocumentModel document) {
    DateTime firstDate = DateTime.now().add(Duration(days: 1));
    DateTime lastDate = DateTime.now().add(Duration(days: 365 * 50));
    DateTime expiryDate = DateTime.now().add(Duration(days: 1));

    DateTime savedExpiryDate = DateTime.now().add(Duration(days: 1));

    if (document.expiryDate != null) {
      savedExpiryDate = GlobalMethods.apiStringToDate(document.expiryDate!);

      if (savedExpiryDate.isBefore(firstDate)) {
        savedExpiryDate = firstDate;
      }
    }

    showDatePicker(
      context: context,
      initialDate: document.expiryDate == null ? expiryDate : savedExpiryDate,
      firstDate: firstDate,
      lastDate: lastDate,
    ).then((value) {
      if (value != null) {
        setState(() {
          document.expiryDate = GlobalMethods.dateToAPIString(value);
        });
      }
    });
  }

  void _selectExpiryDateForNewDocument(DocumentModel document) {
    DateTime firstDate = DateTime.now().add(Duration(days: 1));
    DateTime lastDate = DateTime.now().add(Duration(days: 365 * 50));
    DateTime expiryDate = DateTime.now().add(Duration(days: 1));

    showDatePicker(
      context: context,
      initialDate: document.expiryDate == null
          ? expiryDate
          : GlobalMethods.uiStringToDate(document.expiryDate!),
      firstDate: firstDate,
      lastDate: lastDate,
    ).then((value) {
      if (value != null) {
        setState(() {
          document.expiryDate = GlobalMethods.dateToUIString(value);
        });
      }
    });
  }
}
