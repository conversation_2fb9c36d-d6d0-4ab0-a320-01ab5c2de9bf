import 'package:rooo_driver/features/documents/models/document_model.dart';
import 'package:rooo_driver/features/documents/models/driver_document_model.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/utils/Extensions/app_textfield.dart';

class DocumentCard extends StatelessWidget {
  final DocumentModel data;
  final DriverDocumentModel? uploadedDocument;
  final void Function() uploadDocument;

  final void Function() onExpiryDateSelection;

  const DocumentCard({
    super.key,
    required this.data,
    required this.uploadDocument,
    required this.onExpiryDateSelection,
    this.uploadedDocument,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: screenPadding,
      decoration: BoxDecoration(
        borderRadius: radius(),
        border: Border.all(color: AppColors.primaryColor(context)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            child: Text(
              data.name.toString(),
              style: AppTextStyles.title(),
              maxLines: 2,
            ),
          ),
          height10,
          height10,
          data.type == DRIVER_DOCUMENT_TYPE.text
              ? Column(
                  children: [
                    AppTextField(
                      textFieldType: TextFieldType.PHONE,
                      maxLength: 11,
                      initialValue: data.docText,
                      validator: (value) {
                        if (value?.isEmpty ?? true) {
                          return "Please enter valid information";
                        }
                        return null;
                      },
                      onChanged: (p0) {
                        data.docText = p0.trim();
                      },
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                      ],
                    ),
                    height10,
                  ],
                )
              : Column(
                  children: [
                    height10,
                    AppTextField(
                      textFieldType: TextFieldType.OTHER,
                      initialValue: data.expiryDate,
                      readOnly: true,
                      label: "Expiry Date",
                      onTap: onExpiryDateSelection,
                    ),
                    height10,
                    MaterialButton(
                      shape: RoundedRectangleBorder(
                        borderRadius: radius(),
                        side: BorderSide(
                          width: .2,
                          color: AppColors.primaryColor(context),
                        ),
                      ),
                      height: 150,
                      minWidth: double.infinity,
                      onPressed: uploadDocument,
                      child: Icon(Icons.add),
                    ),
                    height10,
                  ],
                ),
          AppButton(
            text: data.type == DRIVER_DOCUMENT_TYPE.text ? "Save" : "Upload",
            icon: data.type == DRIVER_DOCUMENT_TYPE.text
                ? null
                : Icon(Icons.upload),
            onPressed: () async {
              uploadDocument();
            },
          ),
        ],
      ),
    );
  }
}

class UploadedDocumentCard extends StatefulWidget {
  final DocumentModel data;
  final DriverDocumentModel? uploadedDocument;
  final void Function() editDocument;
  final void Function() onExpiryDateSelection;

  const UploadedDocumentCard({
    super.key,
    required this.data,
    required this.editDocument,
    this.uploadedDocument,
    required this.onExpiryDateSelection,
  });

  @override
  State<UploadedDocumentCard> createState() => _UploadedDocumentCardState();
}

class _UploadedDocumentCardState extends State<UploadedDocumentCard> {
  bool isEditing = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: screenPadding,
      decoration: BoxDecoration(
        borderRadius: radius(),
        border: Border.all(color: AppColors.primaryColor(context)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SizedBox(
                width: MediaQuery.of(context).size.width * .7,
                child: Text(
                  widget.data.name.toString(),
                  style: AppTextStyles.title(),
                  maxLines: 2,
                ),
              ),
              width10,
              Visibility(
                visible: widget.uploadedDocument!.isVerified == 1,
                child: Icon(Icons.verified_user, color: AppColors.greenColor),
              ),
              Visibility(
                visible:
                    ((widget.uploadedDocument!.rejectionText != null) &&
                    (widget.uploadedDocument?.isVerified == 2 ?? false)),
                child: Icon(Icons.close, color: Colors.red),
              ),
            ],
          ),
          Text(
            widget.uploadedDocument!.isVerified == 1
                ? "Approved"
                : widget.uploadedDocument!.isVerified == 2
                ? "Rejected"
                : widget.uploadedDocument!.isVerified == 3
                ? "Expired"
                : "Pending for approval",
            style: TextStyle(
              color: widget.uploadedDocument!.isVerified == 1
                  ? Colors.green
                  : widget.uploadedDocument!.isVerified == 2 ||
                        widget.uploadedDocument!.isVerified == 3
                  ? Colors.red
                  : Colors.orange,
            ),
          ),
          if ((widget.uploadedDocument!.rejectionText != null) &&
              (widget.uploadedDocument?.isVerified == 2))
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                "Rejection Reason: ${widget.uploadedDocument!.rejectionText}",
                style: TextStyle(color: Colors.red),
              ),
            ),
          widget.uploadedDocument!.expiryDate?.isEmpty ?? true
              ? const SizedBox()
              : Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: CustomText(
                    data:
                        "Expiry date : " +
                        GlobalMethods.dateToUIString(
                          GlobalMethods.apiStringToDate(
                            widget.uploadedDocument!.expiryDate!,
                          ),
                        ),
                  ),
                ),
          height10,
          widget.data.type == DRIVER_DOCUMENT_TYPE.text
              ? Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: AppTextField(
                            textFieldType: TextFieldType.PHONE,
                            maxLength: 11,
                            initialValue: widget.uploadedDocument!.docText,
                            readOnly: !isEditing,
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return "Please enter valid information";
                              }
                              return null;
                            },
                            onChanged: (p0) {
                              widget.uploadedDocument!.docText = p0.trim();
                            },
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                RegExp(r'[0-9]'),
                              ),
                            ],
                          ),
                        ),
                        // if (widget.uploadedDocument!.isVerified == 3 ||
                        //     (widget.uploadedDocument!.rejectionText != null) &&
                        //         (widget.uploadedDocument?.isVerified == 2))
                        IconButton(
                          icon: Icon(
                            isEditing ? Icons.save : Icons.edit,
                            color: AppColors.primaryColor(context),
                          ),
                          onPressed: () {
                            if (isEditing) {
                              if (widget.uploadedDocument!.docText?.length ==
                                  11) {
                                setState(() {
                                  isEditing = false;
                                  widget.uploadedDocument!.isVerified = 0;
                                  widget.uploadedDocument!.rejectionText = null;
                                });
                                widget.editDocument();
                              } else {
                                GlobalMethods.errorToast(
                                  context,
                                  "Please enter a valid 11-digit ABN",
                                );
                              }
                            } else {
                              setState(() {
                                isEditing = true;
                              });
                            }
                          },
                        ),
                      ],
                    ),
                    // if ((widget.uploadedDocument!.rejectionText != null) &&
                    //     (widget.uploadedDocument?.isVerified == 2))
                    //   Padding(
                    //     padding: const EdgeInsets.only(top: 8.0),
                    //     child: Text(
                    //       "Rejection Reason: ${widget.uploadedDocument!.rejectionText}",
                    //       style: TextStyle(color: Colors.red),
                    //     ),
                    //   ),
                    height10,
                  ],
                )
              : Column(
                  children: [
                    height10,

                    // (widget.uploadedDocument!.isVerified != 1)
                    // ?
                    AppTextField(
                      key: UniqueKey(),
                      textFieldType: TextFieldType.OTHER,
                      initialValue: GlobalMethods.dateToUIString(
                        GlobalMethods.apiStringToDate(
                          widget.uploadedDocument!.expiryDate!,
                        ),
                      ),
                      readOnly: true,
                      label: "Expiry Date",
                      onTap: widget.onExpiryDateSelection,
                    ),
                    // : const SizedBox(),
                    height10,
                    Stack(
                      children: [
                        InkWell(
                          onTap:
                              // widget.uploadedDocument?.isVerified == 1
                              //     ? null
                              //     :
                              widget.editDocument,
                          child: Align(
                            alignment: Alignment.center,
                            child: commonCachedNetworkImage(
                              widget.uploadedDocument!.driverDocument,
                              height: 200,
                              width: MediaQuery.of(context).size.width,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        // if (widget.uploadedDocument!.isVerified != 1)
                        Positioned(
                          top: 10,
                          right: 10,
                          child: IconButton(
                            style: IconButton.styleFrom(
                              backgroundColor: Colors.white,
                            ),
                            icon: Icon(Icons.edit, color: Colors.black),
                            onPressed: widget.editDocument,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
        ],
      ),
    );
  }
}
