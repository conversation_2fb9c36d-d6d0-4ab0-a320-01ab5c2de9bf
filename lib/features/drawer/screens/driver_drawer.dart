import 'package:flutter/foundation.dart';
import 'package:rooo_driver/features/care/screens/RideHelpScreen.dart';
import 'package:rooo_driver/features/care/screens/care_screen.dart';
import 'package:rooo_driver/features/drawer/widgets/drawer_container.dart';
import 'package:rooo_driver/features/drawer/widgets/drawer_container_vertical.dart';
import 'package:rooo_driver/features/earnings/screens/earning_screen.dart';
import 'package:rooo_driver/features/earnings/screens/earning_setting_screen.dart';
import 'package:rooo_driver/features/edit_profile/screens/edit_profile_screen.dart';
import 'package:rooo_driver/features/help/screens/help_screen2.dart';
import 'package:rooo_driver/features/inbox/screens/inbox_screen2.dart';
import 'package:rooo_driver/features/login/screens/roo_login_screen.dart';
import 'package:rooo_driver/features/opportunity/screens/opportunity_screen2.dart';
import 'package:rooo_driver/features/ride_flow/screen/mapbox_code.dart';
import 'package:rooo_driver/features/settings/map_setting/screens/account_setting_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/network/logs_screen.dart';
import 'package:rooo_driver/screens/NotificationScreen.dart';
import 'package:rooo_driver/screens/RefferalScreen.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

class DrawerDriver extends StatefulWidget {
  final bool is_profile_completed;
  final num driver_rating;

  final GlobalKey<ScaffoldState> scaffoldKey;

  const DrawerDriver({
    super.key,
    required this.scaffoldKey,
    required this.is_profile_completed,
    required this.driver_rating,
  });

  @override
  State<DrawerDriver> createState() => _DrawerDriverState();
}

class _DrawerDriverState extends State<DrawerDriver> {
  String? refferalCode = sharedPref.getString(REFFERALCODE).toString();
  Future<bool> driverStatus({
    int? status,
    bool isLogoutOperation = false,
  }) async {
    // appStore.setLoading(true);
    if (isLogoutOperation == true) {
      setState(() {});
    }
    Map req = {
      "status": "active",
      // removed
      // "is_online": status,
    };
    return await updateDriverPlayerIdApi(req)
        .then((value) {
          sharedPref.setInt(IS_ONLINE, value.data!.isOnline!);
          return true;
        })
        .catchError((error) {
          return false;
        });
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.scaffoldKey.currentState?.isDrawerOpen == true) {
        getAndApplyCounters();
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    return Drawer(
      width: screenWidth * .95,
      backgroundColor: Colors.black,
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: screenPaddingValue),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 35),
              Align(
                alignment: Alignment.centerRight,
                child: IconButton(
                  onPressed: () {
                    widget.scaffoldKey.currentState?.closeDrawer();
                  },
                  icon: Icon(Icons.close, color: Colors.white),
                ),
              ),
              Center(
                child: Observer(
                  builder: (context) {
                    return Column(
                      children: [
                        inkWellWidget(
                          onTap: () {
                            // toast(widget.googleId.toString());
                            GlobalMethods.handleInCompleteProfile(
                              context: context,
                              positiveAction: () {
                                GlobalMethods.pushScreen(
                                  context: context,
                                  screen: EditProfileScreen(
                                    isFromDahboard: false,
                                  ),
                                  screenIdentifier:
                                      ScreenIdentifier.EditProfileScreen,
                                );
                              },
                              is_profile_completed: widget.is_profile_completed,
                              // :widget.googleId,facebookId: widget.facebookId,appleId: widget.appleId
                            );

                            // launchScreen(context, EditProfileScreen());
                          },
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Stack(
                                alignment: Alignment.bottomRight,
                                children: [
                                  CircleAvatar(
                                    radius: 38,
                                    child: CircleAvatar(
                                      radius: 36,
                                      child: ClipRRect(
                                        borderRadius: radius(200),
                                        child: commonCachedNetworkImage(
                                          appStore.userProfile
                                              .validate()
                                              .validate(),
                                          height: 70,
                                          width: 70,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                  ),
                                  CircleAvatar(
                                    radius: 15,
                                    backgroundColor: Colors.white,
                                    child: IconButton(
                                      onPressed: () {
                                        GlobalMethods.handleInCompleteProfile(
                                          context: context,
                                          positiveAction: () {
                                            GlobalMethods.pushScreen(
                                              context: context,
                                              screen: EditProfileScreen(
                                                isFromDahboard: false,
                                              ),
                                              screenIdentifier: ScreenIdentifier
                                                  .EditProfileScreen,
                                            );
                                          },
                                          is_profile_completed:
                                              widget.is_profile_completed,
                                          // :widget.googleId,facebookId: widget.facebookId,appleId: widget.appleId
                                        );
                                      },
                                      icon: Icon(
                                        Icons.edit,
                                        color: AppColors.primaryBlackColor,
                                        size: 15,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(width: 20),
                              SizedBox(
                                width: 140,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // sharedPref.getString(LOGIN_TYPE) !=
                                    //             'mobile' &&
                                    //         sharedPref.getString(LOGIN_TYPE) !=
                                    //             null
                                    //     ? Text(
                                    //         sharedPref
                                    //             .getString(USER_NAME)
                                    //             .validate(),
                                    //         maxLines: 1,
                                    //         overflow: TextOverflow.ellipsis,
                                    //         style: TextStyle(
                                    //           color: Colors.white,
                                    //           fontSize: 18,
                                    //           fontWeight: FontWeight.bold,
                                    //         ),
                                    //       )
                                    //     :
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          sharedPref
                                                  .getString(FIRST_NAME)
                                                  .validate() +
                                              ' ' +
                                              sharedPref
                                                  .getString(LAST_NAME)
                                                  .validate(),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        RatingBar.builder(
                                          itemSize: 25,
                                          ignoreGestures: true,
                                          initialRating: widget.driver_rating
                                              .roundToDouble(),
                                          direction: Axis.horizontal,
                                          glow: false,
                                          allowHalfRating: false,
                                          wrapAlignment:
                                              WrapAlignment.spaceBetween,
                                          itemBuilder: (context, _) => Icon(
                                            Icons.star,
                                            color: Colors.amber,
                                          ),
                                          onRatingUpdate: (rating) {},
                                          unratedColor: Colors.grey,
                                        ),
                                      ],
                                    ),
                                    // SizedBox(height: 4),
                                    // Text(
                                    //   appStore.userEmail,
                                    //   maxLines: 2,
                                    //   overflow: TextOverflow.ellipsis,
                                    //   style: TextStyle(color: Colors.white),
                                    // ),

                                    // email_verified_at!=null?CustomText(data: "Verified",color: Colors.blue,):CustomText(data: "unverified",color: Colors.red,)                                ],
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        // ClipRRect(
                        //   borderRadius: radius(),
                        //   child: commonCachedNetworkImage(
                        //       appStore.userProfile.validate().validate(),
                        //       height: 65,
                        //       width: 65,
                        //       fit: BoxFit.cover),
                        // ),
                      ],
                    );
                  },
                ),
              ),
              height10,
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if(areLogsEnabled)
                  ListTile(title: Text("LOGS",style: TextStyle(color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  )),
                  onTap: () {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => AppLogs()));
                  },
                  ),
                  
                  DrawerContainer(
                    height: 70,
                    width: double.infinity,
                    is_profile_completed: widget.is_profile_completed,
                    screenIdentifier: ScreenIdentifier.inboxScreen,
                    name: language.inBoxTxt,
                    iconData: Icons.home,
                    screenName: InboxScreen(),
                    subTitle: ValueListenableBuilder<int>(
                      valueListenable: GlobalState.global_inbox_count,
                      builder: (context, value, child) {
                        if (value > 0) {
                          return CircleAvatar(
                            radius: 15,
                            backgroundColor: Colors.red,
                            child: CustomText(
                              data: value.toString(),
                              color: Colors.white,
                              size: 12,
                            ),
                          );
                        }
                        return SizedBox();
                      },
                    ),
                  ),
                  height10,
                  DrawerContainer(
                    height: 70,
                    width: double.infinity,
                    is_profile_completed: widget.is_profile_completed,
                    screenIdentifier: ScreenIdentifier.HelpScreen,
                    iconData: Icons.person,
                    name: language.helpTxt,
                    screenName: HelpScreen(),
                  ),
                  height10,
                  DrawerContainer(
                    height: 70,
                    width: double.infinity,
                    is_profile_completed: widget.is_profile_completed,
                    screenIdentifier: ScreenIdentifier.NewCareScreen,
                    name: language.roooCare,
                    iconData: Icons.support_agent,
                    screenName: NewCareScreen(isPendingCare: true),
                    subTitle: ValueListenableBuilder<int>(
                      valueListenable: GlobalState.global_care_count,
                      builder: (context, value, child) {
                        if (value > 0) {
                          return CircleAvatar(
                            radius: 15,
                            backgroundColor: Colors.red,
                            child: CustomText(
                              data: value.toString(),
                              color: Colors.white,
                              size: 12,
                            ),
                          );
                        }
                        return SizedBox();
                      },
                    ),
                  ),
                  height20,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: DrawerContainerVertical(
                          height: 140,
                          is_profile_completed: widget.is_profile_completed,
                          screenIdentifier: ScreenIdentifier.ReferralScreen,
                          name: language.referralTxt,
                          iconData: Icons.content_paste_go_sharp,
                          screenName: ReferralScreen(),
                        ),
                      ),
                      width20,
                      Expanded(
                        child: DrawerContainerVertical(
                          height: 140,
                          is_profile_completed: widget.is_profile_completed,
                          screenIdentifier: ScreenIdentifier.OpportunityScreen,
                          name: language.opportunityTxt,
                          iconData: Icons.card_travel_sharp,
                          screenName: OpportunityScreen(),
                          subTitle: ValueListenableBuilder<int>(
                            valueListenable:
                                GlobalState.global_opportunity_count,
                            builder: (context, value, child) {
                              if (value > 0) {
                                return CircleAvatar(
                                  radius: 15,
                                  backgroundColor: Colors.red,
                                  child: CustomText(
                                    data: value.toString(),
                                    color: Colors.white,
                                    size: 12,
                                  ),
                                );
                              }
                              return SizedBox();
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                  height20,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: DrawerContainerVertical(
                          height: 140,
                          is_profile_completed: widget.is_profile_completed,
                          screenIdentifier: ScreenIdentifier.SettingScreen,
                          name: language.setting,
                          iconData: Icons.settings,
                          screenName: AccountSettingScreen(),
                        ),
                      ),
                      width20,
                      Expanded(
                        child: DrawerContainerVertical(
                          height: 140,
                          is_profile_completed: widget.is_profile_completed,
                          screenIdentifier: ScreenIdentifier.EarningScreen,
                          name: "Earnings",
                          iconData: Icons.attach_money_outlined,
                          screenName: EarningSettingScreen(),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              height10,
              DrawerContainer(
                height: 70,
                width: double.infinity,
                is_profile_completed: widget.is_profile_completed,
                screenIdentifier: ScreenIdentifier.NotificationScreen,
                iconData: Icons.notifications,
                name: "Notifications",
                screenName: NotificationScreen(),
                subTitle: ValueListenableBuilder<int>(
                  valueListenable: GlobalState.global_notification_count,
                  builder: (context, value, child) {
                    if (value > 0) {
                      return CircleAvatar(
                        radius: 15,
                        backgroundColor: Colors.red,
                        child: CustomText(
                          data: value.toString(),
                          color: Colors.white,
                          size: 12,
                        ),
                      );
                    }
                    return SizedBox();
                  },
                ),
              ),
              height10,
              Visibility(
                visible: !widget.is_profile_completed,
                child: AppButton(
                  text: "Log out",
                  backgroundColor: Colors.red,
                  onPressed: () async {
                    GlobalMethods.showConfirmationDialog(
                      context: context,
                      onPositiveAction: () {
                        _logOut();
                      },
                      title: language.areYouSureYouWantToLogoutThisApp,
                    );
                  },
                ),
              ),
              height20,
              height5,
            ],
          ),
        ),
      ),
    );
  }

  _logOut() async {
    await Future.delayed(Duration(milliseconds: 200));

    GlobalMethods.showActivity(context: context, title: "Logging out...");

    await driverStatus(status: 0, isLogoutOperation: true);
    await Future.delayed(Duration(milliseconds: 500));
    await logout();
    GlobalState.mqttClient?.disconnect();
    Navigator.pop(context);
    GlobalMethods.pushAndRemoveAll(
      context: getContext,
      screen: LoginScreen(),
      screenIdentifier: ScreenIdentifier.LoginScreen,
    );
  }
}

Future<void> getAndApplyCounters() async {
  var response = await getCounterData();

  if (response.status) {
    GlobalState.todaysEarningValue.value = response.data!.totalEarnings;
    GlobalState.global_care_count.value =
        response.data!.careCount + response.data!.rideIssuesCoount;
    GlobalState.global_ride_issues_count.value =
        response.data!.rideIssuesCoount;
    GlobalState.global_inbox_count.value = response.data!.inboxCount;
    GlobalState.global_opportunity_count.value =
        response.data!.opportunityCount;
    GlobalState.global_notification_count.value =
        response.data!.notificationCount;
    GlobalState.global_drawer_count.value =
        GlobalState.global_care_count.value +
        GlobalState.global_inbox_count.value +
        GlobalState.global_opportunity_count.value +
        GlobalState.global_notification_count.value +
        GlobalState.global_ride_issues_count.value;
  }
}
