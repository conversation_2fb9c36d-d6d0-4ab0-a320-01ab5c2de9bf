import 'package:rooo_driver/features/earnings/model/earnings_model_responce.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/EarningListModelWeek.dart';
import 'package:rooo_driver/screens/BankAndWalletScreen.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class WeeklyEarningScreen extends StatefulWidget {
  @override
  WeeklyEarningScreenState createState() => WeeklyEarningScreenState();
}

class WeeklyEarningScreenState extends State<WeeklyEarningScreen> {
  EarningListModelWeek? earningListModelWeek;
  List<WeekReport> weekReport = [];
  RideDetailsReportModel? rideDetailsReports;
  num totalRide = 0;
  num totalReferrals = 0;
  num TotalEarnings = 0;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    appStore.setLoading(true);
    Map req = {
      "type": "week",
    };
    await earningList(req: req).then((value) {
      appStore.setLoading(false);

      totalRide = value.totalRideFare ?? 0.0;
      totalReferrals = value.totalOfferReferral ?? 0.0;
      TotalEarnings = value.totalEarnings ?? 0.0;

      weekReport = value.weekReport ?? [];

      rideDetailsReports = value.rideDetails;
      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);

    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (_) {
        return Stack(
          children: [
            SingleChildScrollView(
              padding: screenPadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.all(16),
                    height: 350,
                    child: SfCartesianChart(
                      backgroundColor: Theme.of(context).brightness == Brightness.dark
                          ? Colors.black
                          : Colors.white,
                      primaryXAxis: CategoryAxis(
                        labelStyle: TextStyle(color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black),
                        axisLine: AxisLine(color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black),
                        majorGridLines: MajorGridLines(color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black),
                      ),
                      primaryYAxis: NumericAxis(
                        labelStyle: TextStyle(color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black),
                        axisLine: AxisLine(color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black),
                        majorGridLines: MajorGridLines(color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black),
                      ),
                      title: ChartTitle(
                          text: "Weekly earnings",
                          textStyle: TextStyle(color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black)),
                      tooltipBehavior: TooltipBehavior(
                        enable: true,
                        duration: 3000,
                        activationMode: ActivationMode.singleTap,
                      ),
                      series: <CartesianSeries>[
                        StackedColumnSeries<WeekReport, String>(
                          name: "Amount earned (AUD)",
                          enableTooltip: true,
                          markerSettings: MarkerSettings(isVisible: true),
                          dataSource: weekReport,
                          xValueMapper: (WeekReport exp, _) => exp.day,
                          yValueMapper: (WeekReport exp, _) => exp.amount,
                          color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                        ),
                      ],
                    ),
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                            color: Colors.black12,
                            blurRadius: 10.0,
                            spreadRadius: 0),
                      ],
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.black
                          : Colors.white,
                      borderRadius: BorderRadius.circular(defaultRadius),
                    ),
                  ),
                  SizedBox(height: 16),
                  SizedBox(height: 16),
                  earningText(title: language.totalRide, amount: totalRide),
                  SizedBox(height: 16),
                  earningText(
                      title: language.totalReferralsTxt,
                      amount: totalReferrals),
                  SizedBox(height: 16),
                  Divider(),
                  earningText(
                      title: language.totalEarning, amount: TotalEarnings),
                  height20,
                  height20,
                  ListView.separated(
                      physics: NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        DayRideInfo dayData = rideDetailsReports!.days![index];

                        return ExpansionTile(
                          collapsedBackgroundColor: Theme.of(context).brightness ==
                                  Brightness.dark
                              ? Colors.grey.shade400
                              : Colors.grey.shade100,
                          backgroundColor: Theme.of(context).brightness ==
                                  Brightness.dark
                              ? Colors.grey.shade400
                              : Colors.grey.shade200,
                          collapsedIconColor: Colors.black,
                          title: Text(
                            dayData.name ?? "",
                            style: TextStyle(color: Colors.black),
                          ),
                          children: [
                            dayData.rideInformation!.isEmpty
                                ? Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Text("No data",
                                        style: TextStyle(color: Colors.black)),
                                  )
                                : ListView.separated(
                                    physics: NeverScrollableScrollPhysics(),
                                    shrinkWrap: true,
                                    itemCount:
                                        dayData.rideInformation?.length ?? 0,
                                    itemBuilder: (context, index) {
                                      Ride rideData =
                                          dayData.rideInformation![index];

                                      return Padding(
                                        padding: screenPadding / 2,
                                        child: Column(
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text("Pickup",
                                                    style: TextStyle(
                                                        color: Colors.black)),
                                                width20,
                                                width20,
                                                SizedBox(
                                                    width: 200,
                                                    child: Text(
                                                      rideData.startAddress ??
                                                          "",
                                                      style: TextStyle(
                                                          color: Colors.black),
                                                    ))
                                              ],
                                            ),
                                            height10,
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text("Drop",
                                                    style: TextStyle(
                                                        color: Colors.black)),
                                                width20,
                                                width20,
                                                SizedBox(
                                                    width: 200,
                                                    child: Text(
                                                      rideData.endAddress ?? "",
                                                      style: TextStyle(
                                                          color: Colors.black),
                                                    ))
                                              ],
                                            ),
                                            height10,
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text("Earnings",style: TextStyle(color: Colors.black),),
                                                width20,
                                                width20,
                                                SizedBox(
                                                    width: 200,
                                                    child: Text(
                                                      "AUD " +
                                                          rideData.commission!
                                                              .toStringAsFixed(
                                                                  2),
                                                      style:
                                                          AppTextStyles.text(color: Colors.black),
                                                    ))
                                              ],
                                            )
                                          ],
                                        ),
                                      );
                                      ;
                                    },
                                    separatorBuilder: (context, index) =>
                                        Divider(),
                                  )
                          ],
                        );
                      },
                      separatorBuilder: (context, index) => height10,
                      itemCount: rideDetailsReports?.days?.length ?? 0),
                  Align(
                      alignment: Alignment.center,
                      child: AppButton(
                          text: language.cashOutTxt,
                          onPressed: () async  {
                            GlobalMethods.pushScreen(
                                context: context,
                                screen: BankAndWalletScreen(
                                  isWalletScreen: true,
                                ),
                                screenIdentifier:
                                    ScreenIdentifier.BankAndWalletScreen);
                            // launchScreen(
                            //     context,
                            //     BankAndWalletScreen(
                            //       isWalletScreen: true,
                            //     ));
                          })
                      // AppButtonWidget(
                      //   shapeBorder: RoundedRectangleBorder(
                      //       borderRadius: BorderRadius.circular(10)),
                      //   padding: EdgeInsets.symmetric(horizontal: 40),
                      //   text: language.cashOutTxt,
                      //   onTap: () {
                      //     launchScreen(
                      //         context,
                      //         BankAndWalletScreen(
                      //           isWalletScreen: true,
                      //         ));
                      //   },
                      // ),
                      )
                ],
              ),
            ),
            Visibility(
              visible: appStore.isLoading,
              child: loaderWidget(),
            )
          ],
        );
      },
    );
  }
}