import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:rooo_driver/components/faq_card.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/help/models/help_model.dart';
import 'package:rooo_driver/features/help/models/help_response_model.dart';
import 'package:rooo_driver/features/help/screens/help_detail_screen2.dart';

class HelpScreen extends StatefulWidget {
  const HelpScreen({super.key});

  @override
  State<HelpScreen> createState() => _HelpScreenState();
}

class _HelpScreenState extends State<HelpScreen> {
  int _currentPage = 1;
  int _totalPage = 1;
  ScrollController _scrollController = ScrollController();

  List<WebviewDataModel> _helpList = [];
  String _emptyMesssage = "";

  _onScrolling() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (_currentPage < _totalPage) {
          _currentPage++;

          _getHelpList(currentPage: _currentPage);
        }
      }
    });
  }

  _getHelpList({required int currentPage}) {
    BlocProvider.of<HelpCubit>(context).getHelp(current_page: currentPage);
  }

  _onDataLoaded({required WebViewDataResponseModel helpResponseModel}) {
    _currentPage = helpResponseModel.pagination?.currentPage ?? 1;
    _totalPage = helpResponseModel.pagination?.totalPages ?? 1;

    _helpList = helpResponseModel.data ?? [];
    _emptyMesssage = helpResponseModel.message.toString();
  }

  _onPullToRefresh() {
    _currentPage = 1;
    _init();
  }

  _init() {
    _onScrolling();
    _getHelpList(currentPage: _currentPage);
  }

  _dispose() {
    _scrollController.dispose();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: language.helpTxt),
      body: BlocConsumer<HelpCubit, HelpState>(
        listener: (context, state) {
          if (state is HelpLoadedState) {
            _onDataLoaded(helpResponseModel: state.helpResponseModel);
          }
          if (state is HelpErrorState) {
            GlobalMethods.errorToast(context, state.message);
          }
        },
        builder: (context, state) {
          return ScreenBody(
              onPullToRefresh: () async => await _onPullToRefresh(),
              isLoading: state is HelpLoadingState,
              isEmpty: _helpList.isEmpty,
              emptyMessage: _emptyMesssage,
              child: Padding(
                padding: screenPadding,
                child: AnimationLimiter(
                  child: ListView.separated(
                    shrinkWrap: true,
                    separatorBuilder: (context, index) => const Divider(),
                    itemCount: _helpList.length,
                    itemBuilder: (BuildContext context, int index) {
                      WebviewDataModel data = _helpList[index];
                
                      return AnimationConfiguration.staggeredList(
                        position: index,
                        duration: const Duration(milliseconds: 1000),
                        child: SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: WebViewCard(
                              onTap: () {
                                GlobalMethods.pushScreen(
                                    context: context,
                                    screen: HelpDetailsScreen(
                                        title: data.title, id: data.id),
                                    screenIdentifier:
                                        ScreenIdentifier.HelpDetailsScreen);
                
                                // launchScreen(
                                //     context,
                                //     HelpDetailsScreen(
                                //         title: data.title, id: data.id));
                              },
                              data: data,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ));
        },
      ),
    );
  }
}
