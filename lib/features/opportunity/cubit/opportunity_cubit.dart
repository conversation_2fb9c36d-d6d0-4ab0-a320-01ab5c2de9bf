import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/features/opportunity/repository/opportunity_repository.dart';
import 'package:rooo_driver/global/models/current_ride_response_model.dart';
import 'package:rooo_driver/model/OpposrtunityResponseModel.dart';
import 'package:rooo_driver/network/RestApis.dart';

abstract class OpportunityState {}

class OpportunityInitState extends OpportunityState {}

class OpportunityLoadingState extends OpportunityState {}

class OpportunityAcceptedState extends OpportunityState {
  final String message;

  OpportunityAcceptedState({required this.message});
}

class OpportunityCanceledState extends OpportunityState {
  final String message;

  OpportunityCanceledState({required this.message});
}

class StartOpportunityRideState extends OpportunityState {}
// class CurrentlyRidingStatus extends OpportunityState {
//     final bool isCurrentlyRiding;

//   CurrentlyRidingStatus({required this.isCurrentlyRiding});

// }

class OpportunityDetailData extends OpportunityState {
  final String data;

  OpportunityDetailData({required this.data});
}

class OpportunityLoadedState extends OpportunityState {
  final OpportunityResponseModel opportunityResponseModel;

  OpportunityLoadedState({
    required this.opportunityResponseModel,
  });
}

class OpportunityErrorState extends OpportunityState {
  final String message;

  OpportunityErrorState({required this.message});
}

class OpportunityCubit extends Cubit<OpportunityState> {
  OpportunityCubit() : super(OpportunityInitState());

  OpportunityRepository opportunityRepository = OpportunityRepository();

  void getOpportunity({
    required int currentPage,
    required String status,
  }) async {
    emit(OpportunityLoadingState());
    await opportunityRepository
        .getNewOpportunityListApi(currentPage: currentPage, status: status)
        .then((value) {
      emit(OpportunityLoadedState(opportunityResponseModel: value));
    }).onError((error, stackTrace) {
      emit(OpportunityErrorState(message: "Server error"));
    });
  }

  void acceptOpportunity({required Map request}) async {
    emit(OpportunityLoadingState());
    await opportunityRepository
        .acceptOpportunity(request: request)
        .then((value) {
      if (value.status ?? true) {
        emit(OpportunityAcceptedState(message: value.messasge.toString()));
      } else {
        emit(OpportunityErrorState(message: value.messasge.toString()));
      }
    }).onError((error, stackTrace) {
      emit(OpportunityErrorState(message: "Server error"));
    });
  }

  Future<bool?> checkCurrentlyRiding() async {
    emit(OpportunityLoadingState());
    CurrentRideResponseModel ride =
        await opportunityRepository.checkCurrentlyRiding();
    emit(OpportunityInitState());

    if (ride.data!.onRideRequest == null) {
      // emit(CurrentlyRidingStatus(isCurrentlyRiding: false));
      return false;
    } else if (ride.data!.onRideRequest != null) {
      // emit(CurrentlyRidingStatus(isCurrentlyRiding: true));
      return true;
    } else {
      return null;
    }
  }

  void startOpportunityRide({required Map request, required int rideId}) async {
    emit(OpportunityLoadingState());
    await opportunityRepository
        .startOpportunityRide(request: request, rideId: rideId)
        .then((value) {
      if (value.status) {
        emit(StartOpportunityRideState());
      } else {
        emit(OpportunityErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(OpportunityErrorState(message: "Server error"));
    });
  }

  void cancelOpportunityRide({
    required Map request,
  }) async {
    emit(OpportunityLoadingState());

    await cancelOpportunity(request: request, rideId: request['id'])
        .then((value) {
      if (value.status) {
        emit(OpportunityCanceledState(message: value.message));
      } else {
        emit(OpportunityErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(OpportunityErrorState(message: "Server error"));
    });
  }
}
