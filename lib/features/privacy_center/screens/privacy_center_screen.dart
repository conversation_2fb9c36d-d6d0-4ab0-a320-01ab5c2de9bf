import 'package:rooo_driver/features/privacy_center/cubit/push_info_pref_cubit.dart';
import 'package:rooo_driver/features/privacy_center/models/privacy_center_model.dart';
import 'package:rooo_driver/features/privacy_center/models/privacy_center_response_model.dart';
import 'package:rooo_driver/features/privacy_center/screens/privacy_question_screen.dart';
import 'package:rooo_driver/features/privacy_center/widgets/privacy_blog_card.dart';

import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';

import 'package:rooo_driver/screens/DeleteAccountScreen.dart';
import 'package:rooo_driver/screens/PrivacyPolicyScreen.dart';
import 'package:rooo_driver/screens/blogsDetailScreen.dart';

class PrivacyCenterScreen extends StatefulWidget {
  // final DriverBankInfoModel? bankInfoModel;
  const PrivacyCenterScreen();

  @override
  State<PrivacyCenterScreen> createState() => _PrivacyCenterScreenState();
}

class _PrivacyCenterScreenState extends State<PrivacyCenterScreen> {
  int _currentPage = 1;
  int _totalPage = 1;
  ScrollController _scrollController = ScrollController();

  PrivacyCeterModel? _privacyCenterModel;

  String _emptyMesssage = "";
  // ValueNotifier<BankInfoTypeModel> _isSelectedService =
  //     ValueNotifier(BankInfoTypeModel(id: -1, service_image: "", name: ""));

  // BankListResponseModel

  // TextEditingController _nameController = TextEditingController();
  // // TextEditingController _transmissionController = TextEditingController();
  // TextEditingController _plateNumberController = TextEditingController();

  // ValueNotifier<File> _bankInfoImage = ValueNotifier(File("path"));

  // _saveBankInfo({required MultipartRequest multiPartRequest}) {
  //   BlocProvider.of<BankInfoCubit>(context)
  //       .saveBankInfo(multiPartRequest: multiPartRequest);
  // }

  _getPrivacyCenter() {
    BlocProvider.of<PrivacyCenterCubit>(context).getPrivacyCenterData();
  }

  _onDataLoaded({required PrivacyCenterResponseModel privacy_center}) {
    _privacyCenterModel = privacy_center.data;
    _emptyMesssage = privacy_center.message;
  }

  _onPullToRefresh() {
    _currentPage = 1;
    _init();
  }

  _init() {
    // if (widget.bankInfoModel != null) {
    //   _nameController.text = widget.bankInfoModel!.name!;
    //   _selected_transmission.value = widget.bankInfoModel?.transmission??_tranmission_list[0];
    //   _plateNumberController.text = widget.bankInfoModel!.transmission;
    //   _isSelectedService.value.id=widget.bankInfoModel!.serviceId;
    // }
    _getPrivacyCenter();
  }

  _dispose() {
    _scrollController.dispose();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = AppColors.primaryColor(context);
    
    return BlocConsumer<PrivacyCenterCubit, PrivacyCenterState>(
      listener: (context, state) {
        if (state is PrivacyCenterLoadedState) {
          _onDataLoaded(privacy_center: state.privacyCenter);
        }
        if (state is PrivacyCenterErrorState) {
              GlobalMethods.errorToast(context, state.message);
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: RoooAppbar(title: "Privacy Center"),
          body: ScreenBody(
            onPullToRefresh: () async => await _onPullToRefresh(),
            isLoading: state is PrivacyCenterLoadingState,
            isEmpty: _privacyCenterModel == null,
            emptyMessage: _emptyMesssage,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Privacy Blogs Section
                  if (_privacyCenterModel?.blogs.isNotEmpty ?? false) ...[
                    Padding(
                      padding: screenPadding,
                      child: Text(
                        "Privacy Blogs",
                        style: AppTextStyles.header().copyWith(
                          fontSize: 20,
                        ),
                      ),
                    ),
                    height10,
                    SizedBox(
                      height: MediaQuery.of(context).size.height * .32,
                      child: ListView.separated(
                        padding: EdgeInsets.symmetric(horizontal: screenPaddingValue),
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (context, index) {
                          BlogModel data = _privacyCenterModel!.blogs[index];
                          return PrivacyBlogCard(data: data);
                        },
                        separatorBuilder: (context, index) => width10,
                        itemCount: _privacyCenterModel?.blogs.length ?? 0,
                      ),
                    ),
                  ],
                  height10,

                  // Promotions Section
                  Padding(
                    padding: screenPadding,
                    child: Container(
                      decoration: BoxDecoration(
                        color: primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(left: 16, right: 16, top: 12, bottom: 4),
                            child: Text(
                              "Promotions",
                              style: AppTextStyles.header().copyWith(
                                fontSize: 18,
                                color: primaryColor,
                              ),
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              GlobalMethods.pushScreen(
                                context: context,
                                screen: BlogsDetailScreen(
                                  appBarTitle: "Promotion detail",
                                  id: _privacyCenterModel!.promo.id,
                                  title: _privacyCenterModel?.promo.title ?? "",
                                ),
                                screenIdentifier: ScreenIdentifier.BlogsDetailScreen,
                              );
                            },
                            child: Container(
                              padding: EdgeInsets.all(12),
                              child: Row(
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(10),
                                    decoration: BoxDecoration(
                                      color: primaryColor.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      Icons.add_to_home_screen_outlined,
                                      color: primaryColor,
                                      size: 20,
                                    ),
                                  ),
                                  width15,
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          _privacyCenterModel?.promo.title ?? "",
                                          style: AppTextStyles.header().copyWith(
                                            fontSize: 16,
                                          ),
                                        ),
                                        if (_privacyCenterModel?.promo.subTitle?.isNotEmpty ?? false) ...[
                                          height5,
                                          Text(
                                            _privacyCenterModel?.promo.subTitle ?? "",
                                            style: TextStyle(
                                              color: Theme.of(context).hintColor,
                                              fontSize: 14,
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                  Icon(
                                    Icons.arrow_forward_ios_outlined,
                                    size: 16,
                                    color: primaryColor,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  height10,

                  // Tracking Section
                  Padding(
                    padding: screenPadding,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).cardColor,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).dividerColor,
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(left: 16, right: 16, top: 12, bottom: 4),
                            child: Text(
                              "Tracking",
                              style: AppTextStyles.header().copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              GlobalMethods.pushScreen(
                                context: context,
                                screen: BlogsDetailScreen(
                                  appBarTitle: "Tracking detail",
                                  id: _privacyCenterModel!.tracking.id,
                                  title: _privacyCenterModel?.tracking.title ?? "",
                                ),
                                screenIdentifier: ScreenIdentifier.BlogsDetailScreen,
                              );
                            },
                            child: Container(
                              padding: EdgeInsets.all(12),
                              child: Row(
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).dividerColor.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      Icons.track_changes,
                                      color: Theme.of(context).hintColor,
                                    ),
                                  ),
                                  width15,
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          _privacyCenterModel?.tracking.title ?? "",
                                          style: AppTextStyles.header().copyWith(
                                            fontSize: 16,
                                          ),
                                        ),
                                        if (_privacyCenterModel?.tracking.subTitle?.isNotEmpty ?? false) ...[
                                          height5,
                                          Text(
                                            _privacyCenterModel?.tracking.subTitle ?? "",
                                            style: TextStyle(
                                              color: Theme.of(context).hintColor,
                                              fontSize: 14,
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                  Icon(
                                    Icons.arrow_forward_ios_outlined,
                                    size: 16,
                                    color: Theme.of(context).hintColor,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  height15,

                  // Quick Actions
                  Padding(
                    padding: screenPadding,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Quick Actions",
                          style: AppTextStyles.header().copyWith(
                            fontSize: 18,
                          ),
                        ),
                        height15,
                        Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).cardColor,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Theme.of(context).dividerColor,
                              width: 1,
                            ),
                          ),
                          child: Column(
                            children: [
                              _buildQuickActionItem(
                                context: context,
                                icon: Icons.privacy_tip_outlined,
                                title: "Check privacy policy",
                                onTap: () {
                                  GlobalMethods.pushScreen(
                                    context: context,
                                    screen: PrivacyPolicyScreen(),
                                    screenIdentifier: ScreenIdentifier.PrivacyPolicyScreen,
                                  );
                                },
                              ),
                              Divider(height: 1),
                              _buildQuickActionItem(
                                context: context,
                                icon: Icons.help_outline,
                                title: "Do you have any privacy concerns?",
                                onTap: () {
                                  GlobalMethods.pushScreen(
                                    context: context,
                                    screen: PrivacyCenterQuestionScreen(),
                                    screenIdentifier: ScreenIdentifier.PrivacyPolicyScreen,
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        height20,
                        InkWell(
                          onTap: () {
                            GlobalMethods.pushScreen(
                              context: context,
                              screen: DeleteAccountScreen(),
                              screenIdentifier: ScreenIdentifier.DeleteAccountScreen,
                            );
                          },
                          child: Container(
                            padding: EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.red.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.delete_forever_outlined,
                                  color: Colors.red,
                                ),
                                width15,
                                Text(
                                  "Delete account",
                                  style: AppTextStyles.header().copyWith(
                                    fontSize: 16,
                                    color: Colors.red,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  height20,
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickActionItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Theme.of(context).hintColor,
              ),
            ),
            width15,
            Expanded(
              child: Text(
                title,
                style: AppTextStyles.header().copyWith(
                  fontSize: 16,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios_outlined,
              size: 16,
              color: Theme.of(context).hintColor,
            ),
          ],
        ),
      ),
    );
  }
}
