import 'package:flutter/gestures.dart';
import 'package:pinput/pinput.dart';
import 'package:rooo_driver/features/ride_flow/screen/ride_screen.dart';
import 'package:rooo_driver/features/verify_otp/screens/otp_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/registration/cubit/registration_cubit.dart';
import 'package:rooo_driver/model/AppSettingModel.dart';
import 'package:rooo_driver/screens/PrivacyPolicyScreen.dart';
import 'package:rooo_driver/screens/TermsAndConditionsScreen.dart';

import 'package:rooo_driver/utils/Extensions/app_textfield.dart';

class CompleteProfileScreen extends StatefulWidget {
  final String? userName;
  final String first_name;
  final String last_name;

  final int id;
  final String? email;
  final String? gender;
  final String? otherGenderText;

  CompleteProfileScreen({
    this.userName,
    this.email,
    required this.first_name,
    required this.last_name,
    this.gender,
    this.otherGenderText,
    required this.id,
  });

  @override
  State<CompleteProfileScreen> createState() => _CompleteProfileScreenState();
}

class _CompleteProfileScreenState extends State<CompleteProfileScreen> {
  ValueNotifier<int> currentIndex = ValueNotifier(0);

  ValueNotifier<bool> isAcceptedTermsAndConditions = ValueNotifier(false);

  int _selectedVehicleId = -1;

  List<int> filledIndexes = [];

  final _passwordController = TextEditingController();
  bool _showPassword = false;

  FocusNode passwordFocus = FocusNode();

  final FocusNode _referralFocusNode = FocusNode();

  ValueNotifier<bool> _isReferralCodeVerified = ValueNotifier(false);
  ValueNotifier<bool> _isUserWantsToEnterReferralCode = ValueNotifier(false);

  //  VehicleResponseModel? vehicleResponseModel;

  /////////////////////////////////////////////////////////
  //gender and terms and conditions
  List<String> genderList = [SELECT, MALE, FEMALE, PREFER_NOT_TO_SAY, OTHER];
  ValueNotifier<String> _selectedGender = ValueNotifier(SELECT);
  String _otherGender = "";

  ValueNotifier<XFile> profileImage = ValueNotifier(XFile(""));

  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  // final _userNameController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  String _countryCode = "+61";
  String _countryIsoCode = defaultCountryIsoCode;
  ValueNotifier<bool> _isVerifiedNumber = ValueNotifier(false);

  final _carModelController = TextEditingController();

  final carProductionYearController = TextEditingController();

  final _carPlateNumberController = TextEditingController();

  final carColorController = TextEditingController();

  final dobController = TextEditingController();
  final _referralController = TextEditingController();

  List<GlobalKey<FormState>> _formKeys = [
    GlobalKey<FormState>(),
    GlobalKey<FormState>(),
    GlobalKey<FormState>(),
  ];

  DateTime firstDOB = DateTime.now().subtract(Duration(days: 365 * 90));
  DateTime lastDOB = DateTime.now().subtract(Duration(days: 365 * 20));
  DateTime selectedDOB = DateTime.now().subtract(Duration(days: 365 * 20));

  bool _isEmailOTPSent = true;
  bool _isEmailOTPVerified = true;
  TextEditingController _emailOTPController = TextEditingController(
    text: "123456",
  );
  Timer? _emailVerificationTimer;
  int _emailOTPCountdownTimer = 60;
  bool _isNotifiedForTheEmailVerification = false;

  ProvinceModel? _selectedProvince;

  List<ProvinceModel> _provinceData = [];
  List<ProvinceModel> _filtereedProvinceData = [];
  TextEditingController _provinceInputController = TextEditingController();
  FocusNode _provinceInputFocusNode = FocusNode();

  @override
  void initState() {
    // dobController.text = GlobalMethods.dateToUIString(lastDOB);
    _init();
    super.initState();
    _getProvinceData();

    _referralFocusNode.addListener(() {
      if (_referralFocusNode.hasFocus) {
        Scrollable.ensureVisible(
          _referralFocusNode.context!,
          alignment: 0.1,
          duration: Duration(milliseconds: 300),
        );
      }
    });

    _provinceInputFocusNode.addListener(() {
      if (!_provinceInputFocusNode.hasFocus) {
        setState(() {
          _filtereedProvinceData = [];
        });
      } else {
        Scrollable.ensureVisible(
          _provinceInputFocusNode.context!,
          alignment: 0.1,
          duration: Duration(milliseconds: 300),
        );
        if (_selectedProvince == null) {
          _filtereedProvinceData = _getFilteredProvinceData("");

          setState(() {
            _selectedProvince = null;
          });
        }
      }
    });
  }

  _init() {
    // _userNameController.text=widget.userName??"";

    _firstNameController.text = widget.first_name ?? "";
    _lastNameController.text = widget.last_name ?? '';
    _emailController.text = widget.email ?? "";

    _selectedGender.value =
        (widget.gender == null || widget.gender!.trim().isEmpty)
        ? _selectedGender.value
        : widget.gender!;
    _otherGender = widget.otherGenderText ?? "";

    _isEmailOTPVerified = widget.email != null;

    // _userNameController.text=widget.userName??"";
  }

  // _getVehicleCategoryListByRegionId({required String regionId}){
  //   BlocProvider.of<RegistrationCubit>(context)
  //       .getVehicleCategoryListByRegionId(regionId: regionId);
  // }

  void _getDOB() {
    showDatePicker(
      context: context,
      initialDate: selectedDOB,
      firstDate: firstDOB,
      lastDate: lastDOB,
    ).then((value) {
      if (value != null) {
        selectedDOB = value;
        setState(() {
          dobController.text = GlobalMethods.dateToUIString(selectedDOB);
        });
      }
    });
  }

  _getProvinceData() {
    BlocProvider.of<RegistrationCubit>(context).getprovinceList();
  }

  _sendOtp({required String phone}) {
    Map<String, dynamic> request = {
      'contact_number': phone,
      "user_type": "driver",
    };
    BlocProvider.of<RegistrationCubit>(context).sendOtp(request: request);
  }

  completeProfile({required MultipartRequest multiPartRequest}) {
    BlocProvider.of<RegistrationCubit>(
      context,
    ).completeProfile(multiPartRequest: multiPartRequest);
  }

  _verifyCoupon({required String referral_code}) {
    hideKeyboard(context);
    Map<String, dynamic> request = {"referral_code": referral_code};
    BlocProvider.of<RegistrationCubit>(
      context,
    ).verifyReferral(request: request);
  }

  FocusNode firstNameFocus = FocusNode();
  FocusNode lastNameFocus = FocusNode();

  FocusNode emailFocus = FocusNode();

  // FocusNode userNameFocus = FocusNode();
  FocusNode ageFocus = FocusNode();

  // carModel focusnode only
  FocusNode carModel = FocusNode();
  FocusNode carProductionYear = FocusNode();
  FocusNode carPlateNumber = FocusNode();
  FocusNode carColor = FocusNode();

  Widget build(BuildContext context) {
    titleText({required String title}) {
      return Text(
        title,
        style: TextStyle(fontWeight: FontWeight.w700, fontSize: 18),
      );
    }

    return Scaffold(
      appBar: RoooAppbar(title: "Complete Profile"),
      body: BlocConsumer<RegistrationCubit, RegistrationState>(
        listener: (context, state) async {
          if (state is RegistrationErrorState) {
            GlobalMethods.errorToast(context, state.message);
          } else if (state is RegisterationCompletedState) {
            GlobalMethods.pushAndRemoveAll(
              context: context,
              screen: RideScreen(),
              screenIdentifier: ScreenIdentifier.InitialScreen,
            );
          } else if (state is ReferralCodeVerifiedState) {
            _isReferralCodeVerified.value = true;
          } else if (state is RegistrationOtpSentState) {
            bool? result = await GlobalMethods.pushScreen(
              context: context,
              screen: OtpScreen(
                countryCode: _countryCode,
                phone: _phoneNumberController.text,
                loginKey: state.key,
                isSocialValidation: true,
                countryIsoCode: _countryCode,
              ),
              screenIdentifier: ScreenIdentifier.OtpScreen,
            );
            if (result != null) {
              _isVerifiedNumber.value = true;
            }
          } else if (state is ProvinceListLoadedState) {
            _provinceData = state.provinceList;
            _selectedProvince = state.provinceList[0];
            _provinceInputController.text = _selectedProvince!.provinceName;
          }
        },
        builder: (context, state) {
          return ScreenBody(
            isLoading: state is RegistrationLoadingState,
            isEmpty: false,
            emptyMessage: '',
            onPullToRefresh: () {
              return Future.value();
            },
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 100.0),
                child: Column(
                  children: [
                    ValueListenableBuilder<int>(
                      valueListenable: currentIndex,
                      builder: (context, state, child) {
                        return Stepper(
                          physics: NeverScrollableScrollPhysics(),
                          controlsBuilder: (stepIndex, stepState) {
                            if (stepState.currentStep == 4) {
                              return AppButton(
                                width: double.infinity,
                                text: "Complete profile",
                                onPressed: () async {
                                  if (isAcceptedTermsAndConditions.value ==
                                      false) {
                                    GlobalMethods.errorToast(
                                      context,
                                      "Please accept the terms and conditions",
                                    );
                                    return;
                                  }

                                  MultipartRequest multiPartRequest =
                                      await getMultiPartRequest('update-user');
                                  multiPartRequest.fields['id'] = widget.id
                                      .toString();

                                  // multiPartRequest.fields['username'] =
                                  //     _userNameController.text;
                                  multiPartRequest.fields['first_name'] =
                                      _firstNameController.text;
                                  multiPartRequest.fields['last_name'] =
                                      _lastNameController.text;
                                  multiPartRequest.fields['email'] =
                                      _emailController.text;
                                  multiPartRequest.fields['dob'] =
                                      GlobalMethods.dateToAPIString(
                                        selectedDOB,
                                      );
                                  multiPartRequest.fields['user_type'] =
                                      'driver';
                                  multiPartRequest.fields['contact_number'] =
                                      _countryCode +
                                      _phoneNumberController.text;

                                  multiPartRequest.fields['password'] =
                                      _passwordController.text;

                                  multiPartRequest.fields['player_id'] =
                                      GlobalState.playerId;
                                  // multiPartRequest.fields['uid'] = userData.uid.toString();
                                  multiPartRequest.fields['gender'] =
                                      _selectedGender.value;
                                  if (_selectedGender.value == OTHER) {
                                    multiPartRequest
                                            .fields['other_gender_text'] =
                                        _otherGender;
                                  }
                                  if (_isReferralCodeVerified.value &&
                                      _isUserWantsToEnterReferralCode.value) {
                                    multiPartRequest.fields["referral_code"] =
                                        _referralController.text;
                                  }
                                  multiPartRequest.fields["service_ids"] =
                                      _selectedVehicleId.toString();
                                  multiPartRequest.fields["carModel"] =
                                      _carModelController.text;

                                  multiPartRequest.fields["carPlateNumber"] =
                                      _carPlateNumberController.text;

                                  multiPartRequest.fields["carProductionYear"] =
                                      carProductionYearController.text;

                                  multiPartRequest.fields['region_id'] =
                                      _selectedProvince!.regionId.toString();
                                  multiPartRequest.fields['province_id'] =
                                      _selectedProvince!.id.toString();

                                  multiPartRequest.files.add(
                                    await MultipartFile.fromPath(
                                      'profile_image[]',
                                      profileImage.value.path,
                                    ),
                                  );

                                  print(multiPartRequest);
                                  log(multiPartRequest);

                                  completeProfile(
                                    multiPartRequest: multiPartRequest,
                                  );
                                },
                              );
                              // AppButtonWidget(
                              //   margin:
                              //       EdgeInsets.only(top: screenPaddingValue),
                              //   width: double.infinity,
                              //   onTap: () {
                              //     register();
                              //   },
                              //   text: "register",
                              // );
                            } else {
                              return Row(
                                children: [
                                  ValueListenableBuilder(
                                    valueListenable: _isVerifiedNumber,
                                    builder: (context, value, child) => Visibility(
                                      visible: _isVerifiedNumber.value,
                                      child: AppButton(
                                        text: language.continueText,
                                        onPressed: () async {
                                          if (currentIndex.value == 0 &&
                                              !_isVerifiedNumber.value) {
                                            GlobalMethods.infoToast(
                                              context,
                                              "Please verify number",
                                            );
                                          } else if (currentIndex.value == 0) {
                                            if (_selectedGender.value ==
                                                SELECT) {
                                              GlobalMethods.errorToast(
                                                context,
                                                "Please select gender",
                                              );
                                              return;
                                            }
                                            if (_formKeys[0].currentState
                                                    ?.validate() ??
                                                false) {
                                              // _getVehicleCategoryListByRegionId(
                                              //     regionId:
                                              //         _selected_region_id.value.toString());
                                              incrementCurrentIndex(
                                                index: currentIndex.value,
                                              );
                                            }
                                          } else if (currentIndex.value == 1) {
                                            if (profileImage.value.path == '') {
                                              GlobalMethods.infoToast(
                                                context,
                                                "Please upload image",
                                              );
                                            } else {
                                              incrementCurrentIndex(
                                                index: currentIndex.value,
                                              );
                                            }
                                          } else if (currentIndex.value == 2) {
                                            // related to email and security
                                            if (_formKeys[1].currentState
                                                    ?.validate() ??
                                                false) {
                                              if (!_isEmailOTPSent) {
                                                _showEmailVerificationDialog();
                                              } else if (_emailOTPController
                                                      .text
                                                      .trim()
                                                      .length <
                                                  6) {
                                                GlobalMethods.errorToast(
                                                  context,
                                                  "Please enter email verification code",
                                                );
                                              } else if (!_isEmailOTPVerified) {
                                                _verifyEmailOtp(
                                                  email: _emailController.text
                                                      .trim(),
                                                  otp: _emailOTPController.text
                                                      .trim(),
                                                );
                                              } else {
                                                incrementCurrentIndex(
                                                  index: currentIndex.value,
                                                );
                                              }
                                            }
                                          } else if (currentIndex.value == 3) {
                                            if (_isUserWantsToEnterReferralCode
                                                .value) {
                                              if (_formKeys[2].currentState
                                                      ?.validate() ??
                                                  false) {
                                                if (_isReferralCodeVerified
                                                    .value) {
                                                  incrementCurrentIndex(
                                                    index: currentIndex.value,
                                                  );
                                                } else {
                                                  await _verifyCoupon(
                                                    referral_code:
                                                        _referralController
                                                            .text,
                                                  );
                                                }
                                              }
                                            } else {
                                              incrementCurrentIndex(
                                                index: currentIndex.value,
                                              );
                                            }
                                          }
                                        },
                                      ),
                                    ),
                                  ),
                                  width20,

                                  AppButton(
                                    text: language.cancelTxt,
                                    onPressed: () async {
                                      if (currentIndex.value > 0) {
                                        currentIndex.value =
                                            currentIndex.value - 1;
                                      }
                                    },
                                  ),
                                  // AppButtonWidget(
                                  //   onTap: () {
                                  //     if (currentIndex.value > 0) {
                                  //       currentIndex.value =
                                  //           currentIndex.value - 1;
                                  //     }
                                  //   },
                                  //   child: CustomText(
                                  //     data: language.cancelTxt,
                                  //     color: Colors.white,
                                  //     size: 10,
                                  //   ),
                                  //   padding: screenPadding,
                                  // )
                                ],
                              );
                            }
                          },
                          onStepTapped: (value) {
                            if (filledIndexes.contains(value)) {
                              currentIndex.value = value;
                            }
                          },
                          currentStep: state,
                          steps: [
                            Step(
                              title: titleText(title: "Details"),
                              content: personalDetailsForm(),
                            ),
                            Step(
                              title: titleText(title: "Pictures"),
                              content: _profileImage(),
                            ),
                            Step(
                              title: titleText(title: "Email and Security"),
                              content: _emailAndSecurity(),
                            ),
                            Step(
                              title: titleText(title: "Select Referral Code"),
                              content: referralWidget(),
                            ),
                            Step(
                              title: titleText(title: "Terms and Conditions"),
                              content: genderSelectionAndTermsAndCondition(),
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget personalDetailsForm() {
    return Form(
      key: _formKeys[0],
      child: Column(
        children: [
          height10,
          AppTextField(
            textFieldType: TextFieldType.NAME,
            controller: _firstNameController,
            focus: firstNameFocus,
            nextFocus: lastNameFocus,
            validator: (value) {
              if (value?.isEmpty ?? false || value == null) {
                return "Please enter valid name";
              } else {
                return null;
              }
            },
            errorThisFieldRequired: language.thisFieldRequired,
            decoration: InputDecoration(
              hintText: language.firstName,
              labelText: language.firstName,
            ),
          ),
          height10,
          AppTextField(
            textFieldType: TextFieldType.NAME,
            controller: _lastNameController,
            focus: lastNameFocus,
            nextFocus: ageFocus,
            validator: (value) {
              if (value?.isEmpty ?? false || value == null) {
                return "Please enter valid name";
              } else {
                return null;
              }
            },
            errorThisFieldRequired: language.thisFieldRequired,
            decoration: InputDecoration(
              hintText: language.lastName,
              labelText: language.lastName,
            ),
          ),
          height10,
          AppTextField(
            key: UniqueKey(),

            textFieldType: TextFieldType.OTHER,
            controller: dobController,
            focus: ageFocus,
            nextFocus: emailFocus,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "Please enter valid  DOB";
              }
              return null;
            },
            errorThisFieldRequired: language.thisFieldRequired,
            decoration: InputDecoration(hintText: "DOB", labelText: "DOB"),
            readOnly: true,
            onTap: () {
              _getDOB();
            },
          ),
          height10,
          ValueListenableBuilder<String>(
            valueListenable: _selectedGender,
            builder: (context, value, child) {
              if (genderList.isEmpty || _selectedGender.value == -1) {
                return SizedBox();
              }
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("Gender", style: AppTextStyles.title()),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        width: 1,
                        color: AppColors.primaryColor(context).withOpacity(.6),
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String?>(
                        isExpanded: true,
                        value: value == -1 ? genderList[0] : value,
                        items: genderList.map((e) {
                          return DropdownMenuItem<String>(
                            value: e,
                            child: Text(getTitleCase(e)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          _selectedGender.value = value!;
                        },
                      ),
                    ),
                  ),
                  _selectedGender.value != "other"
                      ? const SizedBox()
                      : Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: AppTextField(
                            textFieldType: TextFieldType.OTHER,
                            label: "Please enter",
                            initialValue: _otherGender,
                            onChanged: (value) {
                              _otherGender = value;
                            },
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return "Please enter";
                              }
                              return null;
                            },
                          ),
                        ),
                ],
              );
            },
          ),
          height10,

          TextFormField(
            focusNode: _provinceInputFocusNode,
            decoration: InputDecoration(
              label: Text("State"),
              hintText: "Type to search...",
              suffixIcon: Icon(Icons.search),
            ),
            controller: _provinceInputController,
            validator: (value) {
              if (_selectedProvince == null) {
                return "Please select a valid state";
              }
              return null;
            },
            onChanged: (value) {
              _filtereedProvinceData = _getFilteredProvinceData(value);
              if (_filtereedProvinceData.isEmpty) {
                _filtereedProvinceData.add(
                  ProvinceModel(
                    id: -1,
                    regionId: -1,
                    regionName: "",
                    provinceName: "No province found",
                  ),
                );
              }
              setState(() {
                _selectedProvince = null;
              });
            },
          ),
          Card(
            child: Container(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey.shade400
                  : Colors.grey.shade100,
              height: _filtereedProvinceData.length > 0 ? 200 : 0,
              child: ListView.separated(
                // shrinkWrap: true,
                // physics: NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return ListTile(
                    title: Text(
                      _filtereedProvinceData[index].provinceName,
                      style: TextStyle(color: Colors.black),
                    ),
                    subtitle: Text(
                      _filtereedProvinceData[index].regionName,
                      style: TextStyle(color: Colors.black),
                    ),
                    onTap: () {
                      if (_filtereedProvinceData[index].id == -1) {
                        return;
                      }
                      _selectedProvince = _filtereedProvinceData[index];
                      _provinceInputController.text =
                          _selectedProvince!.provinceName;
                      setState(() {
                        _filtereedProvinceData = [];
                      });
                    },
                  );
                },
                separatorBuilder: (context, index) => const Divider(),
                itemCount: _filtereedProvinceData.length,
              ),
            ),
          ),

          TextFormField(
            controller: _phoneNumberController,
            autofocus: true,
            readOnly: _isVerifiedNumber.value,
            maxLength: 10,
            validator: (value) {
              if ((value?.length ?? 0) < 9) {
                return "Please enter a valid  mobile number";
              }
              return null;
            },
            onChanged: (value) {
              // _isVerifiedNumber.value = false;
            },
            keyboardType: TextInputType.phone,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            decoration: InputDecoration(
              counterText: "",
              prefixIcon: Padding(
                padding: const EdgeInsets.all(14),
                child: Text("+61", style: AppTextStyles.header()),
              ),
              border: OutlineInputBorder(),
            ),
          ),

          // Column(
          //   crossAxisAlignment: CrossAxisAlignment.center,
          //   children: [
          //     IntlPhoneField(
          //       enabled: true,
          //       style: TextStyle(fontSize: 14),
          //       validator: (p0) {
          //         if (int.tryParse((p0?.number) ?? '') == null) {
          //           return language.InvalidMobileNumberTxt;
          //         }
          //       },
          //       initialValue: _phoneNumberController.text,
          //       decoration: InputDecoration(),
          //       initialCountryCode: _countryIsoCode,
          //       onChanged: (phone) {
          //         _countryCode = phone.countryCode;
          //         _phoneNumberController.text = phone.number;
          //         _countryIsoCode = phone.countryISOCode;
          //         _isVerifiedNumber.value = false;
          //       },
          //     ),
          ValueListenableBuilder<bool>(
            valueListenable: _isVerifiedNumber,
            builder: (context, value, child) {
              if (!value) {
                return AppButton(
                  width: double.infinity,
                  text: "Verify",
                  onPressed: () async {
                    if (_phoneNumberController.text.isEmpty) {
                      GlobalMethods.infoToast(
                        context,
                        language.InvalidMobileNumberTxt,
                      );
                    } else if (_formKeys[0].currentState?.validate() ?? false) {
                      _sendOtp(
                        phone: _countryCode + _phoneNumberController.text,
                      );
                    }
                  },
                );
              }
              return SizedBox();
            },
          ),
          //   ],
          // )
        ],
      ),
    );
  }

  Widget referralWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("Do you have referral code?"),
        Row(
          children: [
            Text("No"),
            width10,
            Switch(
              value: _isUserWantsToEnterReferralCode.value,
              onChanged: (value) {
                setState(() {
                  _isUserWantsToEnterReferralCode.value = value;
                });
              },
            ),
            width10,
            Text("Yes"),
          ],
        ),
        !_isUserWantsToEnterReferralCode.value
            ? const SizedBox()
            : Row(
                children: [
                  Expanded(
                    child: Form(
                      key: _formKeys[2],
                      child: TextFormField(
                        focusNode: _referralFocusNode,
                        controller: _referralController,
                        onChanged: (v) {
                          _isReferralCodeVerified.value = false;
                        },
                        validator: (value) {
                          if (value?.isEmpty ?? false || value == null) {
                            return "Please enter valid referralcode";
                          } else {
                            return null;
                          }
                        },
                        decoration: InputDecoration(hintText: "Referral Code"),
                      ),
                    ),
                  ),
                  ValueListenableBuilder<bool>(
                    valueListenable: _isReferralCodeVerified,
                    builder: (context, value, child) {
                      if (value) {
                        return Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: CircleAvatar(
                            radius: 15,
                            backgroundColor: AppColors.greenColor,
                            child: Icon(Icons.check, color: Colors.white),
                          ),
                        );
                      }
                      return SizedBox();
                    },
                  ),
                ],
              ),
      ],
    );
  }

  // Widget SelectVehicleDropDown() {
  //   if(vehicleResponseModel==null){
  //     return SizedBox();
  //   }return
  //   ListView.separated(
  //             separatorBuilder: (context, index) => height10,
  //             itemCount: vehicleResponseModel!.data!.length,
  //             shrinkWrap: true,
  //             itemBuilder: (context, index) {
  //               VehicleModel data = vehicleResponseModel!.data![index];
  //               // _selectedVehicleId =
  //               //     state.vehicleResponseModel.data?[0].id ?? -1;
  //               return ValueListenableBuilder(
  //                 valueListenable: selectVehicleIndex,
  //                 builder: (context, state, _) {
  //                   return InkWell(
  //                     onTap: () {
  //                       selectVehicleIndex.value = index;
  //                       _selectedVehicleId = data.id ?? -1;
  //                     },
  //                     child: Container(
  //                       padding: screenPadding / 4,
  //                       decoration: BoxDecoration(
  //                           borderRadius: radius(),
  //                           border: Border.all(
  //                               width: .5,
  //                               color: selectVehicleIndex.value == index
  //                                   ? AppColors.greenColor
  //                                   : AppColors.blackColor(context))),
  //                       child: Row(
  //                         children: [
  //                           commonCachedNetworkImage(data.serviceImage,
  //                               fit: BoxFit.contain, height: 50, width: 50),
  //                           SizedBox(width: 16),
  //                           Expanded(
  //                             child: Text(data.name.validate(),
  //                                 style: boldTextStyle()),
  //                           ),
  //                           Visibility(
  //                             visible: selectVehicleIndex.value == index,
  //                             child: Icon(Icons.check_circle_outline,
  //                                 color: Colors.green),
  //                           )
  //                         ],
  //                       ),
  //                     ),
  //                   );
  //                 },
  //               );
  //             });

  // }

  Widget genderSelectionAndTermsAndCondition() {
    return Column(
      children: [
        // ValueListenableBuilder<String>(
        //     valueListenable: _selectedGender,
        //     builder: ((context, value, child) {
        //       return DropdownButtonFormField<String>(
        //         value: value,
        //         onChanged: (String? value) {
        //           _selectedGender.value = value!;
        //           filledIndexes.add(5);
        //         },
        //         items: gender
        //             .map((value) => DropdownMenuItem(
        //                 value: value,
        //                 child: Text(
        //                   "${value.capitalizeFirstLetter()}",
        //                 )))
        //             .toList(),
        //       );
        //     })),
        // SizedBox(height: 8),
        ValueListenableBuilder<bool>(
          valueListenable: isAcceptedTermsAndConditions,
          builder: (context, value, child) {
            return CheckboxListTile(
              contentPadding: EdgeInsets.zero,
              controlAffinity: ListTileControlAffinity.leading,
              title: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: '${language.agreeToThe} ',
                      style: TextStyle(color: AppColors.blackColor(context)),
                    ),
                    TextSpan(
                      text: language.termsConditions,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor(context),
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          GlobalMethods.pushScreen(
                            context: context,
                            screen: TermsAndConditionsScreen(),
                            screenIdentifier:
                                ScreenIdentifier.TermsAndConditionsScreen,
                          );

                          // launchScreen(context, TermsAndConditionsScreen(),
                          //     pageRouteAnimation: PageRouteAnimation.Slide);
                        },
                    ),
                    TextSpan(
                      text: ' & ',
                      style: TextStyle(color: AppColors.blackColor(context)),
                    ),
                    TextSpan(
                      text: language.privacyPolicy,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor(context),
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          GlobalMethods.pushScreen(
                            context: context,
                            screen: PrivacyPolicyScreen(),
                            screenIdentifier:
                                ScreenIdentifier.PrivacyPolicyScreen,
                          );
                          // launchScreen(context, PrivacyPolicyScreen(),
                          //     pageRouteAnimation: PageRouteAnimation.Slide);
                        },
                    ),
                  ],
                ),
                textAlign: TextAlign.left,
              ),
              value: value,
              onChanged: (val) async {
                isAcceptedTermsAndConditions.value = val!;
                filledIndexes.add(5);
              },
            );
          },
        ),
      ],
    );
  }

  Future<void> getImage() async {
    XFile? selectedImage = await ImagePicker().pickImage(
      source: ImageSource.camera,
      imageQuality: 50,
    );

    if (selectedImage != null) {
      bool value = await GlobalMethods.checkImageSize(
        result: selectedImage,
        context: context,
      );
      if (value) {
        profileImage.value = selectedImage;
      }
    }
  }

  Widget _profileImage() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Stack(
          alignment: Alignment.bottomRight,
          children: [
            SizedBox(
              height: 100,
              width: 100,
              child: ValueListenableBuilder<XFile>(
                valueListenable: profileImage,
                builder: (context, value, child) {
                  if (value.path != "") {
                    return Center(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(100),
                        child: Image.file(
                          File(value.path),
                          height: 100,
                          width: 100,
                          fit: BoxFit.cover,
                          alignment: Alignment.center,
                        ),
                      ),
                    );
                  }
                  {
                    return Center(
                      child: Padding(
                        padding: EdgeInsets.only(left: 4, bottom: 4),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(100),
                          child: Image.asset(
                            'images/ic_person.jpg',
                            height: 90,
                            width: 90,
                          ),
                        ),
                      ),
                    );
                  }
                },
              ),
            ),
            inkWellWidget(
              onTap: () {
                getImage();
              },
              child: Container(
                padding: screenPadding / 3,
                decoration: BoxDecoration(
                  color: Colors.black,
                  shape: BoxShape.circle,
                  border: Border.all(),
                ),
                child: Icon(Icons.edit, color: Colors.white),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _emailAndSecurity() {
    return Form(
      key: _formKeys[1],
      child: Column(
        children: [
          height10,
          AppTextField(
            textFieldType: TextFieldType.EMAIL,
            controller: _emailController,
            focus: emailFocus,
            nextFocus: passwordFocus,
            validator: (value) {
              final RegExp _emailRegex = RegExp(
                r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
              );
              if (!_emailRegex.hasMatch(value.toString())) {
                return "Please enter valid email";
              } else {
                return null;
              }
            },
            errorThisFieldRequired: language.thisFieldRequired,
            readOnly: _isEmailOTPVerified,
            decoration: InputDecoration(
              hintText: language.email,
              labelText: language.email,
              suffixIcon: _isEmailOTPVerified
                  ? Icon(Icons.check, color: Colors.green)
                  : null,
            ),
          ),
          height10,
          !_isEmailOTPSent || _isEmailOTPVerified
              ? const SizedBox()
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("Please enter the verification code"),
                    height10,
                    Pinput(
                      controller: _emailOTPController,
                      defaultPinTheme: PinTheme(
                        width: 55,
                        height: 55,
                        decoration: BoxDecoration(
                          border: Border.all(
                            width: .5,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      length: 6,
                      onCompleted: (pin) {
                        // _otp = pin;
                      },
                    ),
                    height20,
                    _timerView(),
                  ],
                ),
          height10,
          AppTextField(
            textFieldType: TextFieldType.PASSWORD,
            controller: _passwordController,
            focus: passwordFocus,
            label: "Password",
            validator: (value) {
              if (value!.length < 6) {
                return "Password must be atleast 6 chars. long";
              } else {
                return null;
              }
            },
            errorThisFieldRequired: language.thisFieldRequired,
          ),
        ],
      ),
    );
  }

  incrementCurrentIndex({required int index}) {
    currentIndex.value = currentIndex.value + 1;
    filledIndexes.add(index);
  }

  List<ProvinceModel> _getFilteredProvinceData(String text) {
    return _provinceData
        .where((o) => o.provinceName.toLowerCase().contains(text.toLowerCase()))
        .toList();
  }

  void _showEmailVerificationDialog() {
    if (_isNotifiedForTheEmailVerification) {
      _sendEmailOtp(email: _emailController.text.trim(), resendOTP: true);
      return;
    }
    GlobalMethods.showInfoDialogNew(
      context: context,
      onClick: () {
        _isNotifiedForTheEmailVerification = true;
        _sendEmailOtp(email: _emailController.text.trim(), resendOTP: false);
      },
      title:
          "A verification code will be sent to your email. Please verify your email address to continue",
    );
  }

  Future<void> _sendEmailOtp({
    required String email,
    required bool resendOTP,
  }) async {
    if (!resendOTP) {
      Navigator.of(context).pop();
    }
    _showSendingEmailOTP();
    var response = await sendOTPToEmail(email: email);
    Navigator.of(context).pop();
    if (!response.status) {
      GlobalMethods.infoToast(context, response.message);
      return;
    }
    setState(() {
      _isEmailOTPSent = true;
    });
    _emailOTPCountdownTimer = 60;
    _emailVerificationTimer = Timer.periodic(Duration(seconds: 1), (v) {
      if (_emailOTPCountdownTimer == 0) {
        _emailVerificationTimer?.cancel();
      } else {
        setState(() {
          _emailOTPCountdownTimer = _emailOTPCountdownTimer - 1;
        });
      }
    });
  }

  void _showSendingEmailOTP() {
    GlobalMethods.showActivity(
      context: context,
      title: "Sending email verification code. Please wait...",
    );
  }

  void _showVerifyingEmail() {
    GlobalMethods.showActivity(
      context: context,
      title:
          "We are verifying your email. Please wait while we verify your email address.",
    );
  }

  Future<void> _verifyEmailOtp({
    required String email,
    required String otp,
  }) async {
    _showVerifyingEmail();
    var response = await verifyEmailOTP(
      email: email,
      otp: _emailOTPController.text.trim(),
    );
    Navigator.of(context).pop();
    if (!response.status) {
      GlobalMethods.infoToast(context, response.message);
      return;
    }
    setState(() {
      _isEmailOTPVerified = true;
    });
  }

  Widget _timerView() {
    return Column(
      children: [
        Row(
          children: [
            Text("Code not received?", style: TextStyle()),
            width10,
            _emailOTPCountdownTimer == 0
                ? AppButton(
                    text: "Resend",
                    onPressed: () async {
                      _sendEmailOtp(
                        email: _emailController.text.trim(),
                        resendOTP: true,
                      );
                    },
                  )
                : Text(
                    _emailOTPCountdownTimer.toString(),
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
          ],
        ),
        height10,
      ],
    );
  }
}
