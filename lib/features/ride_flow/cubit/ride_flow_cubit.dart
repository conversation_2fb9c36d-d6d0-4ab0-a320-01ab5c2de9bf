import 'package:flutter/foundation.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:rooo_driver/background_tasks/LocalNotifications.dart';
import 'package:rooo_driver/features/advertisements/models/advertisement_model.dart';
import 'package:rooo_driver/features/ride_flow/model/destination_places_model.dart';
import 'package:rooo_driver/features/ride_flow/model/multiple_stop_model.dart';
import 'package:rooo_driver/features/ride_flow/model/online_offline_model.dart';
import 'package:rooo_driver/features/ride_flow/ride_repository/ride_repository.dart';
import 'package:rooo_driver/features/ride_flow/screen/ride_screen_utils.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';
import 'package:rooo_driver/model/AppSettingModel.dart';
import 'package:rooo_driver/network/logs_screen.dart';

abstract class RideFlowState {}

class RideFlowInitialState extends RideFlowState {}

class RideFlowLoadingState extends RideFlowState {}

class HomePageDataLoadedState extends RideFlowState {
  final HomePageDataModel homePageData;

  HomePageDataLoadedState({required this.homePageData});
}

class AdvertismentDataLoadedState extends RideFlowState {
  final AdvertisementModel advertisement;

  AdvertismentDataLoadedState({required this.advertisement});
}

class OnlineOfflineDataLoadedState extends RideFlowState {
  final UserData data;

  OnlineOfflineDataLoadedState({required this.data});
}

class OnlineErrorDataLoadedState extends RideFlowState {
  final OnlineOfflineModel data;

  OnlineErrorDataLoadedState({required this.data});
}

// class OnlineOfflineStatusLoadedState extends RideFlowState {
//   final OnlineOfflineResponseModel data;

//   OnlineOfflineStatusLoadedState({required this.data});
// }

class DestinationChangedState extends RideFlowState {
  final DestinationPlaceModel data;

  DestinationChangedState({required this.data});
}

class UnverifiedDriverState extends RideFlowState {}

class QuizNotCompletedState extends RideFlowState {
  final String quizUrl;

  QuizNotCompletedState({required this.quizUrl});
}

class RideFlowErrorState extends RideFlowState {
  final String message;

  RideFlowErrorState({required this.message});
}

class OnlineState extends RideFlowState {
  final bool isExplicitOnlineRequest;

  OnlineState({this.isExplicitOnlineRequest = false});
}

class OfflineState extends RideFlowState {}

class CurrentRideLoadedState extends RideFlowState {
  final RideModel rideModel;

  CurrentRideLoadedState({required this.rideModel});
}

class AdminNotifiedState extends RideFlowState {}

class NewRideState extends RideFlowState {
  final OnRideRequest onRideRequest;

  NewRideState({required this.onRideRequest});
}

class NewRideAcceptState extends RideFlowState {
  NewRideAcceptState();
}

class NewRideAcceptedByOtherDriverState extends RideFlowState {
  String message;
  NewRideAcceptedByOtherDriverState({required this.message});
}

class NewRideErrorState extends RideFlowState {
  final String message;

  NewRideErrorState({required this.message});
}

class NewRideDeclineState extends RideFlowState {
  final int rideId;
  NewRideDeclineState({required this.rideId});
}

class QueuedRideState extends RideFlowState {
  final OnRideRequest onRideRequest;

  QueuedRideState({required this.onRideRequest});
}

class QueuedRideAcceptState extends RideFlowState {
  QueuedRideAcceptState();
}

class QueuedRideDeclineState extends RideFlowState {
  final int rideId;
  QueuedRideDeclineState({required this.rideId});
}

class QueuedRideErrorState extends RideFlowState {
  final String message;

  QueuedRideErrorState({required this.message});
}

class RideCancelledState extends RideFlowState {
  RideCancelledState();
}

// class NewRideMissedState extends RideFlowState {
//   NewRideMissedState();
// }
class StopUpdatedState extends RideFlowState {
  StopUpdatedState();
}

class RideArrivingState extends RideFlowState {
  final RideModel rideModel;

  RideArrivingState({required this.rideModel});
}

class RideCompletedState extends RideFlowState {
  RideCompletedState();
}

class SelfieUploadedState extends RideFlowState {
  SelfieUploadedState();
}

class AppSettingLoadedState extends RideFlowState {
  final AppSettingModel appSettingModel;

  AppSettingLoadedState({required this.appSettingModel});
}

class MqttConnectedState extends RideFlowState {}

class MqttErrorState extends RideFlowState {}

class InitialRideLoadedMqtt extends RideFlowState {
  InitialRideLoadedMqtt();
}

class NewRideStateMqtt extends RideFlowState {
  final OnRideRequest onRideRequest;

  NewRideStateMqtt({required this.onRideRequest});
}

class QueuedRideStateMqtt extends RideFlowState {
  final OnRideRequest onRideRequest;
  QueuedRideStateMqtt({required this.onRideRequest});
}

class NewRiderLocationStateMqtt extends RideFlowState {
  final AppLocationType location;
  NewRiderLocationStateMqtt({required this.location});
}

class RideCancelledStateMqtt extends RideFlowState {
  final int rideId;

  RideCancelledStateMqtt({required this.rideId});
}

class MultipleStopMqtt extends RideFlowState {
  final String message;

  MultipleStopMqtt({required this.message});
}

class MultipleStopDeletedMqtt extends RideFlowState {
  final String message;

  MultipleStopDeletedMqtt({required this.message});
}

class OfflineStateMqtt extends RideFlowState {
  final String messsage;

  OfflineStateMqtt({required this.messsage});
}

class DriverCancelledStateMqtt extends RideFlowState {}

class DestinationChangedStateMqtt extends RideFlowState {
  final String message;

  final OnRideRequest onRideRequest;

  DestinationChangedStateMqtt({
    required this.message,
    required this.onRideRequest,
  });
}

class PickUpChangedStateMqtt extends RideFlowState {
  PickUpChangedStateMqtt();
}
///////////////////////////////////////////////////////////////

class DeviceLocationUpdatedState extends RideFlowState {
  final LatLng location;

  DeviceLocationUpdatedState({required this.location});
}

class RideFlowCubit extends Cubit<RideFlowState> {
  RideRepository _rideRepository = RideRepository();
  RideFlowCubit() : super(RideFlowInitialState());

  Future startDeviceTracking() async {
    // GlobalMethods.infoToast(context,  "start Device tracking");
    await get_device_current_location();

    if (GlobalState.driver_device_timer == null) {
      GlobalState.driver_device_timer = Timer.periodic(Duration(seconds: 20), (
        timer,
      ) async {
        await get_device_current_location();
      });
    }
  }

  Future get_device_current_location() async {
    await GlobalMethods.checkLocationPermission(
      onPermissionGranted: () async {
        await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
        ).then((value) {
          GlobalState.driverPosition = LatLng(value.latitude, value.longitude);
          emit(
            DeviceLocationUpdatedState(location: GlobalState.driverPosition!),
          );

          GlobalState.driverPosition = LatLng(value.latitude, value.longitude);
        });
      },
    );
  }

  getHomePageData() {
    emit(RideFlowLoadingState());
    _rideRepository
        .getHomePageDataApi()
        .then((value) {
          if (value.status) {
            if (value.data != null) {
              emit(HomePageDataLoadedState(homePageData: value.data!));
            } else {
              emit(RideFlowErrorState(message: serverErrorMessage));
            }
          } else {
            emit(RideFlowErrorState(message: value.message));
          }
        })
        .onError((e, _) {
          emit(RideFlowErrorState(message: e.toString()));
        });
  }

  getAdvertisemnts({required String type}) {
    emit(RideFlowLoadingState());
    _rideRepository
        .getAdvertismentApi(type: type)
        .then((value) {
          if (value.status) {
            if (value.data != null) {
              emit(AdvertismentDataLoadedState(advertisement: value.data!));
            } else {
              emit(RideFlowErrorState(message: serverErrorMessage));
            }
          } else {
            emit(RideFlowErrorState(message: value.message));
          }
        })
        .onError((e, _) {
          emit(RideFlowErrorState(message: e.toString()));
        });
  }

  checkOnlineOfflineStatus({required int userId}) {
    emit(RideFlowLoadingState());
    _rideRepository
        .getUserDetailApi(userId: userId)
        .then((value) {
          if (value.status) {
            if (value.data != null) {
              if (value.data?.isOnline == 0) {
                emit(OfflineState());
              } else if (value.data?.isOnline == 1) {
                GlobalState.isDriverOnline = true;
                emit(OnlineState(isExplicitOnlineRequest: true));
              }
            } else {
              emit(RideFlowErrorState(message: serverErrorMessage));
            }
          } else {
            emit(RideFlowErrorState(message: value.message));
          }
        })
        .onError((e, _) {
          emit(RideFlowErrorState(message: e.toString()));
        });
  }

  cancel_ride_request_by_driver({
    required Map request,
    required int rideId,
  }) async {
    emit(RideFlowLoadingState());

    await _rideRepository
        .change_ride_status_api(request: request, rideId: rideId)
        .then((value) {
          if (value.status) {
            getCurrenRide();
          } else {
            emit(RideFlowErrorState(message: value.message));
          }
        })
        .onError((error, stackTrace) {
          emit(RideFlowErrorState(message: "Server error"));
        });
  }

  Future<void> getCurrenRide() async {
    emit(RideFlowLoadingState());
    await _rideRepository
        .getCurrentRideRequestApi()
        .then((value) {
          if (value.status) {
            if (value.data != null) {
              emit(CurrentRideLoadedState(rideModel: value.data!));
            } else {
              emit(RideFlowErrorState(message: serverErrorMessage));
            }
          } else {
            emit(RideFlowErrorState(message: value.message));
          }
        })
        .onError((e, _) {
          emit(RideFlowErrorState(message: e.toString()));
        });
  }

  Future<void> changeRideStatus({
    required Map<String, dynamic> request,
    required int rideId,
    Function? afterSuccessCallback,
  }) async {
    emit(RideFlowLoadingState());
    await _rideRepository
        .change_ride_status_api(request: request, rideId: rideId)
        .then((value) {
          if (value.status) {
            if (afterSuccessCallback != null) {
              afterSuccessCallback();
            } else {
              getCurrenRide();
            }
          } else {
            emit(RideFlowErrorState(message: value.message));
          }
        })
        .onError((e, _) {
          emit(RideFlowErrorState(message: e.toString()));
        });
  }

  notify_admin({required Map<String, dynamic> request}) async {
    emit(RideFlowInitialState());
    emit(AdminNotifiedState());
    // await _rideRepository.notify_admin_api(request: request).then((value) {

    //   if(value.status){

    //   }
    //   else{
    //     emit(RideFlowErrorState(message: value.message));
    //   }
    // }).onError((error, stackTrace) {
    //   emit(RideFlowErrorState(message: "Server error"));
    // });
  }
  // getDriverLocation({required int driverId}) {
  //   // emit(RideFlowLoadingState());
  //   _rideRepository.getUserDetail(driverId: driverId).then((value) {
  //     if (value.status) {
  //       if (value.data != null) {
  //         emit(DriverUpdatedState(driverDetails: value.data!));
  //       } else {
  //         emit(RideFlowErrorState());
  //       }
  //     } else {
  //       emit(RideFlowErrorState(message: value.message));
  //     }
  //   }).onError(
  //     (error, stackTrace) {
  //       emit(RideFlowErrorState(message: "Server error"));
  //     },
  //   );
  // }

  // checkOnlineOfflineStatus({required int userId}) {
  //   emit(RideFlowLoadingState());
  //   _rideRepository.getUserDetailApi(userId: userId).then((value) {
  //     emit(OnlineOfflineDataLoadedState(data: value.data!));
  //   }).onError((e, _) {
  //     emit(RideFlowErrorState(message: e.toString()));
  //   });
  // }

  changeStopStatus({required Map<String, dynamic> request}) async {
    emit(RideFlowLoadingState());

    await _rideRepository
        .changeStopStatus(request: request)
        .then((value) {
          if (value.status) {
            getCurrenRide();
          } else {
            emit(RideFlowErrorState(message: value.message));
          }
        })
        .onError((e, _) {
          emit(RideFlowErrorState(message: e.toString()));
        });
  }

  getAppSetting() {
    emit(RideFlowLoadingState());

    _rideRepository
        .getAppSettingApi()
        .then((value) {
          if (value.status) {
            if (value.data != null) {
              emit(AppSettingLoadedState(appSettingModel: value.data!));
            } else {
              emit(RideFlowErrorState(message: serverErrorMessage));
            }
          } else {
            emit(RideFlowErrorState(message: value.message));
          }
        })
        .onError((error, _) {
          emit(RideFlowErrorState(message: "Server error"));
        });
  }

  changeOnlineOfflineStatus2({
    required int makeOnline,
    required int userId,
  }) async {
    emit(RideFlowLoadingState());

    await _rideRepository
        .updateDriverOnlineUpdateStatusApi(makeOnline, true)
        .then((value) {
          if (value.status) {
            if (value.data != null) {
              emit(OnlineErrorDataLoadedState(data: value.data!));
            } else {
              if (makeOnline == 1) {
                emit(OnlineState(isExplicitOnlineRequest: true));
              } else if (makeOnline == 0) {
                emit(OfflineState());
              }
            }
          } else {
            if (value.data != null) {
              emit(OnlineErrorDataLoadedState(data: value.data!));
            } else {
              if (makeOnline == 0) {
                emit(OfflineState());
              } else {
                emit(RideFlowErrorState(message: value.message));
              }
            }
          }
        })
        .onError((error, _) {
          emit(RideFlowErrorState(message: "Server error"));
        });
  }

  accepDeclineNewRideREquest({
    required Map request,
    required bool is_decline,
    required int rideId,
  }) async {
    emit(RideFlowLoadingState());

    await _rideRepository
        .acceptDeclineNewrideApi(request: request)
        .then((value) {
          if (value.status) {
            if (is_decline) {
              emit(NewRideDeclineState(rideId: rideId));
            } else {
              emit(NewRideAcceptState());
            }
          } else {
            if (value.data != null &&
                value.data!.isAlreadyAcceptedByOtherDriver == 1) {
              emit(NewRideAcceptedByOtherDriverState(message: value.message));
            } else {
              emit(NewRideErrorState(message: value.message));
            }
          }
        })
        .onError((error, stackTrace) {
          emit(NewRideErrorState(message: "Server error"));
        });
  }

  acceptDeclineQueuedRideRequest({
    required Map request,
    required bool is_decline,
    required int rideId,
  }) async {
    emit(RideFlowLoadingState());

    await _rideRepository
        .acceptDeclineNewrideApi(request: request)
        .then((value) {
          if (value.status) {
            if (is_decline) {
              emit(QueuedRideDeclineState(rideId: rideId));
            } else {
              emit(QueuedRideAcceptState());
            }
          } else {
            emit(QueuedRideErrorState(message: value.message));
          }
        })
        .onError((error, stackTrace) {
          emit(QueuedRideErrorState(message: "Server error"));
        });
  }

  complete_ride({
    required int ride_id,
    required int service_id,
    required List<Map<String, dynamic>> waiting_time_list,
    required double total_distance,
  }) async {
    emit(RideFlowLoadingState());
    String end_location = await _rideRepository.get_user_address();

    Map request = {
      "id": ride_id,
      "service_id": service_id,
      "end_latitude": GlobalState.driverPosition!.latitude,
      "end_longitude": GlobalState.driverPosition!.longitude,
      "end_address": end_location,
      "distance": total_distance,
      // "waiting_time_list": waiting_time_list
    };
    await _rideRepository
        .complete_ride_api(request: request)
        .then((value) {
          if (value.status) {
            getCurrenRide();
          } else {
            emit(RideFlowErrorState(message: value.message.toString()));
          }
        })
        .onError((error, _) {
          emit(RideFlowErrorState(message: "Server error"));
        });
  }

  void uploadSelfieImage({required MultipartRequest multiPartRequest}) async {
    emit(RideFlowLoadingState());
    await _rideRepository
        .uploadImage(multiPartRequest: multiPartRequest)
        .then((value) {
          if (value.status) {
            emit(SelfieUploadedState());
          } else {
            emit(RideFlowErrorState(message: value.message));
          }
        })
        .onError((error, stackTrace) {
          emit(RideFlowErrorState(message: "Server error"));
        });
  }

  Future<void> newRiderAlertEmitter({
    required OnRideRequest onRideRequest,
  }) async {
    // Check if driver is already in a ride
    bool isDriverInRide = GlobalState.current_ride?.value.onRideRequest != null;
    // isDriverInRide = true;
    if (isDriverInRide) {
      // Driver is already in a ride, emit queued ride state
      if (GlobalState.isNewQueuedRideShowing) {
        return;
      }
      GlobalState.isNewQueuedRideShowing = true;
      /* show notification */
      await LocalNotifications.showSimpleRideNotification(
        onRideRequest.id!,
        title: "New queued ride request",
        body: "You have a queued ride request",
      );
      emit(QueuedRideStateMqtt(onRideRequest: onRideRequest));
      GlobalMethods.succesToast(
        navigatorKey.currentContext!,
        "New queued ride request",
      );
    } else {
      // Driver is not in a ride, emit normal new ride state
      if (GlobalState.isNewRideShowing) {
        return;
      }
      GlobalState.isNewRideShowing = true;
      /* show notification */
      await LocalNotifications.showSimpleRideNotification(
        onRideRequest.id!,
        title: "New ride request",
        body: "You have a new ride request",
      );
      emit(NewRideStateMqtt(onRideRequest: onRideRequest));
      GlobalMethods.succesToast(
        navigatorKey.currentContext!,
        "New ride request",
      );
    }
  }

  Future mqttForUser() async {
    GlobalState.mqttClient?.disconnect();

    GlobalState.mqttClient = null;
    GlobalState.mqttClient = MqttServerClient.withPort(
      "roooaustralia-bqzjha.a03.euc1.aws.hivemq.cloud",
      "rooo_driver_" + sharedPref.getInt(USER_ID).toString(),
      8883,
      maxConnectionAttempts: 1,
    );

    void onSubscribed(String topic) {
      log('nct->Subscription confirmed for topic $topic');
    }

    GlobalState.mqttClient?.setProtocolV311();
    GlobalState.mqttClient?.secure = true;
    GlobalState.mqttClient?.securityContext = SecurityContext.defaultContext;

    GlobalState.mqttClient?.logging(on: true);
    GlobalState.mqttClient?.keepAlivePeriod = 50;
    GlobalState.mqttClient?.autoReconnect = true;

    try {
      await GlobalState.mqttClient?.connect(
        "hivemq.webclient.1729573412945",
        "3cNh4p\$n>5LAoQ9,#gSB",
      );
    } catch (e) {
      log('nct->' + e.toString());
    }

    if (GlobalState.mqttClient?.connectionStatus!.state ==
        MqttConnectionState.connected) {
      if (GlobalState.callNewRideMqtt) {
        getNewRideAgain();
        GlobalState.callNewRideMqtt = false;
      }
      GlobalState.mqttClient?.onDisconnected = () {
        log('nct-> discinnected');
      };

      GlobalState.mqttClient?.onSubscribed = onSubscribed;
      GlobalState.mqttClient?.subscribe(
        'new_ride_request_' + sharedPref.getInt(USER_ID).toString(),
        MqttQos.atLeastOnce,
      );
      GlobalState.mqttClient?.subscribe(
        'ride_request_status_' + sharedPref.getInt(USER_ID).toString(),
        MqttQos.atLeastOnce,
      );
      GlobalState.mqttClient?.subscribe(
        'ride_pool_notification_' + sharedPref.getInt(USER_ID).toString(),
        MqttQos.atLeastOnce,
      );

      // client.subscribe("rajat", MqttQos.atLeastOnce);
      // client.subscribe(
      //     "rajat",
      //     MqttQos.atLeastOnce);

      log('nct->connected');
    }

    GlobalState.mqttClient?.updates!.listen((
      List<MqttReceivedMessage<MqttMessage?>>? c,
    ) async {
      final MqttPublishMessage recMess = c![0].payload as MqttPublishMessage;
      final pt = MqttPublishPayload.bytesToStringAsString(
        recMess.payload.message,
      );

      if (areLogsEnabled) {
        appLogs.add(
          "MQTT received = " +
              jsonDecode(pt)['success_type'].toString() +
              " at " +
              DateTime.now().toString(),
        );
      }

      if (jsonDecode(pt)['success_type'] == OPPORTUNITY_CANCELED_BY_RIDER) {
        GlobalState.homePageDataRefresher();
        GlobalMethods.showInfoDialogNew(
          context: navigatorKey.currentContext!,
          onClick: () {
            closeScreen(navigatorKey.currentContext!);
          },
          title: "The opportunity has been canceled by the rider.",
        );
      } else if (jsonDecode(pt)['success_type'] == NEW_RIDE_REQUESTED) {
        OnRideRequest onRideRequest = OnRideRequest.fromJson(
          jsonDecode(pt)['result'],
        );
        newRiderAlertEmitter(onRideRequest: onRideRequest);
      } else if (jsonDecode(pt)['success_type'] == "canceled") {
        OnRideRequest onRideRequest = OnRideRequest.fromJson(
          jsonDecode(pt)['result'],
        );
        if (GlobalState.pausableTimer.remainingTime > 0) {
          emit(RideCancelledStateMqtt(rideId: onRideRequest.id!));
        }
      } else if (jsonDecode(pt)['success_type'] == "stop_changed") {
        emit(
          MultipleStopMqtt(
            message: jsonDecode(pt)['success_message'].toString(),
          ),
        );
      } else if (jsonDecode(pt)['success_type'] == "stop_deleted") {
        emit(
          MultipleStopDeletedMqtt(
            message: jsonDecode(pt)['success_message'].toString(),
          ),
        );
      } else if (jsonDecode(pt)['success_type'] == "offline") {
        emit(
          OfflineStateMqtt(
            messsage: jsonDecode(pt)['success_message'].toString(),
          ),
        );
      } else if (jsonDecode(pt)['success_type'] == "ride_alert") {
        GlobalMethods.infoToast(
          navigatorKey.currentContext!,
          jsonDecode(pt)['success_message'].toString(),
        );
      } else if (jsonDecode(pt)['success_type'] == "arriving" ||
          jsonDecode(pt)['success_type'] == "arrived") {
        String message = jsonDecode(pt)['success_message'].toString();

        emit(InitialRideLoadedMqtt());
      } else if (jsonDecode(pt)['success_type'] == "destination") {
        String message = jsonDecode(pt)['success_message'].toString();
        OnRideRequest onRideRequest = OnRideRequest.fromJson(
          jsonDecode(pt)['result'],
        );

        emit(
          DestinationChangedStateMqtt(
            message: message,
            onRideRequest: onRideRequest,
          ),
        );
      } else if (jsonDecode(pt)['success_type'] == "pickup_changed") {
        emit(PickUpChangedStateMqtt());
      } else if (jsonDecode(pt)['success_type'] == "rider_current_location") {
        emit(
          NewRiderLocationStateMqtt(
            location: AppLocationType(
              latitude: jsonDecode(pt)['result']['latitude'],
              longitude: jsonDecode(pt)['result']['longitude'],
            ),
          ),
        );
      }
    });

    GlobalState.MqttConnectionState.value =
        GlobalState.mqttClient?.connectionStatus?.state.name ?? "";

    if (GlobalState.mqttClient?.connectionStatus?.state ==
        MqttConnectionState.connected) {
      emit(MqttConnectedState());
    } else {
      emit(MqttErrorState());
    }
  }
}

// {"type":"type",
// "link":"link",
// "incomplete list":[{"type":"type","link","message":"messageeeee"},{"type":"type","link","message":"messageeeee"},{"type":"type","link","message":"messageeeee"},]
// }
