class NewRideActionModel {
  bool status;
  String message;
  NewRideActionData? data;

  NewRideActionModel({required this.status, required this.message, this.data});

  factory NewRideActionModel.fromMap(Map<String, dynamic> json) {
    return NewRideActionModel(
      status: json['status'],
      message: json['message'] ?? "Done",
      data: json['data'] != null
          ? NewRideActionData.fromMap(json['data'])
          : null,
    );
  }
}

class NewRideActionData {
  int isAlreadyAcceptedByOtherDriver;
  NewRideActionData({required this.isAlreadyAcceptedByOtherDriver});
  factory NewRideActionData.fromMap(Map<String, dynamic> json) {
    return NewRideActionData(
      isAlreadyAcceptedByOtherDriver:
          (json['is_already_accepted_by_other_driver'] ?? 0) as int,
    );
  }
}
