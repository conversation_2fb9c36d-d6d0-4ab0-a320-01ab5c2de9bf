
class CancellationReason {
  final int id;
  final String name;
  final int status;
  final bool isEmergency;
  final String? createdAt;
  final String? updatedAt;

  CancellationReason({
    required this.id,
    required this.name,
    required this.status,
    this.isEmergency = false,
    this.createdAt,
    this.updatedAt,
  });

  // Convert a CancellationReason instance to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'status': status,
      'is_emergency': isEmergency,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  // Create a CancellationReason instance from a JSON map
  factory CancellationReason.fromJson(Map<String, dynamic> json) {
    return CancellationReason(
      id: json['id'],
      name: json['name'],
      status: json['status'],
      isEmergency: json['is_emergency'] ?? false,
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }
}
