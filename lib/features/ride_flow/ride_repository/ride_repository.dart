import 'package:geocoding/geocoding.dart';
import 'package:rooo_driver/features/homepage/model/advertisment_response_model.dart';
import 'package:rooo_driver/features/homepage/model/homepage_response_model.dart';
import 'package:rooo_driver/features/homepage/model/user_detail_response_model.dart';
import 'package:rooo_driver/features/ride_flow/model/basic_ride_related+models.dart';
import 'package:rooo_driver/features/ride_flow/model/change_destination_response_model.dart';
import 'package:rooo_driver/features/ride_flow/model/online_offline_response_model.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/models/app_setting_response_model.dart';
import 'package:rooo_driver/global/models/current_ride_response_model.dart';

class RideRepository {
  //   Future<UserResponseModel > getUserDetail({required int driverId}) async {
  //   return UserResponseModel.fromJson(await handleResponse(await buildHttpResponse(
  //       'user-detail?id=$driverId',
  //       method: HttpMethod.GET)));
  // }
  Future<HomePageResponseModel> getHomePageDataApi() async {
    return HomePageResponseModel.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'get-driver-dashboard?status=upcoming',
          method: HttpMethod.GET,
        ),
      ),
    );
  }

  Future<AdvertismentResponseModel> getAdvertismentApi({
    required String type,
  }) async {
    return AdvertismentResponseModel.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'advertisements?type=${type}',
          method: HttpMethod.GET,
        ),
      ),
    );
  }

  Future<AppSettingResponseModel> getAppSettingApi() async {
    return AppSettingResponseModel.fromJson(
      await handleResponse(
        await buildHttpResponse('appsetting', method: HttpMethod.GET),
      ),
    );
  }

  Future<UserDetailResponseModel> getUserDetailApi({int? userId}) async {
    return UserDetailResponseModel.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'user-detail?id=$userId',
          method: HttpMethod.GET,
        ),
      ),
    );
  }

  Future<UserDetailResponseModel> changeOnlineOfflineStatusApi({
    int? userId,
  }) async {
    return UserDetailResponseModel.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'user-detail?id=$userId',
          method: HttpMethod.GET,
        ),
      ),
    );
  }

  Future<OnlineOfflineResponseModel> getOnlineOfflineStatusDummyApi({
    int? userId,
  }) async {
    return OnlineOfflineResponseModel.fromJson(
      {
        "status": false,
        "message": "Quiz mssage",

        "data": {
          "type": "driver_document",
          "status": 1,
          "link": "https://example.com",
          "incomplete list": [
            {
              "type": "quiz",
              "link": "https://example.com/section1",
              "message": "This is the first incomplete section.",
            },
            {
              "type": "section 2",
              "link": "https://example.com/section2",
              "message": "This is the second incomplete section.",
            },
            {
              "type": "section 3",
              "link": "https://example.com/section3",
              "message": "This is the third incomplete section.",
            },
          ],
        },
      },

      // await handleResponse( await buildHttpResponse(
      //   'user-detail?id=$userId',
      //   method: HttpMethod.GET))
    );
  }

  Future<ChangeDestinationResponseModel> changeDeStinationApi({
    required Map request,
  }) async {
    return ChangeDestinationResponseModel.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'ride-destination-update',
          request: request,
          method: HttpMethod.POST,
        ),
      ),
    );
  }

  Future<CurrentRideResponseModel> getCurrentRideRequestApi() async {
    return CurrentRideResponseModel.fromJson(
      await handleResponse(
        await buildHttpResponse('current-riderequest', method: HttpMethod.GET),
      ),
    );
  }

  Future<StatusMessageModel> change_ride_status_api({
    required Map request,
    required int rideId,
  }) async {
    return StatusMessageModel.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'riderequest-update/$rideId',
          method: HttpMethod.POST,
          request: request,
        ),
      ),
    );
  }

  Future<StatusMessageModel> notify_admin_api({
    required Map<String, dynamic> request,
  }) async {
    return StatusMessageModel.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'arrived-admin-notification',
          method: HttpMethod.POST,
          request: request,
        ),
      ),
    );
  }

  Future<String> get_user_address() async {
    List<Placemark> placemarks = await placemarkFromCoordinates(
      GlobalState.driverPosition!.latitude,
      GlobalState.driverPosition!.longitude,
    );
    Placemark place = placemarks[0];
    return '${place.street},${place.subLocality},${place.thoroughfare},${place.locality}';
  }

  Future<StatusMessageModel> complete_ride_api({required Map request}) async {
    return StatusMessageModel.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'complete-riderequest',
          method: HttpMethod.POST,
          request: request,
        ),
      ),
    );
  }

  Future<NewRideActionModel> acceptDeclineNewrideApi({
    required Map request,
  }) async {
    Response response = await buildHttpResponse(
      'riderequest-respond',
      method: HttpMethod.POST,
      request: request,
    );
    var json = jsonDecode(response.body);
    try {
      return NewRideActionModel.fromMap(json);
    } catch (e) {
      return NewRideActionModel(
        status: false,
        message: "Server error",
        data: null,
      );
    }
  }

  Future<StatusMessageModel> changeStopStatus({required Map request}) async {
    return StatusMessageModel.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'update-stop-status',
          method: HttpMethod.POST,
          request: request,
        ),
      ),
    );
  }

  Future<OnlineOfflineResponseModel> updateDriverOnlineUpdateStatusApi(
    int status,
    bool driverStatusChangeCase,
  ) async {
    Map request;

    request = {"status": "active"};

    if (driverStatusChangeCase) {
      request = {"status": "active", "is_online": status};
    }

    return OnlineOfflineResponseModel.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'update-user-status',
          method: HttpMethod.POST,
          request: request,
        ),
      ),
    );
  }

  Future<StatusMessageModel> uploadImage({
    required MultipartRequest multiPartRequest,
  }) async {
    return await StatusMessageModel.fromJson(
      await sendMultiPartApi(multiPartRequest: multiPartRequest),
    );
  }
}
