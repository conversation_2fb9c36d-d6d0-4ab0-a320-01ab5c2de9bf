import 'dart:math' as math;

class FineArea {
  final int id;
  final String title;
  final double latitude;
  final double longitude;

  FineArea({
    required this.id,
    required this.title,
    required this.latitude,
    required this.longitude,
  });

  factory FineArea.fromMap(Map<String, dynamic> json) {
    return FineArea(
      id: json['id'],
      title: json['title'],
      latitude: json['latitude'],
      longitude: json['longitude'],
    );
  }
}

class FineAreaResponse {
  bool status;
  String message;
  List<FineArea>? data;

  FineAreaResponse({
    required this.status,
    required this.message,
    required this.data,
  });

  factory FineAreaResponse.fromMap(Map<String, dynamic> json) {
    return FineAreaResponse(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? (json['data'] as List).map((i) => FineArea.fromMap(i)).toList()
          : null,
    );
  }
}

class FineAreaUtils {
  // Calculate distance between two points using Haversine formula
  static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371000; // Earth's radius in meters

    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);

    double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) *
        math.sin(dLon / 2) * math.sin(dLon / 2);

    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));

    return earthRadius * c;
  }

  static double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }

  // Check if driver is within 30 meters of any fine area
  static FineArea? getFineAreaWithinRange(
    List<FineArea> fineAreas,
    double driverLat,
    double driverLng,
    {double rangeInMeters = 30.0}
  ) {
    for (FineArea fineArea in fineAreas) {
      double distance = calculateDistance(
        driverLat,
        driverLng,
        fineArea.latitude,
        fineArea.longitude,
      );

      if (distance <= rangeInMeters) {
        return fineArea;
      }
    }
    return null;
  }
}
