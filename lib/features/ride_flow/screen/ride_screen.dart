import 'dart:ui' as ui;
import 'dart:math';
import 'package:custom_info_window/custom_info_window.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:lottie/lottie.dart' as llt;
import 'package:map_launcher/map_launcher.dart' as map_launcher;
import 'package:http/http.dart' as http;
import 'package:location/location.dart' as location;
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mapbox;
import 'package:mqtt_client/mqtt_client.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pinput/pinput.dart';
import 'package:polyline_animation_v1/polyline_animation_v1.dart';
import 'package:rooo_driver/background_tasks/BackgroundTasks.dart';
import 'package:rooo_driver/features/care/screens/add_care_screen.dart';
import 'package:rooo_driver/features/care/screens/care_screen.dart';
import 'package:rooo_driver/features/edit_profile/screens/edit_profile_screen.dart';
import 'package:rooo_driver/features/online_offline_status/screens/online_offline_status_screen.dart';
import 'package:rooo_driver/features/permissions/screens/required_permissions_screen.dart';
import 'package:rooo_driver/features/registration/screens/complete_profile_screen.dart';
import 'package:rooo_driver/features/ride_flow/model/multiple_stop_model.dart';
import 'package:rooo_driver/features/ride_flow/model/online_offline_model.dart';
import 'package:rooo_driver/features/ride_flow/ride_repository/ride_repository.dart';
import 'package:rooo_driver/features/ride_flow/screen/ride_screen_utils.dart';
import 'package:rooo_driver/features/ride_flow/screen/selfie_image.dart';
import 'package:rooo_driver/features/ride_flow/screen/fine_area_utils.dart';
import 'package:rooo_driver/features/ride_flow/service/heart_beat_service.dart';
import 'package:rooo_driver/features/ride_flow/widgets/directions_widget.dart';
import 'package:rooo_driver/features/ride_flow/widgets/ride_estimated_time.dart';
import 'package:rooo_driver/features/settings/map_setting/cubit/map_setting_cubit.dart';
import 'package:rooo_driver/features/vehicles/screens/add_vehicle_screen.dart';
import 'package:rooo_driver/features/vehicles/screens/vehicle_screen.dart';
import 'package:rooo_driver/global/constants/app_lottie.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/globalMethods/BackgroundAudio.dart';
import 'package:rooo_driver/global/globalMethods/battery_optimizations_service.dart';
import 'package:rooo_driver/global/globalMethods/local_waiting_time_model.dart';
import 'package:rooo_driver/global/globalMethods/one_signal_service.dart';
import 'package:rooo_driver/global/globalMethods/pausable_class.dart';
import 'package:rooo_driver/global/globalMethods/zego_service.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';
import 'package:rooo_driver/global/widgets/loader_bottom_sheet.dart';
import 'package:rooo_driver/screens/dashboard/widgets/call_button.dart';
import 'package:rooo_driver/screens/dashboard/widgets/chat_count.dart';
import 'package:rooo_driver/screens/dashboard/widgets/new_ride_amount_widget.dart';
import 'package:rooo_driver/screens/dashboard/widgets/offline_box.dart';
import 'package:rooo_driver/screens/dashboard/widgets/online_box.dart';
import 'package:rooo_driver/screens/dashboard/widgets/rider_contact_widget.dart';
import 'package:rooo_driver/screens/dashboard/widgets/timer_widget.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:workmanager/workmanager.dart';
import '../../advertisements/models/advertisement_model.dart';

class RideScreen extends StatefulWidget {
  final RideModel? rideModel;
  final bool isNewSignUp;
  const RideScreen({super.key, this.rideModel, this.isNewSignUp = false});

  @override
  State<RideScreen> createState() => _RideScreenState();
}

class _RideScreenState extends State<RideScreen>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  DateTime _lastAppPausedTime = DateTime.now();
  DateTime _lastAppInActiveTime = DateTime.now();

  bool _isOfflineAudioPlayed = false;
  bool _isAppInitDone = false;
  bool _areAllPermissionsAllowed = false;
  bool _isLocationPermissionForMapAllowed = false;
  bool _checkingMapLocationPermission = true;
  mapbox.MapboxMap? mapboxMap;
  late mapbox.PointAnnotationManager _pointAnnotationManager;
  List<mapbox.PointAnnotation> _mapAnnotations = [];
  late mapbox.PolylineAnnotationManager _polylineAnnotationManager;
  Set<Factory<OneSequenceGestureRecognizer>> gestureRecognizers = {
    Factory<OneSequenceGestureRecognizer>(() => EagerGestureRecognizer()),
  };

  _onMapCreated(mapbox.MapboxMap mapboxMap) async {
    this.mapboxMap = mapboxMap;
    // disable scale bar
    this.mapboxMap!.scaleBar.updateSettings(
      mapbox.ScaleBarSettings(enabled: false),
    );

    _polylineAnnotationManager = await mapboxMap.annotations
        .createPolylineAnnotationManager();
    _pointAnnotationManager = await mapboxMap.annotations
        .createPointAnnotationManager();
    this.mapboxMap?.location.updateSettings(
      mapbox.LocationComponentSettings(
        enabled: true,
        pulsingEnabled: true,
        // puckBearingEnabled: true,
        locationPuck: mapbox.LocationPuck(
          locationPuck3D: mapbox.LocationPuck3D(
            modelUri: 'asset://assets/3d_models/arrow4.glb',
            modelScale: [20, 20, 20],
            // modelScale: [0.05, 0.05, 0.01],
            modelRotation: [
              90,
              -90,
              (GlobalState.latestLocation?.heading ?? 0.0).toDouble() + 90,
            ],
          ),
        ),
      ),
    );
  }

  bool _isOfflineDialogBoxShown = false;
  double _cameraZoom = 16.5;

  num _driver_rating = 0.0;

  DateTime _lastTime = DateTime.now();

  List<map_launcher.Waypoint> _wayPoints = [];

  // Fine area data storage
  List<FineArea> _fineAreas = [];

  // Fine area checking variables
  DateTime? _lastMovementTime;
  bool _isDriverStationary = false;
  FineArea? _lastShownFineArea;
  static const Duration STATIONARY_THRESHOLD = Duration(seconds: 10);

  bool _isLocationPermisionGranted = false;

  location.Location locationPlugin = new location.Location();

  double _currentSpeed = 0.0;

  // Speed thresholds
  static const double HIGH_SPEED_THRESHOLD = 35.0; // km/h
  static const double CONSIDER_STOPPED_THRESHOLD = 5.0; // km/h
  // Map perspective settings
  static const double HIGH_SPEED_PITCH = 55.0; // degrees
  static const double LOW_SPEED_PITCH = 0.0; // degrees
  bool _mapInitialCenter = false;

  void _checkLocationPermission() {
    Permission.locationAlways.status.then((value) {
      _isLocationPermisionGranted = value == PermissionStatus.granted;
      if (_isLocationPermisionGranted) {
        _isLocationPermissionForMapAllowed = true;
        _onLocationPermissionGranted();
      }
      setState(() {
        _checkingMapLocationPermission = false;
      });
    });
  }

  Future<void> _checkNotificationPermission() async {
    Permission.notification.status.then((value) {
      if (value == PermissionStatus.granted) {
        OneSignalService.init();
        if (ZegoVoiceCallService.isinitialized.value == -1 ||
            ZegoVoiceCallService.isinitialized.value == 0) {
          if (!widget.isNewSignUp) {
            Navigator.of(context).popUntil((route) => route.isFirst);
          }
          ZegoVoiceCallService.init(
            navigatorKey: navigatorKey,
            appId: AppCred.zegoAppId,
            appSign: AppCred.zegoAppSign,
            callerId: sharedPref.getInt(USER_ID).toString(),
            callerName: sharedPref.getString(FIRST_NAME).toString(),
          );
        }
      }
    });
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    /* if app terminated, cancel */
    if (state == AppLifecycleState.detached) {
      Workmanager().cancelAll();
    }
    if (state == AppLifecycleState.resumed) {
      var backgroundNewRide = await BackgroundTasks.getBackgroundRide();
      if (backgroundNewRide.ride != null) {
        if (GlobalState.current_ride?.value.onRideRequest != null) {
          if (GlobalState.isNewQueuedRideShowing == false) {
            _onQueuedRide(
              onRideRequest: backgroundNewRide.ride!,
              playAudio: false,
              remainingSeconds: backgroundNewRide.seconds,
            );
          }
        } else {
          if (GlobalState.isNewRideShowing == false) {
            _onNewRide(
              onRideRequest: backgroundNewRide.ride!,
              playAudio: false,
              remainingSeconds: backgroundNewRide.seconds,
            );
          }
        }
      }
      _isAppUserFacing = true;
      /* if there is a new incoming ride, then play new ride sound */
      if (_newride.value.id != null) {
        BackgroundAudio.playNewRideAudio();
      }
    } else if (state == AppLifecycleState.inactive) {
      _lastAppInActiveTime = DateTime.now();
    } else {
      _isAppUserFacing = false;
      _lastAppPausedTime = DateTime.now();
    }
    if (GlobalState.location_permission.value ==
            PermissionStatus.granted.name &&
        GlobalState.notification_permission.value ==
            PermissionStatus.granted.name) {
    } else {
      if (state == AppLifecycleState.resumed) {
        try {
          _checkLocationPermission();
          _checkNotificationPermission();

          if (DateTime.now().difference(_lastAppPausedTime).inSeconds > 3 ||
              DateTime.now().difference(_lastAppInActiveTime).inSeconds > 3) {
            if (!GlobalState.isNewRideShowing &&
                !GlobalState.isNewQueuedRideShowing) {
              _getCurrentRide();
            }
          }

          GlobalState.is_foreground_state = true;
          if (_isOnline.value == 1 || _ride.value.id != null) {
            appLocation.enableBackgroundMode(enable: true);

            appLocationStream?.cancel();

            GlobalMethods.startsevertrackingTracking();
          } else if (_isOnline.value == 0) {
            appLocation.enableBackgroundMode(enable: false);
            appLocationStream?.cancel();
            GlobalMethods.stopServerTracking();
          }
          setState(() {
            _checkingMapLocationPermission = false;
          });
        } catch (e) {}
      } else if (state == AppLifecycleState.paused) {
        GlobalState.is_foreground_state = false;
        if (_isOnline.value == 1 || _ride.value.id != null) {
          GlobalState.driver_server_timer?.cancel();
          GlobalState.driver_server_timer = null;

          GlobalMethods.startsevertrackingTracking();
        }
      }
    }
  }

  bool _isUserInteractingWithMap = false;
  Timer? _userInteractionTimer;
  AudioPlayer? _audioPlayer;
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  List<List<mapbox.Position>> _savedPoolPolylinePoint = [];
  ValueNotifier<bool> isAudioPermissionGranted = ValueNotifier(false);
  CustomInfoWindowController _customInfoWindowController =
      CustomInfoWindowController();
  bool _homePageDataLoaded = false;
  HomePageDataModel? _homePageData;
  ValueNotifier<int> _isOnline = ValueNotifier(
    GlobalState.isDriverOnline ? 1 : 0,
  );
  PanelController _panelController = PanelController();
  PanelController _panelControllerForRideBottomSheet = PanelController();
  ScrollController _scrollController = ScrollController();
  TextEditingController _startPasswordController = TextEditingController();
  String _oldRideStatus = "";
  ValueNotifier<RideModel> _ride = ValueNotifier(RideModel());
  ValueNotifier<OnRideRequest> _newride = ValueNotifier(OnRideRequest());
  ValueNotifier<OnRideRequest> _queuedRide = ValueNotifier(OnRideRequest());

  ValueNotifier<OnRideRequest> _selectedOnrideRequest = ValueNotifier(
    OnRideRequest(),
  );
  ValueNotifier<List<OnRideRequest>> _poolRides = ValueNotifier([]);
  late StreamSubscription<RideFlowState> _rideFlowListenerListener;
  ValueNotifier<Set<int>> _arrived_otp_verified = ValueNotifier({});
  ValueNotifier<File> imagefile1 = ValueNotifier(File("path"));
  ValueNotifier<File> imagefile2 = ValueNotifier(File("path"));
  ValueNotifier<File> imagefile3 = ValueNotifier(File("path"));
  ValueNotifier<File> imagefile4 = ValueNotifier(File("path"));
  TutorialCoachMark? tutorialCoachMark;
  GlobalKey _earningKeyButton = GlobalKey();
  GlobalKey _onlineKeyButton = GlobalKey();
  GlobalKey _drawerKeyButton = GlobalKey();
  GlobalKey _blogKeyButton = GlobalKey();
  GlobalKey _currentLocationKeyButton = GlobalKey();
  GlobalKey _emergencyKeyButton = GlobalKey();
  GlobalKey keyButton5 = GlobalKey();
  GlobalKey keyBottomNavigation1 = GlobalKey();
  GlobalKey keyBottomNavigation2 = GlobalKey();
  GlobalKey keyBottomNavigation3 = GlobalKey();
  Timer? _stop_waiting_time_timer;
  ValueNotifier<int> _stop_waiting_time_timer_value = ValueNotifier(-1);
  ReceivePort _port = ReceivePort();
  Timer? _adminNotifyWaitingTimer;
  ValueNotifier<int> _adminNotifyWaitingTimeTimerValue = ValueNotifier(-1);
  Timer? _arrivedStateWaitingTimer;
  ValueNotifier<num> _arrivedStateWaitingTimerValue = ValueNotifier(-1);
  bool _isArrivedWaitingTimeNotifyAPICalled = false;
  bool _isStopWaitingTimeNotifyAPICalled = false;

  _startArrivedStateWaitingTimer({required int waiting_time}) {
    DateTime startTime = DateTime.now();
    if (waiting_time > 0) {
      startTime = startTime.subtract(Duration(seconds: waiting_time));
    }
    GlobalMethods.saveStartWaitingTimeData(
      data: LocalWaitingTimeModel(
        startTime: startTime,
        endTime: null,
        type: LocalWaitingTimeTyes.arrived,
      ),
    );
    if (!_isArrivedWaitingTimeNotifyAPICalled) {
      notifyWaitingTimeStarted(rideId: _ride.value.onRideRequest?.id ?? 0);
      _isArrivedWaitingTimeNotifyAPICalled = true;
    }
    _adminNotifyWaitingTimer?.cancel();
    _adminNotifyWaitingTimeTimerValue.value = -1;
    _arrivedStateWaitingTimerValue.value = waiting_time;
    _arrivedStateWaitingTimer?.cancel();
    _arrivedStateWaitingTimer = null;
    _arrivedStateWaitingTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      _arrivedStateWaitingTimerValue.value =
          _arrivedStateWaitingTimerValue.value + 1;
    });
  }

  Future<bool> _showTutorial() async {
    if (widget.isNewSignUp) {
      final BuildContext newContext = context;
      final NavigatorState navigator = Navigator.of(newContext);
      while (ModalRoute.of(context)?.isCurrent != true && navigator.canPop()) {
        navigator.pop();
        await Future.delayed(Duration(milliseconds: 100));
      }

      if (_scaffoldKey.currentState?.isDrawerOpen ?? false) {
        _scaffoldKey.currentState?.closeDrawer();
        await Future.delayed(Duration(milliseconds: 300));
      }

      _createTutorial();
      tutorialCoachMark?.show(context: context);
      return false;
    }
    _initApp();
    return true;
  }

  Future<void> _onLocationPermissionGranted() async {
    if (_isAppInitDone == false) {
      _justDisplayAppRequiredNotificationPermission();

      locationPlugin.onLocationChanged.listen((locationData) async {
        if (locationData.latitude == null) {
          return;
        }
        try {
          GlobalState.latestLocation = locationData;
          GlobalState.driverPosition = LatLng(
            locationData.latitude ?? 0,
            locationData.longitude ?? 0,
          );
          if (GlobalState.lastKnownLocation == null) {
            GlobalState.lastKnownLocation = locationData;
          }
          if (_ride.value.onRideRequest != null &&
              _ride.value.onRideRequest!.status == ARRIVING) {
            await RideScreenUtils.onNearArrivedLocation(
              currentLatitude: locationData.latitude!.toDouble(),
              currentLongitude: locationData.longitude!.toDouble(),
              targetLatitude: double.parse(
                _ride.value.onRideRequest!.startLatitude!,
              ),
              targetLongitude: double.parse(
                _ride.value.onRideRequest!.startLongitude!,
              ),
              callback: () {
                _markArrived(
                  isAutomatic: true,
                  rideId: _ride.value.onRideRequest!.id!,
                  pickupLocation: LatLng(
                    double.parse(_ride.value.onRideRequest!.startLatitude!),
                    double.parse(_ride.value.onRideRequest!.startLongitude!),
                  ),
                  currentLocation: LatLng(
                    locationData.latitude!.toDouble(),
                    locationData.longitude!.toDouble(),
                  ),
                );
              },
            );
          }
          if (_ride.value.onRideRequest != null &&
              _ride.value.onRideRequest!.status == IN_PROGRESS) {
            await RideScreenUtils.onNearReachedLocation(
              currentLatitude: locationData.latitude!.toDouble(),
              currentLongitude: locationData.longitude!.toDouble(),
              targetLatitude: double.parse(
                _ride.value.onRideRequest!.endLatitude!,
              ),
              targetLongitude: double.parse(
                _ride.value.onRideRequest!.endLongitude!,
              ),
              callback: () async {
                if (_ride.value.onRideRequest!.status != REACHED) {
                  await _markReached(
                    destinationLocation: LatLng(
                      double.parse(_ride.value.onRideRequest!.endLatitude!),
                      double.parse(_ride.value.onRideRequest!.endLongitude!),
                    ),
                    currentLocation: GlobalState.driverPosition!,
                    onrideRequest: _ride.value.onRideRequest!,
                    isAutomatic: true,
                  );
                } else {
                  // _endRide(onrideRequest: _ride.value.onRideRequest!);
                }
              },
            );
          }
          _currentSpeed = locationData.speed!.toDouble() * 3.6;
        } catch (e) {
          print(e.toString());
        }
      });

      _startDeviceTracking().then((onValue) async {
        if (onValue) {
          _checkOnlineOfflineStatus();
          if (_homePageData?.is_live_photo_required ?? false) {
            if (GlobalState.isLiveImageScreenOpened == false) {
              _uploadSelfie();
            }
          }
          _mqttForUser();

          _getCurrentRide();
          // if (!widget.isNewSignUp) {
          //   Navigator.of(context).popUntil((route) => route.isFirst);
          // }
        }
      });

      setState(() {
        _isLocationPermisionGranted = true;
        _isLocationPermissionForMapAllowed = true;
      });
      _isAppInitDone = true;
      if (!await BatteryOptimizationsService.areAllOptimizationsDisabled()) {
        BatteryOptimizationsService.initPrompt();
      }
    }
  }

  void _createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: Colors.red,
      textSkip: "Ok, got it",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ui.ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onFinish: () {
        _initApp();
        GlobalMethods.toast("Tutorial completed, you are good to go");
      },
      onClickTarget: (target) {},
      onClickTargetWithTapPosition: (target, tapDetails) {},
      onClickOverlay: (target) {},
      onSkip: () {
        _initApp();
        return true;
      },
    );
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "Earnings",
        targetPosition: TargetPosition(
          Size(60, 60),
          Offset(MediaQuery.sizeOf(context).width / 2.3, 50),
        ),
        // keyTarget: _earningKeyButton,
        alignSkip: Alignment.topRight,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Earnings",
                      style: AppTextStyles.header(color: Colors.white),
                    ),
                    height10,
                    Text(
                      "You can see your daily earnings here.",
                      style: AppTextStyles.title(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "Earning button",
        keyTarget: _drawerKeyButton,
        alignSkip: Alignment.topRight,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Menu",
                      style: AppTextStyles.header(color: Colors.white),
                    ),
                    height10,
                    Text(
                      "You can see all other options here.",
                      style: AppTextStyles.title(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "Earning button",
        keyTarget: _emergencyKeyButton,
        alignSkip: Alignment.topRight,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return Padding(
                padding: const EdgeInsets.only(top: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Emergency Contacts",
                      style: AppTextStyles.header(color: Colors.white),
                    ),
                    height10,
                    Text(
                      "You can access emergency contacts here,\n Also you can call 000.",
                      style: AppTextStyles.title(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "Earning button",
        keyTarget: _currentLocationKeyButton,
        alignSkip: Alignment.topRight,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return Padding(
                padding: const EdgeInsets.only(top: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Current location",
                      style: AppTextStyles.header(color: Colors.white),
                    ),
                    height10,
                    Text(
                      "Click on this to get your location on the map.",
                      style: AppTextStyles.title(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "Online button",
        keyTarget: _onlineKeyButton,
        alignSkip: Alignment.topRight,
        enableOverlayTab: true,
        shape: ShapeLightFocus.RRect,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return Padding(
                padding: const EdgeInsets.only(top: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Go Online",
                      style: AppTextStyles.header(color: Colors.white),
                    ),
                    height10,
                    Text(
                      "Slide it to go online and get new ride request.",
                      style: AppTextStyles.title(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "Blog panel",
        keyTarget: _blogKeyButton,
        alignSkip: Alignment.topRight,
        enableOverlayTab: true,
        shape: ShapeLightFocus.RRect,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return Padding(
                padding: const EdgeInsets.only(top: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Blogs and other updates",
                      style: AppTextStyles.header(color: Colors.white),
                    ),
                    height10,
                    Text(
                      "Slide up or click here to see blogs and other updates.",
                      style: AppTextStyles.title(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );

    return targets;
  }

  Future<List<mapbox.Position>> getRouteCoordinates({
    required mapbox.Position origin,
    required mapbox.Position destination,
    List<mapbox.Position>? waypoints,
  }) async {
    List<mapbox.Position> polylineCoordinates = [];
    try {
      String url =
          'https://api.mapbox.com/directions/v5/mapbox/driving/'
          '${origin.lng},${origin.lat}';
      if (waypoints?.isNotEmpty ?? false) {
        String waypointStr = waypoints!
            .map((waypoint) => '${waypoint.lng},${waypoint.lat}')
            .join(';');
        url = '$url;$waypointStr';
      }
      url =
          '$url;${destination.lng},${destination.lat}'
          '?geometries=geojson&overview=full&steps=true&access_token=${AppCred.mapBoxPublicTokenKey}';
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['routes'].isEmpty) {
          return polylineCoordinates;
        }
        final route = data['routes'][0];
        final geometry = route['geometry'];
        final coordinates = geometry['coordinates'];

        for (var coord in coordinates) {
          polylineCoordinates.add(mapbox.Position(coord[0], coord[1]));
        }

        return polylineCoordinates;
      } else {
        return polylineCoordinates;
      }
    } catch (e) {
      return polylineCoordinates;
    }
  }

  List<AppLocationType> _currentPolylineCoordinates = [];

  Future<List<mapbox.Position>> createPolyline({
    required mapbox.Position startLocation,
    required mapbox.Position endLocation,
    List<mapbox.Position>? waypoints,
    required int colors,
    double? lineOpacity,
    double? lineWidth,
  }) async {
    List<mapbox.Position> coordinates = await getRouteCoordinates(
      origin: startLocation,
      destination: endLocation,
      waypoints: waypoints,
    );
    _currentPolylineCoordinates = coordinates
        .map(
          (e) => AppLocationType(
            latitude: e.lat.toDouble(),
            longitude: e.lng.toDouble(),
          ),
        )
        .toList();

    await _polylineAnnotationManager.create(
      mapbox.PolylineAnnotationOptions(
        lineOpacity: lineOpacity ?? 1,
        lineWidth: lineWidth ?? 16,
        lineColor: colors,
        // lineBorderWidth: 4,
        // lineBorderColor: colors,
        lineJoin: mapbox.LineJoin.ROUND,
        geometry: mapbox.LineString(coordinates: coordinates),
      ),
    );
    return coordinates;
  }

  Future<List<mapbox.Position>> _setPolyLines({
    required double initialLat,
    required double initialLang,
    required double finalLat,
    required double finalLang,
    required Color colors,
    required int id,
    List<mapbox.Position>? savedPolylineCoordinates,
    List<mapbox.Position>? wayPoints,
    double? lineOpacity,
    double? lineWidth,
  }) async {
    await _polylineAnnotationManager.deleteAll();
    return await createPolyline(
      waypoints: wayPoints,
      colors: colors.value,
      startLocation: mapbox.Position(initialLang, initialLat),
      endLocation: mapbox.Position(finalLang, finalLat),
      lineOpacity: lineOpacity,
      lineWidth: lineWidth,
    );
  }

  _ondestinationChangedInsideModel({
    required StopsModel data,
    required int length,
  }) async {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Column(
            children: [
              Text(
                "Rider changed the destination location",
                style: AppTextStyles.title(),
              ),
              height10,
              Text(
                data.title,
                style: TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
              ),
              height10,
              Text("Updating the map....", style: TextStyle(fontSize: 12)),
              height10,
              AppLoader(size: 12),
            ],
          ),
        );
      },
    );
    BackgroundAudio.playNewRideAudio();
    final Map<PolylineId, Polyline> _polylines = {};
    GoogleMapController? _controller;
    List<LatLng> list = [];
    ValueNotifier<bool> loaded = ValueNotifier(false);

    try {
      List<mapbox.Position> result = await getRouteCoordinates(
        origin: mapbox.Position(
          data.currentLat ?? GlobalState.driverPosition!.latitude,
          data.currentLng ?? GlobalState.driverPosition!.longitude,
        ),
        destination: mapbox.Position(data.stopLng, data.stopLng),
      );
      Navigator.pop(context);

      if (result.isNotEmpty) {
        result.forEach((element) {
          list.add(LatLng(element.lat.toDouble(), element.lng.toDouble()));
        });

        showDialog(
          barrierDismissible: false,
          context: context,
          builder: (context) {
            final PolylineAnimator _animator = PolylineAnimator();

            void _startPolylineAnimation() {
              _animator.animatePolyline(
                list,
                'polyline_id',
                Colors.black,
                Colors.blue,
                _polylines,
                () {
                  loaded.notifyListeners();
                },
              );
            }

            return AlertDialog(
              contentPadding: EdgeInsets.zero,
              content: Column(
                children: [
                  SizedBox(
                    height: MediaQuery.of(context).size.height * .50,
                    child: ValueListenableBuilder(
                      valueListenable: loaded,
                      builder: (context, value, child) {
                        return GoogleMap(
                          onMapCreated: (controller) {
                            _controller = controller;
                            _startPolylineAnimation();
                          },
                          polylines: _polylines.values.toSet(),
                          initialCameraPosition: CameraPosition(
                            target: LatLng(
                              data.currentLat ??
                                  GlobalState.driverPosition!.latitude,
                              data.currentLng ??
                                  GlobalState.driverPosition!.longitude,
                            ),
                            zoom: _cameraZoom,
                          ),
                        );
                      },
                    ),
                  ),
                  Padding(
                    padding: screenPadding,
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("Destination changed"),
                            Text(length.toString() + " " + "times"),
                          ],
                        ),
                        Divider(),
                        Column(
                          children: [
                            Text("From", style: AppTextStyles.title()),
                            Text(data.currentAdress ?? "", maxLines: 10),
                          ],
                        ),
                        height10,
                        Column(
                          children: [
                            Text("To", style: AppTextStyles.title()),
                            Text(data.title, maxLines: 10),
                          ],
                        ),
                        AppButton(
                          text: "OK",
                          onPressed: () async {
                            BackgroundAudio.stopAudio();
                            Navigator.pop(context);
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      }
    } catch (e) {
      BlocProvider.of<RideFlowCubit>(
        context,
      ).emit(RideFlowErrorState(message: e.toString()));
      Navigator.pop(context);
    }
    ;
  }

  Future<void> cancel_ride_request_by_driver({
    required int rideId,
    required int reason_id,
    required List<Map<String, dynamic>> waitingCharges,
  }) async {
    Map request = {
      "id": rideId,
      "status": "canceled",
      "cancel_by": "driver",
      "reason_id": reason_id,
      "waiting_time_list": waitingCharges,
    };
    BlocProvider.of<RideFlowCubit>(
      context,
    ).cancel_ride_request_by_driver(request: request, rideId: rideId);
  }

  Future<void> _showEmergencyDialog(OnRideRequest onRideRequest) async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: BorderSide(
              color: MediaQuery.platformBrightnessOf(context) == Brightness.dark
                  ? Colors.grey.shade700
                  : Colors.grey,
            ),
          ),
          titlePadding: EdgeInsets.zero,
          backgroundColor:
              MediaQuery.platformBrightnessOf(context) == Brightness.dark
              ? Colors.black
              : Colors.white,
          title: Container(
            color: MediaQuery.platformBrightnessOf(context) == Brightness.dark
                ? Colors.grey.shade800
                : Colors.grey.shade200,
            height: 90,
            width: double.infinity,
            child: llt.Lottie.asset("assets/lottie/alert.json"),
          ),
          content: Padding(
            padding: const EdgeInsets.all(6.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "This is an emergency cancellation. Please select how you would like to get assistance-",
                ),
                SizedBox(height: 20),
                AppButton(
                  text: "Call ROOO Care",
                  onPressed: () async {
                    final contactNumber = GlobalState
                        .appSettingModel
                        ?.settingModel
                        ?.contactNumber;
                    if (contactNumber != null) {
                      Navigator.pop(context);
                      launchUrl(Uri.parse('tel:$contactNumber'));
                    } else {
                      GlobalMethods.errorToast(
                        context,
                        "Contact number not available",
                      );
                    }
                  },
                ),
                SizedBox(height: 10),
                AppButton(
                  text: "Create Care Request",
                  onPressed: () async {
                    Navigator.pop(context);
                    GlobalMethods.pushScreen(
                      context: context,
                      screen: AddCareScreen(rideId: onRideRequest.id!),
                      screenIdentifier: ScreenIdentifier.NewCareScreen,
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  _startStopTime({required int id}) {
    Map<String, dynamic> request = {
      "timer_start": DateTime.now().toString(),
      "id": id,
    };

    BlocProvider.of<RideFlowCubit>(context).changeStopStatus(request: request);

    GlobalMethods.saveStartWaitingTimeData(
      data: LocalWaitingTimeModel(
        startTime: DateTime.now(),
        endTime: DateTime.now(),
        type: LocalWaitingTimeTyes.stop,
      ),
    );
  }

  _endStopTime({
    required int id,
    DateTime? startTime,
    required bool iscanceled,
  }) {
    DateTime _currentTime = DateTime.now();
    int? totalTime;
    if (startTime != null) {
      totalTime = _currentTime.difference(startTime).inSeconds;
    }
    Map<String, dynamic> request = {
      if (!iscanceled) "timer_end": _currentTime.toString(),
      "id": id,
      "status": "completed",
      if (!iscanceled) "total_time": totalTime,
    };

    BlocProvider.of<RideFlowCubit>(context).changeStopStatus(request: request);
    GlobalMethods.saveEndWaitingTimeData(
      data: LocalWaitingTimeModel(
        startTime: DateTime.now(),
        endTime: DateTime.now(),
        type: LocalWaitingTimeTyes.stop,
      ),
    );
  }

  _reachedAtStop({required StopsModel stop}) {
    GlobalMethods.showConfirmationDialog(
      context: context,
      onPositiveAction: () async {
        Map<String, dynamic> request = {
          "is_arrived": true,
          "id": stop.id,
          "status": "arrived",
        };

        BlocProvider.of<RideFlowCubit>(
          context,
        ).changeStopStatus(request: request);
        // }
      },
      title: "Are you sure you want to mark this stop as reached?",
    );
  }

  _checkAdminNotifyWaitingTime({required OnRideRequest onrideRequest}) {
    if (onrideRequest.status != ARRIVED) {
      _adminNotifyWaitingTimer?.cancel();
      return;
    }
    int timeDifferenceInSeconds = 0;
    if (true) {
      bool isScheduled = onrideRequest.isSchedule == 1;
      GlobalState.adminNotificationTimers.setRideType(isScheduled: isScheduled);

      DateTime arrived_time = DateTime.parse(onrideRequest.arrived_time!);

      DateTime now = DateTime.now();
      timeDifferenceInSeconds = now.difference(arrived_time).inSeconds;
      if (timeDifferenceInSeconds > GlobalState.adminNotifyLimit) {
        _adminNotifyWaitingTimer?.cancel();
        _startArrivedStateWaitingTimer(
          waiting_time: timeDifferenceInSeconds - GlobalState.adminNotifyLimit,
        );
      } else {
        _adminNotifyWaitingTimeTimerValue.value =
            GlobalState.adminNotifyLimit - timeDifferenceInSeconds;
        _adminNotifyWaitingTimer = Timer.periodic(Duration(seconds: 1), (
          timer,
        ) {
          if (_adminNotifyWaitingTimeTimerValue.value < 1) {
            setState(() {
              _adminNotifyWaitingTimer?.cancel();
            });

            timeDifferenceInSeconds = now.difference(arrived_time).inSeconds;
            if (_arrivedStateWaitingTimerValue.value == -1) {
              DateTime now = DateTime.now();

              timeDifferenceInSeconds = now.difference(arrived_time).inSeconds;

              if (timeDifferenceInSeconds > GlobalState.adminNotifyLimit) {
                _adminNotifyWaitingTimer?.cancel();
                _startArrivedStateWaitingTimer(
                  waiting_time:
                      timeDifferenceInSeconds - GlobalState.adminNotifyLimit,
                );
              }
            }
          } else {
            _adminNotifyWaitingTimeTimerValue.value =
                _adminNotifyWaitingTimeTimerValue.value - 1;
          }
        });
      }
    }
  }

  _arrivedNotifyAdmin({required int rideId}) async {
    Map<String, dynamic> request = {"rideId": rideId};

    BlocProvider.of<RideFlowCubit>(context).notify_admin(request: request);
  }

  _getCurrentRide() async {
    BlocProvider.of<RideFlowCubit>(context).getCurrenRide().then((onValue) {
      setState(() {
        //
      });
    });
  }

  _checkOnlineOfflineStatus() {
    if (GlobalState.isDriverOnline) {
      return;
    }
    BlocProvider.of<RideFlowCubit>(
      context,
    ).checkOnlineOfflineStatus(userId: sharedPref.getInt(USER_ID)!);
  }

  _getAppSetting() {
    BlocProvider.of<RideFlowCubit>(context).getAppSetting();
  }

  _mqttForUser() {
    BlocProvider.of<RideFlowCubit>(context).mqttForUser();
  }

  _changeOnlineOfflineStatus({required int makeOnline}) {
    BlocProvider.of<RideFlowCubit>(context).changeOnlineOfflineStatus2(
      makeOnline: makeOnline,
      userId: sharedPref.getInt(USER_ID)!,
    );
  }

  _handleMissingRegion() {
    GlobalMethods.showInfoDialog(
      barrierDismissible: false,
      context: context,
      positiveAction: () async {
        bool? result = await GlobalMethods.pushScreen(
          context: context,
          screen: EditProfileScreen(isFromDahboard: true),
          screenIdentifier: ScreenIdentifier.EditProfileScreen,
        );

        if (result == true) {
          Navigator.of(context).pop();
        }
      },
      title: language.handleMiddimgregionId,
    );
  }

  _showAdvertisment({required AdvertisementModel advertisement}) {
    GlobalMethods.showAdvertismentDialog(
      context: context,
      positiveAction: () {
        launchUrl(Uri.parse(advertisement.webUrl));
      },
      title: "",
      imageUrl: advertisement.imageURL,
    );
  }

  _getChatCount({required RideModel ride}) {
    if (ride.onRideRequest?.isPool ?? false) {
      if (ride.pool_rides == null) {
        if (!GlobalState.chat_count.value.any(
          (chat) => chat.rideId == ride.onRideRequest!.id,
        )) {
          GlobalState.chat_count.value.add(
            ChatCountModel(rideId: ride.onRideRequest!.id!, chatCount: 0),
          );
        } else {
          GlobalState.chat_count.value.removeWhere(
            (chat) => chat.rideId != ride.onRideRequest!.id!,
          );
        }
      } else {
        ride.pool_rides?.forEach((rides) {
          if (!GlobalState.chat_count.value.any(
            (chat) => chat.rideId == rides.id,
          )) {
            GlobalState.chat_count.value.add(
              ChatCountModel(rideId: rides.id!, chatCount: 0),
            );
          } else {
            GlobalState.chat_count.value.removeWhere(
              (chat) => chat.rideId != rides.id,
            );
          }
        });
      }
    } else {
      if (ride.onRideRequest != null) {
        if (!GlobalState.chat_count.value.any(
          (chat) => chat.rideId != ride.onRideRequest!.id,
        )) {
          GlobalState.chat_count.value.add(
            ChatCountModel(rideId: ride.onRideRequest!.id!, chatCount: 0),
          );
        } else {
          GlobalState.chat_count.value.removeWhere(
            (chat) => chat.rideId != ride.onRideRequest!.id!,
          );
        }
      }
    }

    log(GlobalState.chat_count.value.toString());
  }

  Future<void> _justDisplayAppRequiredNotificationPermission() async {
    var status = await Permission.notification.status;
    if (status == PermissionStatus.granted) {
      return;
    } else if (status == PermissionStatus.permanentlyDenied) {
      GlobalMethods.showConfirmationDialog(
        context: navigatorKey.currentContext!,
        barrierDismissible: true,
        onPositiveAction: () async {
          await openAppSettings();
        },
        onNegativeAction: () {},
        positiveText: "Ok",
        negativeText: "No",
        title:
            "To get the best experience, please allow the \"Notification\" permission.",
      );
    } else {
      if (Platform.isIOS) {
        FirebaseMessaging.instance.requestPermission();
        return;
      }
      GlobalMethods.showConfirmationDialog(
        context: navigatorKey.currentContext!,
        barrierDismissible: true,
        onPositiveAction: () async {
          FirebaseMessaging.instance.requestPermission();
        },
        onNegativeAction: () {},
        positiveText: "Ok",
        negativeText: "No",
        title:
            "To get the best experience, please allow the \"Notification\" permission.",
      );
    }
  }

  Future<bool> _checkRequiredPermissions() async {
    var result = await Future.wait([
      // Permission.notification.status,
      Permission.locationAlways.status,
      Permission.locationWhenInUse.status,
    ]);

    // _locationPermissionForMapAllowed = result[2] == PermissionStatus.granted;

    return result[0] == PermissionStatus.granted &&
        result[1] == PermissionStatus.granted;
  }

  Future<void> _startRideListner() async {
    _rideFlowListenerListener = context.read<RideFlowCubit>().stream.listen((
      state,
    ) async {
      if (state is RideFlowErrorState) {
        BackgroundAudio.playErrorAudio();

        GlobalMethods.errorToast(context, state.message);
      } else if (state is AppSettingLoadedState) {
        GlobalState.appSettingModel = state.appSettingModel;
        if (state.appSettingModel.settingModel?.notifyAdminWaitingTime !=
            null) {
          var timerSettings =
              state.appSettingModel.settingModel!.notifyAdminWaitingTime!;
          GlobalState.adminNotificationTimers.normalRideTimer =
              timerSettings.normalRideTime;
          GlobalState.adminNotificationTimers.scheduledRideTimer =
              timerSettings.scheduledRideTime;
        }
      } else if (state is HomePageDataLoadedState) {
        _onloadHomePageData(data: state.homePageData);
      } else if (state is AdvertismentDataLoadedState) {
        _showAdvertisment(advertisement: state.advertisement);
      } else if (state is OnlineErrorDataLoadedState) {
        getPositiveAction({required IncompleteSectionModel incomplete}) {
          if (incomplete.type == DRIVER_PENDINGS.quiz) {
            launchUrl(Uri.parse(incomplete.link));
          } else if (incomplete.type == DRIVER_PENDINGS.profileIncomplete) {
            GlobalMethods.pushScreen(
              context: context,
              screen: CompleteProfileScreen(
                first_name: sharedPref.getString(FIRST_NAME)!,
                last_name: sharedPref.getString(LAST_NAME)!,

                // name: sharedPref.getString(Name),
                userName: sharedPref.getString(USER_NAME),
                id: sharedPref.getInt(USER_ID)!,
                email: sharedPref.getString(USER_EMAIL),
                gender: sharedPref.getString(GENDER),
                otherGenderText: sharedPref.getString(OTHER_GENDER_TEXT),
              ),
              screenIdentifier: ScreenIdentifier.CompleteProfileScreen,
            );
          } else if (incomplete.type == DRIVER_PENDINGS.subscriptionPending ||
              incomplete.type == DRIVER_PENDINGS.subscriptionPayment ||
              incomplete.type == DRIVER_PENDINGS.subscriptionExpired) {
            launchUrl(Uri.parse(incomplete.link));
          } else if (incomplete.type == DRIVER_PENDINGS.documentUpload ||
              incomplete.type == DRIVER_PENDINGS.documentApproval) {
            GlobalMethods.pushScreen(
              context: context,
              screen: DocumentScreen(canGoToDashboard: false),
              screenIdentifier: ScreenIdentifier.DocumentScreen,
            );
          } else if (incomplete.type == DRIVER_PENDINGS.vehicleAdd) {
            GlobalMethods.pushScreen(
              context: context,
              screen: AddVehiclesScreen(),
              screenIdentifier: ScreenIdentifier.vehicleScreen,
            );
          } else if (incomplete.type == DRIVER_PENDINGS.vehicleApproval ||
              incomplete.type == DRIVER_PENDINGS.vehicleInactive) {
            GlobalMethods.pushScreen(
              context: context,
              screen: VehiclesScreen(),
              screenIdentifier: ScreenIdentifier.vehicleScreen,
            );
          } else if (incomplete.type == DRIVER_PENDINGS.profilePhotoMissing) {
            GlobalMethods.pushScreen(
              context: context,
              screen: EditProfileScreen(isFromDahboard: false),
              screenIdentifier: ScreenIdentifier.EditProfileScreen,
            );
          }
        }

        // Set driver status to offline after document submission
        _isOnline.value = 0;
        sharedPref.setInt(IS_ONLINE, 0);

        GlobalMethods.showOnlineOfflineDialog(
          lottieImage: AppLottie.sadEmoji,
          context: context,
          positiveAction: () {
            getPositiveAction(incomplete: state.data.incompleteList[0]);
          },
          title: Column(
            mainAxisSize: MainAxisSize.min,
            children: state.data.incompleteList.map((incomplete) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Text(
                  incomplete.message,
                  style: AppTextStyles.title(),
                  textAlign: TextAlign.center,
                ),
              );
            }).toList(),
          ),
        );
      } else if (state is OnlineState) {
        _isOfflineDialogBoxShown = false;

        // Only set online if explicitly requested through the online button
        if (state.isExplicitOnlineRequest) {
          _isOnline.value = 1;
          GlobalMethods.startsevertrackingTracking();
        } else {
          _isOnline.value = 0;
        }

        HeartBeatService.start(
          sharedPref.getInt(USER_ID).toString(),
          sharedPref.getString(TOKEN) ?? "",
        );
        if (!GlobalState.isOnlineAudioPlayed) {
          GlobalState.isOnlineAudioPlayed = true;
          BackgroundAudio.playOnlineAudio();
        }
      } else if (state is OfflineState) {
        _onOffline();
      } else if (state is NewRideDeclineState) {
        GlobalState.isNewRideShowing = false;
        _onDecline(rideId: state.rideId);
      } else if (state is NewRideAcceptState) {
        RideScreenUtils.isAlreadyInArrived = false;
        RideScreenUtils.isAlreadyInReached = false;
        _onAccept();
      } else if (state is NewRideAcceptedByOtherDriverState) {
        GlobalState.isNewRideShowing = false;
        _onNewRideAlreadyAcceptedByOtherDriver(message: state.message);
      } else if (state is QueuedRideDeclineState) {
        GlobalState.isNewQueuedRideShowing = false;
        _onQueuedRideDecline(rideId: state.rideId);
      } else if (state is QueuedRideAcceptState) {
        GlobalState.isNewQueuedRideShowing = false;
        _onQueuedRideAccept();
      } else if (state is QueuedRideErrorState) {
        GlobalState.isNewQueuedRideShowing = false;
        GlobalMethods.errorToast(context, state.message);
        // Keep the queued ride overlay open on error
      } else if (state is NewRideErrorState) {
        GlobalState.isNewRideShowing = false;
        _newride.value = OnRideRequest();
        _queuedRide.value = OnRideRequest();
        GlobalState.pausableTimer.cancel();
        GlobalMethods.errorToast(context, state.message);
        _getCurrentRide();

        // void timerCallback() {
        //   GlobalState.new_ride_countdown_timer_value.value =
        //       GlobalState.pausableTimer.remainingTime;

        //   if (GlobalState.new_ride_countdown_timer_value.value < 2) {
        //     GlobalState.pausableTimer.cancel();
        //     _onTimeOut();
        //   }
        // }

        // GlobalState.pausableTimer.start(timerCallback);
      } else if (state is RideFlowErrorState) {
        GlobalMethods.errorToast(context, state.message);
      } else if (state is CurrentRideLoadedState) {
        GlobalState.isNewRideShowing = false;
        GlobalState.isNewQueuedRideShowing = false;
        // _isItNewRide = false;
        await GlobalMethods.saveRideIdLocally(state.rideModel);

        if (state.rideModel.onRideRequest?.status == ARRIVING) {
          await GlobalMethods.removeWaitingTimeData();
        }

        if (state.rideModel.onRideRequest != null) {
          try {
            if (tutorialCoachMark?.isShowing ?? false) {
              tutorialCoachMark?.finish();
            }
          } catch (e) {}
          if (state.rideModel.onRideRequest?.status == COMPLETED) {
            await GlobalMethods.removeWaitingTimeData();
            if (state.rideModel.onRideRequest?.isDriverRated == 0) {
              GlobalMethods.replaceScreen(
                screenIdentifier: ScreenIdentifier.rideScreen,
                context: context,
                screen: NewReviewScreen(
                  isPooling: false,
                  onrideRequest: state.rideModel.onRideRequest!,
                  rideId: state.rideModel.onRideRequest!.id!,
                ),
              );
            }
          }
          if (state.rideModel.onRideRequest?.stops != null) {
            _wayPoints = state.rideModel.onRideRequest!.stops!.map((element) {
              return map_launcher.Waypoint(
                element.stopLat,
                element.stopLng,
                element.title,
              );
            }).toList();
          }
        } else {
          await GlobalMethods.removeWaitingTimeData();
          /* clear all polylines + markers */
          if (_newride.value.id == null) {
            await _polylineAnnotationManager.deleteAll();
            await _clearMapAnnotations();
          }
        }

        GlobalState.current_ride = ValueNotifier(state.rideModel);
        _handleRideStateFunctions(ride: state.rideModel);
        _getChatCount(ride: state.rideModel);
      } else if (state is AdminNotifiedState) {
        _adminNotifyWaitingTimeTimerValue.value = -3;
      } else if (state is RideCancelledStateMqtt) {
        _onRideCancelled(rideId: state.rideId);
      } else if (state is MultipleStopMqtt) {
        BackgroundAudio.playNewRideAudio();
        GlobalMethods.showInfoDialogNew(
          context: context,
          onClick: () {
            BackgroundAudio.stopAudio();
            Navigator.of(context).pop();
          },
          title: state.message,
        );
        _getCurrentRide();
      } else if (state is MultipleStopDeletedMqtt) {
        BackgroundAudio.playErrorAudio();
        GlobalMethods.showInfoDialogNew(
          context: context,
          onClick: () {
            Navigator.of(context).pop();
          },
          title: state.message,
        );
        _getCurrentRide();
      } else if (state is DriverCancelledStateMqtt) {
        _getCurrentRide();
      } else if (state is InitialRideLoadedMqtt) {
        _getCurrentRide();
      } else if (state is NewRideStateMqtt) {
        _onNewRide(onRideRequest: state.onRideRequest);
      } else if (state is QueuedRideStateMqtt) {
        _onQueuedRide(onRideRequest: state.onRideRequest);
      } else if (state is PickUpChangedStateMqtt) {
        GlobalMethods.showInfoDialogNew(
          context: context,
          onClick: () {
            Navigator.of(context).pop();
          },
          title: "Rider changed the pickup location",
        );
        _getCurrentRide();
      } else if (state is DestinationChangedStateMqtt) {
        _ondestinationChangedInsideModel(
          data: state.onRideRequest.destinationPlace!.last,
          length: state.onRideRequest.destinationPlace!.length,
        );
        _getCurrentRide();
      } else if (state is NewRiderLocationStateMqtt) {
        _createRiderCurrentLocationPointAnnotation(state.location);
      } else if (state is OfflineStateMqtt) {
        if (_isOnline.value == 1) {
          if (_isOfflineDialogBoxShown == false) {
            _isOfflineDialogBoxShown = true;

            GlobalMethods.showInfoDialog(
              onShowedDialog: () {
                _isOfflineDialogBoxShown = true;
              },
              context: context,
              positiveAction: () {},
              title: state.messsage,
            );

            _onOffline();
          }
        }
      } else if (state is MqttErrorState) {
        GlobalMethods.errorToast(
          context,
          "Server error: Please wait sometime and reload the app",
        );
      } else if (state is DeviceLocationUpdatedState) {}
    });
  }

  init() async {
    _startRideListner();
    _onNoride();
  }

  // Load fine area data and store in local variable
  _loadFineAreaData() async {
    try {
      FineAreaResponse response = await getFineAreaRoutes();
      if (response.status && response.data != null) {
        setState(() {
          _fineAreas = response.data!;
        });
        print("Fine areas loaded: ${_fineAreas.length} areas");
      } else {
        print("Failed to load fine areas: ${response.message}");
      }
    } catch (e) {
      print("Error loading fine areas: $e");
    }
  }

  // Check if driver is within fine area proximity when stationary
  _checkFineAreaProximity() {
    if (_fineAreas.isEmpty || GlobalState.latestLocation == null) return;

    DateTime now = DateTime.now();

    if (_currentSpeed > CONSIDER_STOPPED_THRESHOLD) {
      _lastMovementTime = now;
      _isDriverStationary = false;
      return;
    }

    // Initialize last movement time if not set
    if (_lastMovementTime == null) {
      _lastMovementTime = now;
      return;
    }

    // Check if driver has been stationary for the threshold duration
    Duration stationaryDuration = now.difference(_lastMovementTime!);
    if (stationaryDuration >= STATIONARY_THRESHOLD) {
      _isDriverStationary = true;

      // Check if driver is within any fine area
      FineArea? nearbyFineArea = FineAreaUtils.getFineAreaWithinRange(
        _fineAreas,
        GlobalState.latestLocation!.latitude!,
        GlobalState.latestLocation!.longitude!,
      );

      if (nearbyFineArea != null &&
          nearbyFineArea.id != _lastShownFineArea?.id) {
        _lastShownFineArea = nearbyFineArea;
        _showFineAreaDialog(nearbyFineArea);
      }
    }
  }

  // Show fine area dialog to the driver
  _showFineAreaDialog(FineArea fineArea) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.orange, size: 24),
              SizedBox(width: 8),
              Text(
                "Fine Area Alert",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "You are currently in a fine area. Please do not park/stop your vehicle here.",
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              SizedBox(height: 8),
              Text(
                fineArea.title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              SizedBox(height: 12),
              Text(
                "Please be aware of local regulations and drive carefully in this area.",
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text("OK", style: TextStyle(fontSize: 16)),
            ),
          ],
        );
      },
    );
  }

  Future<void> _initApp() async {
    _areAllPermissionsAllowed = await _checkRequiredPermissions();
    setState(() {
      _checkingMapLocationPermission = false;
    });
    if (_areAllPermissionsAllowed == true) {
      _onLocationPermissionGranted();
      if (widget.rideModel != null) {
        GlobalMethods.startsevertrackingTracking();
        HeartBeatService.stop();
        _getChatCount(ride: widget.rideModel!);
        _handleRideStateFunctions(ride: widget.rideModel!);
      }
    }
  }

  Future<mapbox.PointAnnotation> _createMarkerPoint({
    required mapbox.Position location,
    required String icon,
    required double iconSize,
  }) async {
    final ByteData bytes = await rootBundle.load(icon);
    final Uint8List list = bytes.buffer.asUint8List();

    return await _pointAnnotationManager.create(
      mapbox.PointAnnotationOptions(
        image: list,
        iconSize: iconSize,
        geometry: mapbox.Point(coordinates: location),
      ),
    );
  }

  Future<mapbox.PointAnnotation> _setDestinationMarker({
    required MarkerId markerId,
    required LatLng endLocation,
    required String endIcon,
    required String endAddress,
    double iconSize = .3,
  }) async {
    mapbox.PointAnnotation annotation = await _createMarkerPoint(
      iconSize: iconSize,
      location: mapbox.Position(endLocation.longitude, endLocation.latitude),
      icon: endIcon,
    );

    _mapAnnotations.add(annotation);
    return annotation;
  }

  Future _setPickUpAndDestinationMarker({
    required mapbox.Position startLocation,
    required String startIcon,
    required String startAdress,
    required mapbox.Position endLocation,
    required String endIcon,
    required String endAddress,
    required String id,
  }) async {
    // await _clearMapAnnotations();
    _mapAnnotations.add(
      await _createMarkerPoint(
        iconSize: .25,
        location: startLocation,
        icon: startIcon,
      ),
    );
    _mapAnnotations.add(
      await _createMarkerPoint(
        iconSize: .1,
        location: endLocation,
        icon: endIcon,
      ),
    );
  }

  Future<void> _markReached({
    required LatLng destinationLocation,
    required LatLng currentLocation,
    required OnRideRequest onrideRequest,
    bool isAutomatic = false,
  }) async {
    String end_location = await RideRepository().get_user_address();

    Map<String, dynamic> request = {
      "id": onrideRequest.id,
      "status": REACHED,
      "service_id": onrideRequest.serviceId!,
      "end_latitude": GlobalState.driverPosition!.latitude,
      "end_longitude": GlobalState.driverPosition!.longitude,
      "end_address": end_location,
      "distance": null,
      "waiting_time_list": GlobalMethods.getWaitingTimeData()
          .map((e) => e.toMap())
          .toList(),
    };
    if (isAutomatic) {
      /* check if already reached */
      if (onrideRequest.status == REACHED) {
        _endRide(onrideRequest: onrideRequest);
        return;
      }
      await BlocProvider.of<RideFlowCubit>(context).changeRideStatus(
        request: request,
        rideId: onrideRequest.id!,
        afterSuccessCallback: () {
          _getCurrentRide();
          // _endRide(onrideRequest: onrideRequest);
        },
      );
      return;
    }
    GlobalMethods.showConfirmationDialog(
      context: context,
      negativeText: "No",
      positiveText: "Yes",
      onPositiveAction: () async {
        /* check if already reached */
        if (onrideRequest.status == REACHED) {
          _endRide(onrideRequest: onrideRequest);
          return;
        }
        await BlocProvider.of<RideFlowCubit>(context).changeRideStatus(
          request: request,
          rideId: onrideRequest.id!,
          afterSuccessCallback: () {
            _endRide(onrideRequest: onrideRequest);
          },
        );
      },
      title: "Have you dropped off the rider?",
    );
  }

  _markInprogress({
    required LatLng pickupLocation,
    required LatLng currentLocation,
    required int rideId,
    required DateTime arrived_time,
  }) {
    GlobalMethods.showConfirmationDialog(
      context: context,
      onPositiveAction: () async {
        bool result = true;
        if (result) {
          GlobalMethods.saveEndWaitingTimeData(
            data: LocalWaitingTimeModel(
              startTime: DateTime.now(),
              endTime: DateTime.now(),
              type: LocalWaitingTimeTyes.arrived,
            ),
          );

          _arrivedStateWaitingTimer?.cancel();
          _arrivedStateWaitingTimerValue = ValueNotifier(-1);
          DateTime now = DateTime.now();
          int arrived_waiting_time =
              now.difference(arrived_time).inSeconds -
              GlobalState.adminNotifyLimit;
          Map<String, dynamic> request = {
            "id": rideId,
            "status": IN_PROGRESS,
            "inprogress_time": now.toString(),
            if (arrived_waiting_time > 0)
              "arrived_waiting_time": arrived_waiting_time,
          };
          BlocProvider.of<RideFlowCubit>(
            context,
          ).changeRideStatus(request: request, rideId: rideId);
        } else {
          GlobalMethods.errorToast(
            context,
            "You are not at the specified location",
          );
        }
      },
      title:
          "Please ensure you are at the specified location. This operation will only proceed if you are there.",
    );
  }

  Future<void> _declineNewRide({
    required int? reasonId,
    required int newRideId,
  }) async {
    BackgroundAudio.stopAudio();
    Map request = {
      "id": newRideId,
      "is_accept": "0",
      "is_flag": false,
      "driver_id": sharedPref.getInt(USER_ID),
      "reason": reasonId,
    };
    GlobalState.pausableTimer.pause();
    BlocProvider.of<RideFlowCubit>(context).accepDeclineNewRideREquest(
      request: request,
      is_decline: true,
      rideId: newRideId,
    );
    _showCancelRason.value = false;
  }

  _acceptNewRide({required int newRideId}) {
    BackgroundAudio.stopAudio();
    Map request = {
      "id": newRideId,
      "is_accept": "1",
      "driver_id": sharedPref.getInt(USER_ID),
    };
    GlobalState.pausableTimer.pause();

    BlocProvider.of<RideFlowCubit>(context).accepDeclineNewRideREquest(
      rideId: newRideId,
      request: request,
      is_decline: false,
    );
    _showCancelRason.value = false;
  }

  _declineQueuedRide({required int reasonId, required int queuedRideId}) {
    BackgroundAudio.stopAudio();
    Map request = {
      "id": queuedRideId,
      "is_accept": "0",
      "is_flag": false,
      "driver_id": sharedPref.getInt(USER_ID),
      "reason": reasonId,
    };
    GlobalState.pausableTimer.pause();
    BlocProvider.of<RideFlowCubit>(context).acceptDeclineQueuedRideRequest(
      request: request,
      is_decline: true,
      rideId: queuedRideId,
    );
    _showCancelRason.value = false;
  }

  _acceptQueuedRide({required int queuedRideId}) {
    BackgroundAudio.stopAudio();
    Map request = {
      "id": queuedRideId,
      "is_accept": "1",
      "driver_id": sharedPref.getInt(USER_ID),
    };
    GlobalState.pausableTimer.pause();

    BlocProvider.of<RideFlowCubit>(context).acceptDeclineQueuedRideRequest(
      rideId: queuedRideId,
      request: request,
      is_decline: false,
    );
    _showCancelRason.value = false;
  }

  _endRide({required OnRideRequest onrideRequest}) {
    BlocProvider.of<RideFlowCubit>(context).complete_ride(
      ride_id: onrideRequest.id!,
      service_id: onrideRequest.serviceId!,
      total_distance: 10,
      waiting_time_list: [],
    );
  }

  _markArrived({
    required int rideId,
    required LatLng pickupLocation,
    required LatLng currentLocation,
    bool isAutomatic = false,
  }) {
    if (isAutomatic) {
      Map<String, dynamic> request = {
        "id": rideId,
        "status": ARRIVED,
        "arrived_time": DateTime.now().toString(),
      };
      BlocProvider.of<RideFlowCubit>(
        context,
      ).changeRideStatus(request: request, rideId: rideId);
      return;
    }

    GlobalMethods.showConfirmationDialog(
      context: context,
      onPositiveAction: () async {
        bool result = await GlobalMethods.isCurrentLocationInDesiredArea(
          currentLocation: currentLocation,
          targetLocation: pickupLocation,
        );
        if (result) {
          Map<String, dynamic> request = {
            "id": rideId,
            "status": ARRIVED,
            "arrived_time": DateTime.now().toString(),
          };
          BlocProvider.of<RideFlowCubit>(
            context,
          ).changeRideStatus(request: request, rideId: rideId);
        } else {
          GlobalMethods.errorToast(
            context,
            "You are not at the specified location",
          );
        }
      },
      title:
          "Please ensure you are at the specified location. This operation will only proceed if you are there.",
    );
  }

  _getHomePageData() {
    BlocProvider.of<RideFlowCubit>(context).getHomePageData();
  }

  Future<bool> _startDeviceTracking() async {
    bool result = true;

    await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    ).then((value) {
      GlobalState.driverPosition = LatLng(value.latitude, value.longitude);
    });
    await BlocProvider.of<RideFlowCubit>(context).get_device_current_location();
    await BlocProvider.of<RideFlowCubit>(context).startDeviceTracking();
    return result;
  }

  _onNoride() {
    GlobalState.homePageDataRefresher = () {
      _getHomePageData();
    };

    _getAppSetting();

    _getHomePageData();
  }

  _onPoolRide({required List<OnRideRequest> poolRide}) async {
    _poolRides.value = poolRide;

    _arrived_otp_verified.value = poolRide
        .map((element) => element.id!)
        .toSet();

    Color getcolor({required int index}) {
      if (index == 0) {
        return Colors.black;
      } else if (index == 1) {
        return Colors.blue;
      }
      return Colors.orange;
    }

    await _polylineAnnotationManager.deleteAll();

    await _clearMapAnnotations();
    for (var i = 0; i < poolRide.length; i++) {
      List<mapbox.Position>? pool_coordinated = await _setPolyLines(
        id: poolRide[i].id!,
        initialLat: double.parse(poolRide[i].startLatitude!),
        initialLang: double.parse(poolRide[i].startLongitude!),
        finalLat: double.parse(poolRide[i].endLatitude!),
        finalLang: double.parse(poolRide[i].endLongitude!),
        savedPolylineCoordinates:
            _savedPoolPolylinePoint.length != poolRide.length
            ? null
            : _savedPoolPolylinePoint[i],
        colors: getcolor(index: i),
      );

      await _setPickUpAndDestinationMarker(
        id: poolRide[i].id.toString(),
        startLocation: mapbox.Position(
          double.parse(poolRide[i].startLongitude!),
          double.parse(poolRide[i].startLatitude!),
        ),
        startIcon: riderIcon,
        startAdress: poolRide[i].startAddress!,
        endLocation: mapbox.Position(
          double.parse(poolRide[i].endLongitude!),
          double.parse(poolRide[i].endLatitude!),
        ),
        endIcon: DestinationIcon,
        endAddress: poolRide[i].endAddress!,
      );

      if (pool_coordinated.isNotEmpty) {
        _savedPoolPolylinePoint.add(pool_coordinated);
      }
    }
  }

  _onNewRide({
    required OnRideRequest onRideRequest,
    bool playAudio = true,
    int remainingSeconds = -1,
  }) async {
    /* close all other top content */
    final BuildContext newContext = context;
    final NavigatorState navigator = Navigator.of(newContext);
    while (ModalRoute.of(context)?.isCurrent != true && navigator.canPop()) {
      navigator.pop();
      await Future.delayed(Duration(milliseconds: 100));
    }

    if (_scaffoldKey.currentState?.isDrawerOpen ?? false) {
      _scaffoldKey.currentState?.closeDrawer();
      await Future.delayed(Duration(milliseconds: 300));
    }

    startNewRideCountdown({required int newRideId}) {
      GlobalState.pausableTimer.cancel();

      if (playAudio) {
        BackgroundAudio.playNewRideAudio();
      }
      GlobalState.pausableTimer = PausableTimer(GlobalState.new_ride_countdown);

      if (remainingSeconds != -1) {
        GlobalState.pausableTimer = PausableTimer(remainingSeconds);
      } else {
        GlobalState.new_ride_countdown_timer_value.value =
            GlobalState.new_ride_countdown;
      }

      void timerCallback() {
        GlobalState.new_ride_countdown_timer_value.value =
            GlobalState.pausableTimer.remainingTime;

        if (GlobalState.new_ride_countdown_timer_value.value < 2) {
          GlobalState.pausableTimer.cancel();
          _onTimeOut();
        }
      }

      GlobalState.pausableTimer.start(timerCallback);
    }

    startNewRideCountdown(newRideId: onRideRequest.id!);

    _newride.value = onRideRequest;
    await _drawSmartPolylines(onRideRequest: onRideRequest);
  }

  _onQueuedRide({
    required OnRideRequest onRideRequest,
    bool playAudio = true,
    int remainingSeconds = -1,
  }) async {
    startQueuedRideCountdown({required int queuedRideId}) {
      GlobalState.pausableTimer.cancel();

      if (playAudio) {
        BackgroundAudio.playNewRideAudio();
      }
      GlobalState.pausableTimer = PausableTimer(GlobalState.new_ride_countdown);
      if (remainingSeconds == -1) {
        GlobalState.new_ride_countdown_timer_value.value =
            GlobalState.new_ride_countdown;
      } else {
        GlobalState.new_ride_countdown_timer_value.value = remainingSeconds;
      }

      void timerCallback() {
        GlobalState.new_ride_countdown_timer_value.value =
            GlobalState.pausableTimer.remainingTime;

        if (GlobalState.new_ride_countdown_timer_value.value < 2) {
          GlobalState.pausableTimer.cancel();
          _onQueuedRideTimeOut();
        }
      }

      GlobalState.pausableTimer.start(timerCallback);
    }

    startQueuedRideCountdown(queuedRideId: onRideRequest.id!);
    _queuedRide.value = onRideRequest;
  }

  _onArrivingState({required RideModel ride}) async {
    RideScreenUtils.isAlreadyInArrived = false;
    RideScreenUtils.isAlreadyInReached = false;
    _ride.value = ride;
    await _drawSmartPolylines(onRideRequest: ride.onRideRequest!);
  }

  _onArrivedState({required RideModel ride}) async {
    RideScreenUtils.isAlreadyInArrived = false;
    RideScreenUtils.isAlreadyInReached = false;
    // GlobalMethods.succesToast(context, "At Pickup Location");
    _ride.value = ride;
    if (_adminNotifyWaitingTimer == null ||
        _adminNotifyWaitingTimer!.isActive == false) {
      _checkAdminNotifyWaitingTime(onrideRequest: ride.onRideRequest!);
    }
    await _drawSmartPolylines(onRideRequest: ride.onRideRequest!);
  }

  _onOffline() {
    GlobalMethods.stopServerTracking();
    _isOfflineDialogBoxShown = false;
    HeartBeatService.stop();
    _isOnline.value = 0;

    if (!_isOfflineAudioPlayed) {
      _isOfflineAudioPlayed = true;
      // BackgroundAudio.playOfflineAudio();
    }

    _panelController.animatePanelToPosition(
      0,
      duration: Duration(milliseconds: 200),
      curve: Curves.linear,
    );
    _scrollController.animateTo(
      0,
      duration: Duration(milliseconds: 100),
      curve: Curves.linear,
    );
  }

  _onDecline({required int rideId}) async {
    // BackgroundAudio.playOfflineAudio();

    if (GlobalState.current_ride?.value.onRideRequest?.id != null ||
        GlobalState.current_ride?.value.rideRequest?.id != null) {
      return;
    }
    if (_poolRides.value.isNotEmpty) {
      _onPoolRide(poolRide: _poolRides.value);
    } else {
      await _polylineAnnotationManager.deleteAll();
      await _clearMapAnnotations();
    }

    _newride.value = OnRideRequest();
    _poolRides.notifyListeners();
    GlobalMethods.succesToast(context, "Ride declined");

    // GlobalMethods.showInfoDialog(
    //   context: context,
    //   positiveAction: () {},
    //   title: language.RidedeclinedText,
    // );
  }

  _onNewRideAlreadyAcceptedByOtherDriver({required String message}) async {
    if (GlobalState.current_ride?.value.onRideRequest?.id != null ||
        GlobalState.current_ride?.value.rideRequest?.id != null) {
      return;
    }
    if (_poolRides.value.isNotEmpty) {
      _onPoolRide(poolRide: _poolRides.value);
    } else {
      await _polylineAnnotationManager.deleteAll();
      await _clearMapAnnotations();
    }

    _newride.value = OnRideRequest();
    _poolRides.notifyListeners();

    GlobalMethods.showInfoDialog(
      context: context,
      positiveAction: () {},
      title: message,
    );
  }

  _onloadHomePageData({required HomePageDataModel data}) async {
    _homePageData = data;

    await sharedPref.setInt(GLOBAL_REGION_ID, data.region_id ?? 0);

    await sharedPref.setInt(GLOBAL_REGION_ID, data.region_id ?? 0);
    global_region_id = data.region_id ?? 0;
    delete_account_instruction_text =
        data.account_delete_instructions_for_driver ?? "";
    about_us_instruction_text = data.about_us_instruction_driver ?? "";
    GlobalState.new_ride_countdown =
        data.ride_accept_decline_duration_for_driver_in_second ?? 30;
    _driver_rating = data.rating ?? 0.0;
    GlobalState.todaysEarningValue.value = data.today_earning ?? 0.0;

    getAndApplyCounters();
    setState(() {
      _homePageDataLoaded = true;
    });
    // if (_homePageData?.dashboardAd != null) {
    //   _showAdvertisment(advertisement: _homePageData!.dashboardAd!);
    // }
    // if (_is_profile_completed.value && data.region_id == null) {
    //   _handleMissingRegion();
    // }

    _showTutorial();
  }

  _uploadSelfie() {
    GlobalMethods.showInfoDialog(
      context: context,
      barrierDismissible: false,
      autoHandleClose: false,
      positiveAction: () async {
        GlobalMethods.pushScreen(
          context: context,
          screen: SelfieScreen(),
          screenIdentifier: ScreenIdentifier.SelfieScreen,
        );
      },
      title: "For further rides, please upload one selfie ",
    );
  }

  _onTimeOut() async {
    GlobalState.isNewRideShowing = false;
    BackgroundAudio.stopAudio();
    // _isItNewRide = false;
    if (_poolRides.value.isNotEmpty) {
      _onPoolRide(poolRide: _poolRides.value);
    } else {
      await _polylineAnnotationManager.deleteAll();
      await _clearMapAnnotations();
    }
    GlobalMethods.infoToast(context, "You missed a ride request");

    _newride.value = OnRideRequest();
    _poolRides.notifyListeners();
  }

  _onQueuedRideTimeOut() async {
    GlobalState.isNewQueuedRideShowing = false;
    BackgroundAudio.stopAudio();
    GlobalMethods.infoToast(context, "You missed a queued ride request");
    _queuedRide.value = OnRideRequest();
  }

  _onDriverCancelled({required RideModel ride}) {
    GlobalMethods.infoToast(
      context,
      language.youHaveCanceledTheRidePleaseWaitForAdminApproval,
    );
    _ride.value = ride;
  }

  String getStopOrder({required int index}) {
    if (index == 0) {
      return FirstStop;
    } else if (index == 1) {
      return SecondStop;
    } else if (index == 2) {
      return ThirdStop;
    }
    return "";
  }

  Future _addStopMarker({required OnRideRequest onrideRequest}) async {
    for (var i = 0; i < onrideRequest.stops!.length; i++) {
      await _setDestinationMarker(
        markerId: MarkerId(i.toString()),
        endLocation: LatLng(
          onrideRequest.stops![i].stopLat,
          onrideRequest.stops![i].stopLng,
        ),
        endIcon: getStopOrder(index: i),
        endAddress: onrideRequest.stops![i].title,
      );
    }
  }

  Future<void> _createRiderCurrentLocationPointAnnotation(
    AppLocationType riderLocation,
  ) async {
    /* just draw the marker */
    if (_riderCurrentLocationPointAnnotation == null) {
      _riderCurrentLocationPointAnnotation = await _setDestinationMarker(
        markerId: MarkerId("rider_current_location"),
        endLocation: LatLng(riderLocation.latitude, riderLocation.longitude),
        endIcon: blueDotIcon,
        iconSize: 0.7,
        endAddress: "Rider current location",
      );
    } else {
      _riderCurrentLocationPointAnnotation!.geometry.coordinates =
          mapbox.Position(riderLocation.longitude, riderLocation.latitude);
    }
  }

  /// Draws smart polylines for normal rides with stops
  /// - Primary polyline: source to next unvisited stop (or destination if no stops)
  /// - Secondary polyline: source to final destination (lighter shade)
  Future _drawSmartPolylines({
    required OnRideRequest onRideRequest,
    isReRouteCreation = false,
  }) async {
    await _polylineAnnotationManager.deleteAll();
    await _clearMapAnnotations();

    List<mapbox.Position> wayPoints = [];

    mapbox.Position sourcePosition = mapbox.Position(
      double.parse(onRideRequest.startLongitude!),
      double.parse(onRideRequest.startLatitude!),
    );

    if (isReRouteCreation ||
        onRideRequest.status == ARRIVING ||
        onRideRequest.status == ARRIVED) {
      if (isReRouteCreation) {
        wayPoints = [
          mapbox.Position(
            GlobalState.latestLocation!.longitude!,
            GlobalState.latestLocation!.latitude!,
          ),
        ];
      } else {
        sourcePosition = mapbox.Position(
          GlobalState.latestLocation!.longitude!,
          GlobalState.latestLocation!.latitude!,
        );
      }
    }

    mapbox.Position destinationPosition = mapbox.Position(
      double.parse(onRideRequest.endLongitude!),
      double.parse(onRideRequest.endLatitude!),
    );
    if (onRideRequest.status == ARRIVING || onRideRequest.status == ARRIVED) {
      destinationPosition = mapbox.Position(
        double.parse(onRideRequest.startLongitude!),
        double.parse(onRideRequest.startLatitude!),
      );
    }

    // If no stops, draw single polyline to destination
    if (onRideRequest.stops == null || onRideRequest.stops!.isEmpty) {
      await createPolyline(
        waypoints: wayPoints,
        startLocation: sourcePosition,
        endLocation: destinationPosition,
        colors: 0xFFFF9800, // Colors.orangeAccent
        lineWidth: 16,
        lineOpacity: 1.0,
      );

      if (onRideRequest.status == ARRIVING || onRideRequest.status == ARRIVED) {
        await _setDestinationMarker(
          markerId: MarkerId("pickup"),
          endLocation: LatLng(
            double.parse(onRideRequest.startLatitude.toString()),
            double.parse(onRideRequest.startLongitude.toString()),
          ),
          endIcon: riderIcon,
          iconSize: .27,
          endAddress: onRideRequest.startAddress.toString(),
        );
      } else {
        // Set pickup and destination markers
        await _setPickUpAndDestinationMarker(
          id: onRideRequest.id.toString(),
          startLocation: sourcePosition,
          startIcon: riderIcon,
          startAdress: onRideRequest.startAddress!,
          endLocation: destinationPosition,
          endIcon: DestinationIcon,
          endAddress: onRideRequest.endAddress!,
        );
      }
      return;
    }

    // Find the next unvisited stop
    StopsModel? nextStop;
    for (var i = 0; i < onRideRequest.stops!.length; i++) {
      await _setDestinationMarker(
        markerId: MarkerId(i.toString()),
        endLocation: LatLng(
          onRideRequest.stops![i].stopLat,
          onRideRequest.stops![i].stopLng,
        ),
        endIcon: getStopOrder(index: i),
        endAddress: onRideRequest.stops![i].title,
      );

      if (nextStop == null && onRideRequest.stops![i].isArrived != true) {
        nextStop = onRideRequest.stops![i];
        // break;
      }
    }

    // Determine target for primary polyline
    mapbox.Position primaryTarget;
    if (nextStop != null) {
      // Draw to next unvisited stop
      primaryTarget = mapbox.Position(nextStop.stopLng, nextStop.stopLat);
    } else {
      // All stops visited, draw to final destination
      primaryTarget = destinationPosition;
    }

    // Draw primary polyline (source to next target)
    await createPolyline(
      startLocation: sourcePosition,
      endLocation: primaryTarget,
      colors: 0xFFFF9800, // Colors.orangeAccent
      lineWidth: 16,
      lineOpacity: 1.0,
    );

    // Draw secondary polyline (source to final destination) only if we have unvisited stops
    if (nextStop != null) {
      // Create lighter shade of the primary color (orangeAccent with 30% opacity)
      await createPolyline(
        startLocation: sourcePosition,
        endLocation: destinationPosition,
        waypoints: onRideRequest.stops!.map((element) {
          return mapbox.Position(element.stopLng, element.stopLat);
        }).toList(),
        colors: 0xFFFF9800,
        // lineWidth: 12,
        lineOpacity: 0.3,
      );
    }

    // Set pickup and destination markers
    await _setPickUpAndDestinationMarker(
      id: onRideRequest.id.toString(),
      startLocation: sourcePosition,
      startIcon: riderIcon,
      startAdress: onRideRequest.startAddress!,
      endLocation: destinationPosition,
      endIcon: DestinationIcon,
      endAddress: onRideRequest.endAddress!,
    );
  }

  _onRideCancelled({required int rideId}) async {
    BackgroundAudio.stopAudio();
    _poolRides.notifyListeners();
    if (_newride.value.id != null || _ride.value.onRideRequest?.id != null) {
      GlobalState.pausableTimer.cancel();

      _adminNotifyWaitingTimer?.cancel();
      _arrivedStateWaitingTimer?.cancel();
      _arrivedStateWaitingTimerValue.value = -1;
      _stop_waiting_time_timer?.cancel();
      _stop_waiting_time_timer_value.value = -1;
      _adminNotifyWaitingTimeTimerValue.value = -1;
      await _polylineAnnotationManager.deleteAll();
      await _clearMapAnnotations();
      _ride.value = RideModel();
      _newride.value = OnRideRequest();
      GlobalMethods.infoToast(context, "The ride has been canceled");
      _getCurrentRide();
      // GlobalMethods.showInfoDialogNew(
      //   context: context,
      //   barrierDismissible: false,
      //   onClick: () {
      //     closeScreen(context);
      //     GlobalMethods.pushAndRemoveAll(
      //       context: context,
      //       screen: RideScreen(),
      //       screenIdentifier: ScreenIdentifier.InitialScreen,
      //     );
      //   },
      //   title: "The ride has been canceled",
      // );
    } else if (_queuedRide.value.id != null) {
      _queuedRide.value = OnRideRequest();
      GlobalMethods.infoToast(context, "The queued ride has been canceled");
    }
  }

  _onAccept() async {
    BackgroundAudio.stopAudio();
    if (_poolRides.value.isNotEmpty) {
      _onPoolRide(poolRide: _poolRides.value);
    } else {
      await _polylineAnnotationManager.deleteAll();
      await _clearMapAnnotations();
    }
    GlobalMethods.succesToast(context, language.RideAcceptedTxt);
    _newride.value = OnRideRequest();

    _getCurrentRide();
  }

  _onQueuedRideDecline({required int rideId}) async {
    BackgroundAudio.stopAudio();
    GlobalState.pausableTimer.pause();

    _queuedRide.value = OnRideRequest();
    GlobalMethods.succesToast(context, "Queued ride declined");

    // GlobalMethods.showInfoDialog(
    //   context: context,
    //   positiveAction: () {},
    //   title: "Queued ride declined",
    // );
  }

  _onQueuedRideAccept() async {
    BackgroundAudio.stopAudio();
    GlobalMethods.succesToast(
      context,
      "Queued ride accepted! It will start after your current ride.",
    );
    GlobalState.pausableTimer.pause();
    _queuedRide.value = OnRideRequest();
  }

  _onInProgressState({required RideModel ride}) async {
    RideScreenUtils.isAlreadyInArrived = false;
    RideScreenUtils.isAlreadyInReached = false;
    _ride.value = ride;
    _port.close();
    ReceivePort port = ReceivePort();
    _port = port;

    ui.IsolateNameServer.removePortNameMapping('back');
    await _drawSmartPolylines(onRideRequest: ride.onRideRequest!);
    // if (ride.onRideRequest!.stops?.isNotEmpty ?? false) {
    // } else {
    //   await _setPickUpAndDestinationMarker(
    //       id: ride.onRideRequest!.id.toString(),
    //       startLocation: mapbox.Position(
    //           double.parse(ride.onRideRequest!.startLongitude!),
    //           double.parse(ride.onRideRequest!.startLatitude!)),
    //       startIcon: riderIcon,
    //       startAdress: ride.onRideRequest!.startAddress!,
    //       endLocation: mapbox.Position(
    //           double.parse(ride.onRideRequest!.endLongitude!),
    //           double.parse(ride.onRideRequest!.endLatitude!)),
    //       endIcon: DestinationIcon,
    //       endAddress: ride.onRideRequest!.endAddress!);
    //   await _setPolyLines(
    //     id: ride.onRideRequest!.id!,
    //     initialLat: double.parse(ride.onRideRequest!.startLatitude!),
    //     initialLang: double.parse(ride.onRideRequest!.startLongitude!),
    //     finalLat: double.parse(ride.onRideRequest!.endLatitude!),
    //     finalLang: double.parse(ride.onRideRequest!.endLongitude!),
    //     colors: AppColors.primaryMustardColr,
    //   );
    // }

    if (ride.onRideRequest!.stop_pending != null) {
      StopsModel _currentStop = ride.onRideRequest!.stop_pending!;

      if (_currentStop.status == "arrived") {
        if (_currentStop.timerStart != null) {
          Duration difference = DateTime.now().difference(
            _currentStop.timerStart!,
          );
          _stop_waiting_time_timer_value.value = difference.inSeconds;
          _stop_waiting_time_timer?.cancel();
          _stop_waiting_time_timer = null;
          _stop_waiting_time_timer = Timer.periodic(Duration(seconds: 1), (
            timer,
          ) {
            _stop_waiting_time_timer_value.value =
                _stop_waiting_time_timer_value.value + 1;
          });

          _arrivedStateWaitingTimerValue.value = -1;
          _arrivedStateWaitingTimer?.cancel();

          _stop_waiting_time_timer_value.notifyListeners();
        } else {
          _stop_waiting_time_timer?.cancel();
          _stop_waiting_time_timer_value.value = 0;
        }
      } else {
        _stop_waiting_time_timer?.cancel();
        _stop_waiting_time_timer_value.value = 0;
      }
    }

    // GlobalMethods.succesToast(context, language.inProgress);
  }

  _onReachedState({required RideModel ride}) async {
    sharedPref.reload();
    _port.close();
    _arrivedStateWaitingTimer?.cancel();
    _ride.value = ride;
    await _drawSmartPolylines(onRideRequest: ride.onRideRequest!);
  }

  _onPoolRideCompleted({required OnRideRequest onRideRequest}) async {
    bool reviewDone = await GlobalMethods.pushScreen(
      screenIdentifier: ScreenIdentifier.reviewScreen,
      context: context,
      screen: NewReviewScreen(
        onrideRequest: onRideRequest,
        rideId: onRideRequest.id!,
        isPooling: true,
      ),
    );
    if (reviewDone) {
      _getCurrentRide();
    }
  }

  _onCompleted({required RideModel ride}) async {
    _ride.value = ride;
    sharedPref.reload();
    await sharedPref.remove("serverLocations");
    await GlobalMethods.deletRideIdLocally(
      _ride.value.onRideRequest!.id.toString(),
    );
    await sharedPref.remove("isWaitingTimeStarted");
    await sharedPref.remove("lastLocation");
    await sharedPref.remove("newLocation");

    GlobalMethods.replaceScreen(
      screenIdentifier: ScreenIdentifier.reviewScreen,
      context: context,
      screen: NewReviewScreen(
        onrideRequest: ride.onRideRequest!,
        rideId: ride.onRideRequest!.id!,
        isPooling: false,
      ),
    );
  }

  _driverCancelledWidget({required OnRideRequest onRideRequest}) {
    return Stack(
      children: [
        SlidingUpPanel(
          defaultPanelState: PanelState.OPEN,
          color: AppColors.whiteColor(context),
          panelBuilder: (sc) {
            return SingleChildScrollView(
              controller: sc,
              child: Padding(
                padding: screenPadding,
                child: Column(
                  children: [
                    SlidingUpHandleContainer(),
                    Text(
                      language.youCancelledARide,
                      style: AppTextStyles.header(),
                    ),
                    widgetMiddePart(onRideRequest: onRideRequest),
                    Container(
                      padding: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        borderRadius: radius(),
                        color: Colors.black,
                      ),
                      child: CustomText(
                        data: language
                            .youHaveCanceledTheRidePleaseWaitForAdminApproval,
                        size: 15,
                        textAlign: TextAlign.center,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        BlocBuilder<RideFlowCubit, RideFlowState>(
          builder: (context, state) {
            if (state is RideFlowLoadingState) {
              return LoaderBottomSheet();
            }
            return SizedBox();
          },
        ),
      ],
    );
  }

  _noRideWidget() {
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          BlocBuilder<RideFlowCubit, RideFlowState>(
            builder: (context, state) {
              return ValueListenableBuilder<int>(
                valueListenable: _isOnline,
                builder: (context, value, child) {
                  if (value == 0 || value == -1) {
                    return OnlineButton(
                      tutorial_key: _onlineKeyButton,
                      blocState: state,
                      onPress: () async {
                        GlobalMethods.handleInCompleteProfile(
                          context: context,
                          is_profile_completed: GlobalState.isProfileComplete,
                          positiveAction: () async {
                            var result = await Future.wait([
                              // Permission.notification.status,
                              Permission.locationAlways.status,
                            ]);

                            bool isAllOk = result[0].isGranted;
                            if (isAllOk == true) {
                              GlobalState.isOnlineAudioPlayed = false;
                              _changeOnlineOfflineStatus(makeOnline: 1);
                            } else {
                              Navigator.of(context).push<bool>(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      RequiredPermissionScreen(),
                                ),
                              );
                            }
                          },
                        );
                      },
                    );
                  }
                  return SizedBox();
                },
              );
            },
          ),
          BlocBuilder<RideFlowCubit, RideFlowState>(
            builder: (context, state) {
              return SlidingUpPanel(
                color: AppColors.whiteColor(context),
                controller: _panelController,
                minHeight: _isOnline.value == 1 ? 130 : 85,
                maxHeight: MediaQuery.of(context).size.height * .8,
                panelBuilder: (sc) {
                  _scrollController = sc;
                  return BlocBuilder<RideFlowCubit, RideFlowState>(
                    builder: (context, state) {
                      return SingleChildScrollView(
                        // controller: sc,
                        child: Column(
                          children: [
                            ValueListenableBuilder<int>(
                              valueListenable: _isOnline,
                              builder: (context, value, child) {
                                if (value == 1) {
                                  return OnlineBox(
                                    controller: _panelController,
                                  );
                                } else if (value == 0 || value == -1) {
                                  return OfflineBox(
                                    tutorial_key: _blogKeyButton,
                                    controller: _panelController,
                                  );
                                }
                                return SizedBox();
                              },
                            ),
                            InkWell(
                              onTap: () {
                                GlobalMethods.pushScreen(
                                  context: context,
                                  screen: OnlineOfflineStatusScreen(),
                                  screenIdentifier: ScreenIdentifier
                                      .OnlineOfflineStatusScreen,
                                );
                              },
                              child: Container(
                                width: double.infinity,
                                color: Colors.blue,
                                padding: screenPadding,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Track online/offline time",
                                      style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 12,
                                      ),
                                    ),
                                    Icon(
                                      Icons.arrow_forward_ios_rounded,
                                      size: 12,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            ScheduleRide(
                              scheduleRide: _homePageData?.scheduledRides ?? [],
                            ),
                            Blogs(blogs: _homePageData?.blogs ?? []),
                            ValueListenableBuilder<int>(
                              valueListenable: _isOnline,
                              builder: (context, value, child) {
                                if (value == 1) {
                                  return OfflineButton(
                                    onPress: () {
                                      _isOfflineAudioPlayed = false;
                                      _changeOnlineOfflineStatus(makeOnline: 0);
                                    },
                                    blocState: state,
                                  );
                                }
                                return SizedBox();
                              },
                            ),
                          ],
                        ),
                      );
                    },
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  _queuedRideOverlayWidget({required OnRideRequest queuedRide}) {
    return Container(
      color: Colors.black54,
      child: Center(
        child: Container(
          margin: EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.whiteColor(context),
            borderRadius: appRadius,
          ),
          child: Padding(
            padding: screenPadding,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: AppColors.primaryColor(context),
                    ),
                    width10,
                    Expanded(
                      child: Text(
                        "New Ride Request (While in ride)",
                        style: AppTextStyles.header(),
                      ),
                    ),
                  ],
                ),
                Divider(),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 4),
                        Text(
                          "Accept or decline this queued ride",
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    Spacer(),
                    TimerWidget(
                      value: GlobalState.new_ride_countdown_timer_value,
                    ),
                  ],
                ),
                Divider(),
                /* Rider info , name + rating */
                RideContactAndDetailsWidget(
                  rider: UserData(
                    firstName: queuedRide.riderName,
                    lastName: "",
                    rating: queuedRide.riderRating ?? 0,
                  ),
                  showActions: false,
                  onRideRequest: OnRideRequest(),
                  onRefreshRideDetails: _getCurrentRide,
                  onCancelRide: _cancelRideLogic,
                ),
                Divider(),
                Row(
                  children: [
                    Text("Service Name"),
                    width10,
                    Text(
                      queuedRide.serviceName ?? "",
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                NewRideAmountWidget(onRideRequest: queuedRide),
                Divider(),
                AddressWidget(onRideRequest: queuedRide),
                Divider(),
                height20,
                Row(
                  children: [
                    Expanded(
                      child: AppButton(
                        text: language.decline,
                        onPressed: () async {
                          await _declineNewRide(
                            reasonId: null,
                            newRideId: queuedRide.id!,
                          );
                          _queuedRide.value = OnRideRequest();
                          //  showDialog(
                          //   context: context,
                          //   builder: (context) {
                          //     return AlertDialog(
                          //       title: Text("Select Reason"),
                          //       content: SizedBox(
                          //         height:
                          //             MediaQuery.sizeOf(context).height * 0.6,
                          //         child: Scrollbar(
                          //           thumbVisibility: true,
                          //           child: ListView.separated(
                          //             padding: EdgeInsets.fromLTRB(4, 4, 8, 4),
                          //             shrinkWrap: true,
                          //             itemBuilder: (context, index) {
                          //               return InkWell(
                          //                 onTap: () async {
                          //                   Navigator.of(context).pop();
                          //                   await _declineNewRide(
                          //                     reasonId: queuedRide
                          //                         .cancel_reasons![index]
                          //                         .id,
                          //                     newRideId: queuedRide.id!,
                          //                   );
                          //                   _queuedRide.value = OnRideRequest();
                          //                 },
                          //                 child: Container(
                          //                   decoration: BoxDecoration(
                          //                     borderRadius: appRadius,
                          //                     color: AppColors.primaryColor(
                          //                       context,
                          //                     ),
                          //                   ),
                          //                   padding: screenPadding,
                          //                   width: double.infinity,
                          //                   child: Text(
                          //                     queuedRide
                          //                         .cancel_reasons![index]
                          //                         .name,
                          //                   ),
                          //                 ),
                          //               );
                          //             },
                          //             separatorBuilder: (context, index) =>
                          //                 height10,
                          //             itemCount:
                          //                 queuedRide.cancel_reasons?.length ??
                          //                 0,
                          //           ),
                          //         ),
                          //       ),
                          //     );
                          //   },
                          // );
                        },
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: AppButton(
                        onPressed: () async {
                          _acceptQueuedRide(queuedRideId: queuedRide.id!);
                        },
                        text: language.accept,
                      ),
                    ),
                  ],
                ),
                ValueListenableBuilder<bool>(
                  valueListenable: _showCancelRason,
                  builder: (context, value, child) {
                    if (value) {
                      return Column(
                        children: [
                          height20,
                          Divider(),
                          Text("Reason", style: AppTextStyles.header()),
                          ListView.separated(
                            physics: NeverScrollableScrollPhysics(),
                            padding: screenPadding / 2,
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              return InkWell(
                                onTap: () async {
                                  _declineQueuedRide(
                                    reasonId:
                                        queuedRide.cancel_reasons![index].id,
                                    queuedRideId: queuedRide.id!,
                                  );
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: appRadius,
                                    color: AppColors.primaryColor(context),
                                  ),
                                  padding: screenPadding,
                                  width: double.infinity,
                                  child: Text(
                                    queuedRide.cancel_reasons![index].name,
                                  ),
                                ),
                              );
                            },
                            separatorBuilder: (context, index) => height10,
                            itemCount: queuedRide.cancel_reasons?.length ?? 0,
                          ),
                          height20,
                          height20,
                        ],
                      );
                    }
                    return SizedBox();
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _newRideWidget({required OnRideRequest newRide}) {
    // _isItNewRide = true;
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        SlidingUpPanel(
          defaultPanelState: PanelState.OPEN,
          maxHeight: MediaQuery.sizeOf(context).height - 100,
          color: AppColors.whiteColor(context),
          padding: screenPadding,
          panelBuilder: (sc) {
            return SingleChildScrollView(
              controller: sc,
              child: Column(
                children: [
                  SlidingUpHandleContainer(),
                  Text(language.newRide, style: AppTextStyles.header()),
                  Divider(),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 4),
                          Text(
                            language.pleaseAcceptTheRide,
                            style: AppTextStyles.title(),
                          ),
                        ],
                      ),
                      Spacer(),
                      TimerWidget(
                        value: GlobalState.new_ride_countdown_timer_value,
                      ),
                    ],
                  ),
                  Divider(),
                  /* Rider info , name + rating */
                  RideContactAndDetailsWidget(
                    rider: UserData(
                      firstName: newRide.riderName,
                      lastName: "",
                      rating: newRide.riderRating ?? 0,
                    ),
                    showActions: false,
                    onRideRequest: OnRideRequest(),
                    onRefreshRideDetails: _getCurrentRide,
                    onCancelRide: _cancelRideLogic,
                  ),
                  Divider(),
                  Row(
                    children: [
                      Text("Service Name"),
                      width10,
                      Text(
                        newRide.serviceName ?? "",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  NewRideAmountWidget(onRideRequest: newRide),
                  Divider(),
                  AddressWidget(onRideRequest: newRide),
                  Divider(),
                  height20,
                  Row(
                    children: [
                      Expanded(
                        child: AppButton(
                          text: language.decline,
                          onPressed: () async {
                            _declineNewRide(
                              reasonId: null,
                              newRideId: newRide.id!,
                            );
                            // showDialog(
                            //   context: context,
                            //   builder: (context) {
                            //     return AlertDialog(
                            //       title: Text("Select Reason"),
                            //       content: SizedBox(
                            //         height:
                            //             MediaQuery.sizeOf(context).height * 0.6,
                            //         child: Scrollbar(
                            //           thumbVisibility: true,
                            //           child: ListView.separated(
                            //             padding: EdgeInsets.fromLTRB(
                            //               4,
                            //               4,
                            //               8,
                            //               4,
                            //             ),
                            //             shrinkWrap: true,
                            //             itemBuilder: (context, index) {
                            //               return InkWell(
                            //                 onTap: () async {
                            //                   Navigator.of(context).pop();
                            //                   _declineNewRide(
                            //                     reasonId: newRide
                            //                         .cancel_reasons![index]
                            //                         .id,
                            //                     newRideId: newRide.id!,
                            //                   );
                            //                 },
                            //                 child: Container(
                            //                   decoration: BoxDecoration(
                            //                     borderRadius: appRadius,
                            //                     color: AppColors.primaryColor(
                            //                       context,
                            //                     ),
                            //                   ),
                            //                   padding: screenPadding,
                            //                   width: double.infinity,
                            //                   child: Text(
                            //                     newRide
                            //                         .cancel_reasons![index]
                            //                         .name,
                            //                   ),
                            //                 ),
                            //               );
                            //             },
                            //             separatorBuilder: (context, index) =>
                            //                 height10,
                            //             itemCount:
                            //                 newRide.cancel_reasons?.length ?? 0,
                            //           ),
                            //         ),
                            //       ),
                            //     );
                            //   },
                            // );
                          },
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: AppButton(
                          onPressed: () async {
                            _acceptNewRide(newRideId: newRide.id!);
                          },
                          text: language.accept,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 100),
                ],
              ),
            );
          },
        ),
        BlocBuilder<RideFlowCubit, RideFlowState>(
          builder: (context, state) {
            if (state is RideFlowLoadingState) {
              return LoaderBottomSheet();
            }
            return SizedBox();
          },
        ),
      ],
    );
  }

  _rideStateFunction({required RideModel ride}) {
    String status = ride.onRideRequest!.status!;
    _arrived_otp_verified.value.add(ride.onRideRequest!.id!);

    if (status == NEW_RIDE_REQUESTED) {
      _onNewRide(onRideRequest: ride.onRideRequest!);
    } else if (status == ARRIVING) {
      _onArrivingState(ride: ride);
    } else if (status == ARRIVED) {
      _onArrivedState(ride: ride);
    } else if (status == IN_PROGRESS) {
      _onInProgressState(ride: ride);
    } else if (status == REACHED) {
      _onReachedState(ride: ride);
    } else if (status == COMPLETED) {
      if (ride.onRideRequest!.isRiderRated != 1) {
        _onCompleted(ride: ride);
      }
    } else if (status == DRIVERCANCELED) {
      _onDriverCancelled(ride: ride);
    }
  }

  _handleRideStateFunctions({required RideModel ride}) async {
    _ride.value = ride;
    _poolRides.value = ride.pool_rides ?? [];

    if (ride.pool_rides != null) {
      if (ride.pool_rides!.length > 1) {
        _onPoolRide(poolRide: ride.pool_rides!);
      } else {
        _rideStateFunction(ride: ride);
      }
    } else {
      if (ride.onRideRequest != null) {
        _rideStateFunction(ride: ride);
      } else {
        GlobalMethods.removerStorageForWaitinfTime();
      }
    }
  }

  Widget RideBottomSheet({required OnRideRequest onRideRequest}) {
    String status = onRideRequest.status!;

    if (status != ARRIVED) {
      _adminNotifyWaitingTimer?.cancel();
    }

    if (status == NEW_RIDE_REQUESTED) {
      return _newRideWidget(newRide: onRideRequest);
    } else if (status == ARRIVING) {
      return _arrivingWidget(onRideRequest: onRideRequest);
    } else if (status == ARRIVED) {
      return _arrivedWidget(onRideRequest: onRideRequest);
    } else if (status == IN_PROGRESS) {
      return _InprogressWidget(onRideRequest: onRideRequest);
    } else if (status == REACHED) {
      return _InprogressWidget(onRideRequest: onRideRequest);
      // return _reachedWidget(onRideRequest: onRideRequest);
    } else if (status == DRIVERCANCELED) {
      return _driverCancelledWidget(onRideRequest: onRideRequest);
    }

    return SizedBox();
  }

  Widget _handleRideBottomSheet({required RideModel ride}) {
    if (ride.onRideRequest == null) {
      return _noRideWidget();
    } else {
      if (ride.pool_rides != null) {
        if (ride.pool_rides!.length > 1) {
          return poolRideWidget(poolrides: ride.pool_rides!);
        } else {
          return RideBottomSheet(onRideRequest: ride.pool_rides![0]);
        }
      } else {
        if (ride.onRideRequest != null) {
          return RideBottomSheet(onRideRequest: ride.onRideRequest!);
        }
      }

      return SizedBox();
    }
  }

  Widget _handlePoolRideBottomSheet({required OnRideRequest onRideRequest}) {
    String status = onRideRequest.status!;
    if (status == NEW_RIDE_REQUESTED) {
      return _newRideWidget(newRide: onRideRequest);
    } else if (status == ARRIVING) {
      return _arrivingPoolwidget(onRideRequest: onRideRequest);
    } else if (status == ARRIVED) {
      return _arrivedPoolWidget(onRideRequest: onRideRequest);
    } else if (status == IN_PROGRESS) {
      return _inProgressPoolWidget(onRideRequest: onRideRequest);
    } else if (status == REACHED) {
      return _reachedPoolWidget(onRideRequest: onRideRequest);
    } else if (status == COMPLETED) {
      if (onRideRequest.isRiderRated == 0) {
        return _completdPoolWidget(onRideRequest: onRideRequest);
      }
    } else if (status == DRIVERCANCELED) {
      return _driverCancelledPoolWidget(onRideRequest: onRideRequest);
    }
    return SizedBox();
  }

  Timer? _mapViewPointUpdaterTimer;

  Future<void> _mapViewPointUpdater() async {
    if (GlobalState.latestLocation == null) {
      return;
    }

    // use in built location icon
    // final driverIcon = await rootBundle.load('images/new-driver-icon.png');
    // var icon = driverIcon.buffer.asUint8List();

    //resize icon using dart
    // ByteData data = await rootBundle.load('images/new-driver-icon.png');
    // ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
    //     targetWidth: 100, targetHeight: 100);
    // ui.FrameInfo fi = await codec.getNextFrame();
    // icon = (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
    //     .buffer
    //     .asUint8List();

    await mapboxMap?.easeTo(
      mapbox.CameraOptions(
        bearing: (GlobalState.latestLocation?.heading ?? 0.0).toDouble(),
        zoom: _cameraZoom,
        pitch: _currentSpeed > HIGH_SPEED_THRESHOLD
            ? HIGH_SPEED_PITCH
            : LOW_SPEED_PITCH,
        center: mapbox.Point(
          coordinates: mapbox.Position(
            GlobalState.latestLocation!.longitude!.toDouble(),
            GlobalState.latestLocation!.latitude!.toDouble(),
          ),
        ),
        padding: _currentSpeed > HIGH_SPEED_THRESHOLD
            ? mapbox.MbxEdgeInsets(top: 300, left: 0, bottom: 0, right: 0)
            : mapbox.MbxEdgeInsets(bottom: 0, left: 0, right: 0, top: 0),
      ),
      mapbox.MapAnimationOptions(duration: _mapInitialCenter ? 1500 : 0),
    );

    this.mapboxMap?.location.updateSettings(
      mapbox.LocationComponentSettings(
        enabled: true,
        pulsingEnabled: true,
        // puckBearingEnabled: true,
        locationPuck: mapbox.LocationPuck(
          locationPuck3D: mapbox.LocationPuck3D(
            modelUri: 'asset://assets/3d_models/arrow4.glb',
            modelScale: [20, 20, 20],
            modelRotation: [
              90,
              -90,
              (GlobalState.latestLocation?.heading ?? 0.0).toDouble() + 90,
            ],
          ),
        ),
      ),
    );

    _mapInitialCenter = true;
  }

  // timer for fine area checks
  Timer? _checkIfDriverInFineAreaTimer;

  Future<void> _checkIfDriverIsOutOfRoute() async {
    if (GlobalState.latestLocation == null ||
        _ride.value.onRideRequest?.isPool == true ||
        _ride.value.onRideRequest?.status == ARRIVED ||
        _newride.value.id != null) {
      return;
    }
    bool isOnTheRoute = RideScreenUtils.isUserWithinPolyline(
      AppLocationType(
        latitude: GlobalState.latestLocation!.latitude!,
        longitude: GlobalState.latestLocation!.longitude!,
      ),
      _currentPolylineCoordinates,
      10,
    );

    if (!isOnTheRoute &&
        _ride.value.onRideRequest?.status != null &&
        _oldRideStatus != _ride.value.onRideRequest?.status) {
      /* Re-create polyline from the current driver location to the destination */
      await _drawSmartPolylines(
        onRideRequest: _ride.value.onRideRequest!,
        isReRouteCreation: true,
      );
    }
  }

  ValueNotifier<bool> _showCancelRason = ValueNotifier(false);
  mapbox.PointAnnotation? _riderCurrentLocationPointAnnotation;
  // bool _isItNewRide = false;
  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    Workmanager().initialize(backgroundDriverStatusManager,);
    Workmanager().registerPeriodicTask(
      "app.rooo.driver.background_driver_status_update",
      "app.rooo.driver.background_driver_status_update",
      initialDelay: Duration(minutes: 20),
      frequency: Duration(minutes: 20),
      constraints: Constraints(networkType: NetworkType.connected),
    );
    _mapViewPointUpdaterTimer = Timer.periodic(const Duration(seconds: 5), (t) {
      _mapViewPointUpdater();
      _checkIfDriverIsOutOfRoute();
    });
    BackgroundAudio.init();
    GlobalState.isLoggedIn = true;
    GlobalState.isProfileComplete =
        sharedPref.getBool(IS_PROFILE_COMPLETE) ?? true;
    afterBuildCreated(() {
      init();
      _checkNotificationPermission();
      _checkLocationPermission();
      _loadFineAreaData();
      _checkIfDriverInFineAreaTimer = Timer.periodic(
        const Duration(seconds: 5),
        (t) {
          _checkFineAreaProximity();
        },
      );
      BlocProvider.of<MapSettingCubit>(
        context,
      ).getMapSetting(userId: sharedPref.getInt(USER_ID)!);
    });
    super.initState();
  }

  _onCameraChanged(cameraChangedEventData) {
    // Detect user interaction with the map
    _isUserInteractingWithMap = true;

    // Cancel any existing timer
    _userInteractionTimer?.cancel();

    // Set a timer to resume automatic camera updates after user stops interacting
    _userInteractionTimer = Timer(Duration(seconds: 5), () {
      _isUserInteractingWithMap = false;
    });
  }

  bool _isAppUserFacing = true;
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _mapViewPointUpdaterTimer?.cancel();
    GlobalState.isLoggedIn = false;
    _rideFlowListenerListener.cancel();
    _arrivedStateWaitingTimer?.cancel();
    _stop_waiting_time_timer?.cancel();
    _adminNotifyWaitingTimer?.cancel();
    GlobalState.driver_device_timer?.cancel();
    GlobalState.driver_server_timer?.cancel();
    _port.close();
    _audioPlayer?.dispose();
    _checkIfDriverInFineAreaTimer?.cancel();
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    // if (_isAppUserFacing || Platform.isAndroid) {
    //   this.mapboxMap?.loadStyleURI(
    //     Theme.of(context).brightness != Brightness.dark
    //         ? mapbox.MapboxStyles.DARK
    //         : mapbox.MapboxStyles.STANDARD,
    //   );
    // }
    super.didChangePlatformBrightness();
  }

  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (!didPop) {
          if (DateTime.now().difference(_lastTime).inSeconds <= 2) {
            SystemNavigator.pop();
          } else {
            _lastTime = DateTime.now();
            GlobalMethods.infoToast(context, "Double tap to exit");
          }
        }
      },
      child: Scaffold(
        key: _scaffoldKey,
        drawer: DrawerDriver(
          driver_rating: _driver_rating,
          scaffoldKey: _scaffoldKey,
          is_profile_completed: GlobalState.isProfileComplete,
        ),
        body: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Stack(
              children: [
                mapbox.MapWidget(
                  // styleUri: Theme.of(context).brightness == Brightness.dark
                  //     ? mapbox.MapboxStyles.DARK
                  //     : mapbox.MapboxStyles.STANDARD,
                  gestureRecognizers: gestureRecognizers,
                  onCameraChangeListener: _onCameraChanged,
                  key: ValueKey("mapWidget"),
                  onMapCreated: _onMapCreated,
                  cameraOptions: mapbox.CameraOptions(
                    bearing: (GlobalState.latestLocation?.heading ?? 0.0)
                        .toDouble(),
                    zoom: _cameraZoom,
                    center: mapbox.Point(
                      coordinates: mapbox.Position(
                        GlobalState.driverPosition?.longitude ?? 133.2096,
                        GlobalState.driverPosition?.latitude ?? -25.9437,
                      ),
                    ),
                  ),
                ),
                _isLocationPermissionForMapAllowed == true ||
                        _checkingMapLocationPermission == true
                    ? const SizedBox()
                    : Center(child: mapOverlayerIfNotGranted(context)),
              ],
            ),
            CustomInfoWindow(
              controller: _customInfoWindowController,
              height: 75,
              width: 150,
              offset: 50,
            ),
            Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: Platform.isIOS
                    ? MediaQuery.of(context).padding.top > 20
                          ? EdgeInsets.only(top: screenPaddingValue * 3)
                          : EdgeInsets.only(top: screenPaddingValue * 1)
                    : EdgeInsets.only(top: screenPaddingValue * 2),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        DrawerIcon(
                          tutorial_key: _drawerKeyButton,
                          scaffoldKey: _scaffoldKey,
                          is_profile_completed: GlobalState.isProfileComplete,
                          isDataLoaded: _homePageDataLoaded,
                        ),
                        ValueListenableBuilder<num>(
                          valueListenable: GlobalState.todaysEarningValue,
                          builder: (context, value, child) {
                            return CurrentEarningWidget(
                              keys: _earningKeyButton,
                              is_profile_completed:
                                  GlobalState.isProfileComplete,
                              current_earning: value,
                              isDataLoaded: _homePageDataLoaded,
                            );
                          },
                        ),
                        SizedBox(width: 60),
                      ],
                    ),
                    ValueListenableBuilder<int>(
                      valueListenable: ZegoVoiceCallService.isinitialized,
                      builder: (context, value, child) {
                        if (value == -1) {
                          return Container(
                            color: AppColors.primaryColor(context),
                            padding: screenPadding,
                            child: Row(
                              children: [
                                SizedBox(
                                  width: MediaQuery.of(context).size.width * .7,
                                  child: Text(
                                    "Call service error, please try to reload the app or tap on reload",
                                    maxLines: 4,
                                    style: TextStyle(
                                      // color: Colors.white,
                                    ),
                                  ),
                                ),
                                TextButton(
                                  child: Text(
                                    "Reload",
                                    style: TextStyle(color: Colors.red),
                                  ),
                                  onPressed: () {
                                    ZegoVoiceCallService.init(
                                      navigatorKey: navigatorKey,
                                      appId: AppCred.zegoAppId,
                                      appSign: AppCred.zegoAppSign,
                                      callerId: sharedPref
                                          .getInt(USER_ID)
                                          .toString(),
                                      callerName: sharedPref
                                          .getString(FIRST_NAME)
                                          .toString(),
                                    );
                                  },
                                ),
                              ],
                            ),
                          );
                        }

                        return SizedBox();
                      },
                    ),
                    ValueListenableBuilder(
                      valueListenable: GlobalState.MqttConnectionState,
                      builder: (context, value, child) {
                        if (GlobalState.MqttConnectionState.value ==
                                MqttConnectionState.connected.name ||
                            GlobalState.MqttConnectionState.value.isEmpty) {
                          return SizedBox();
                        }
                        return Container(
                          color: AppColors.primaryColor(context),
                          padding: screenPadding,
                          child: Row(
                            children: [
                              Row(
                                children: [
                                  SizedBox(
                                    width:
                                        MediaQuery.of(context).size.width * .7,
                                    child: Text(
                                      "Mqtt service error, please try to reload the app or report issue",
                                      maxLines: 4,
                                      style: TextStyle(
                                        // color: Colors.white,
                                      ),
                                    ),
                                  ),
                                  TextButton(
                                    child: Text(
                                      "Report",
                                      style: TextStyle(color: Colors.red),
                                    ),
                                    onPressed: () {
                                      GlobalMethods.pushScreen(
                                        context: context,
                                        screen: NewCareScreen(
                                          isPendingCare: true,
                                        ),
                                        screenIdentifier:
                                            ScreenIdentifier.NewCareScreen,
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    height10,
                  ],
                ),
              ),
            ),
            Positioned(
              left: 10,
              bottom: 140,
              child: SOSbutton(
                tutorial_key: _emergencyKeyButton,
                ride_id: 1,
                region_id: 1,
              ),
            ),
            _isLocationPermisionGranted
                ? Positioned(
                    right: .1,
                    bottom: 140,
                    child: CurrentLocationButton(
                      tutorial_key: _currentLocationKeyButton,
                      animate_map: () {
                        mapboxMap?.easeTo(
                          mapbox.CameraOptions(
                            bearing:
                                (GlobalState.latestLocation!.heading ?? 0.0)
                                    .toDouble(),
                            zoom: _cameraZoom,
                            center: mapbox.Point(
                              coordinates: mapbox.Position(
                                GlobalState.driverPosition?.longitude ?? 10,
                                GlobalState.driverPosition?.latitude ?? 10,
                              ),
                            ),
                          ),
                          mapbox.MapAnimationOptions(),
                        );
                      },
                    ),
                  )
                : const SizedBox(),
            ValueListenableBuilder<OnRideRequest>(
              valueListenable: _newride,
              builder: (context, value, child) {
                if (value.id != null) {
                  return _newRideWidget(newRide: value);
                } else {
                  return ValueListenableBuilder(
                    valueListenable: _poolRides,
                    builder: (context, value, child) {
                      return ValueListenableBuilder<RideModel>(
                        valueListenable: _ride,
                        builder: (context, ride, child) {
                          if (_poolRides.value.length > 1) {
                            return poolRideWidget(poolrides: _poolRides.value);
                          } else {
                            if (ride.id != null) {
                              return _handleRideBottomSheet(ride: ride);
                            } else {
                              return _noRideWidget();
                            }
                          }
                        },
                      );
                    },
                  );
                }
              },
            ),
            // Queued ride overlay - shows on top of current ride UI
            ValueListenableBuilder<OnRideRequest>(
              valueListenable: _queuedRide,
              builder: (context, queuedRideValue, child) {
                if (queuedRideValue.id != null) {
                  return _queuedRideOverlayWidget(queuedRide: queuedRideValue);
                } else {
                  return SizedBox();
                }
              },
            ),
            // Positioned(
            //   top: 20,
            //   child: DirectionsWidget(
            //     onRideRequest: _ride.value.onRideRequest,
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  Future<void> _cancelRideLogic(OnRideRequest onRideRequest) async {
    bool? isDriverWantsToCancel = false;

    /* check if waiting time is started and is above 3 minutes */
    if (onRideRequest.status == "arrived" &&
        _arrivedStateWaitingTimerValue.value < 181) {
      isDriverWantsToCancel = await GlobalMethods.showConfirmationDialog(
        context: context,
        positiveText: "Wait",
        onPositiveAction: () {
          // Navigator.of(context).pop(false);
        },
        negativeText: "Cancel",
        onNegativeAction: () {
          // Navigator.of(context).pop(true);
        },
        title:
            "Please wait for at least 3 minutes before canceling the ride. This allows for waiting charges to be applied, ensuring you receive some compensation for your time.",
      );
    }

    if (!isDriverWantsToCancel) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            // backgroundColor: Colors.red,
            child: Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.7,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    height: 90,
                    width: double.infinity,
                    child: llt.Lottie.asset("assets/lottie/alert.json"),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          "Please select reason to cancel this ride",
                          style: TextStyle(
                            // color: Colors.black,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(height: 16),
                        SizedBox(
                          height: MediaQuery.sizeOf(context).height / 4,
                          child: Scrollbar(
                            thumbVisibility: true,
                            trackVisibility: true,
                            child: ListView.separated(
                              itemCount: onRideRequest.ride_cancel?.length ?? 0,
                              separatorBuilder: (context, index) =>
                                  SizedBox(height: 8),
                              itemBuilder: (context, index) {
                                return Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () async {
                                      Navigator.of(context).pop();

                                      // Check if the selected cancellation reason is an emergency
                                      final selectedReason =
                                          onRideRequest.ride_cancel![index];
                                      if (selectedReason.isEmergency) {
                                        // Show emergency dialog
                                        _showEmergencyDialog(onRideRequest);
                                        return;
                                      }

                                      await GlobalMethods.saveEndWaitingTimeData(
                                        data: LocalWaitingTimeModel(
                                          startTime: DateTime.now(),
                                          endTime: DateTime.now(),
                                          type: LocalWaitingTimeTyes.stop,
                                        ),
                                      );
                                      cancel_ride_request_by_driver(
                                        rideId: onRideRequest.id!,
                                        reason_id: onRideRequest
                                            .ride_cancel![index]
                                            .id,
                                        waitingCharges:
                                            GlobalMethods.getWaitingTimeData()
                                                .map((e) => e.toMap())
                                                .toList(),
                                      );
                                    },
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: appRadius,
                                        color: AppColors.primaryColor(context),
                                      ),
                                      padding: EdgeInsets.symmetric(
                                        vertical: 12,
                                        horizontal: 16,
                                      ),
                                      width: double.infinity,
                                      child: Text(
                                        onRideRequest.ride_cancel![index].name,
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text("Close"),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }
  }

  widgetMiddePart({required OnRideRequest onRideRequest}) {
    return Column(
      children: [
        height20,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(onRideRequest.riderName!, style: AppTextStyles.title()),
          ],
        ),
        onRideRequest.isPool != false
            ? Text("Pool ride", style: AppTextStyles.title())
            : SizedBox(),
        // Divider(),
        // AddressWidget(
        //   onRideRequest: onRideRequest,
        // ),
        // Divider(),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: ZegoCallButton(
                context: context,
                riderId: onRideRequest.riderId.toString(),
                riderName: onRideRequest.riderName!,
              ),
            ),
            Expanded(
              child: ChatButton(
                onRiderPlayerIdMissing: () {
                  GlobalMethods.showConfirmationDialog(
                    context: context,
                    onPositiveAction: () {
                      _getCurrentRide();
                    },
                    title:
                        errorMessage + " " + "Please press ok and check agian",
                  );
                },
                onRideRequest: onRideRequest,
              ),
            ),
            onRideRequest.status == DRIVERCANCELED
                ? SizedBox()
                : onRideRequest.canDriverCancelTheRide == false
                ? const SizedBox()
                : Expanded(
                    child: RideCancelButton(
                      onTap: () async {
                        _cancelRideLogic(onRideRequest);
                      },
                    ),
                  ),
          ],
        ),
        Divider(),
      ],
    );
  }

  Widget _arrivingWidget({required OnRideRequest onRideRequest}) {
    return Stack(
      children: [
        SlidingUpPanel(
          controller: _panelControllerForRideBottomSheet,
          defaultPanelState: PanelState.OPEN,
          maxHeight: Platform.isIOS ? 370 : 350,
          minHeight: 140,
          color: AppColors.whiteColor(context),
          panelBuilder: (sc) {
            return SingleChildScrollView(
              controller: sc,
              child: Padding(
                padding: screenPadding,
                child: Column(
                  children: [
                    // SlidingUpHandleContainer(),
                    SizedBox(
                      height: 100,
                      child: Column(
                        children: [
                          RideEstimatedTime(rideId: onRideRequest.id!),
                          const Divider(),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: AppButton(
                                  width: double.infinity,
                                  text: "Mark arrived",
                                  onPressed: () async {
                                    _markArrived(
                                      rideId: onRideRequest.id!,
                                      pickupLocation: LatLng(
                                        double.parse(
                                          onRideRequest.startLatitude!,
                                        ),
                                        double.parse(
                                          onRideRequest.startLongitude!,
                                        ),
                                      ),
                                      currentLocation:
                                          GlobalState.driverPosition!,
                                    );
                                  },
                                ),
                              ),

                              // Text(
                              //   language.arriving,
                              //   style: AppTextStyles.header(),
                              // ),
                              width10,
                              Expanded(
                                child: OutlinedButton.icon(
                                  icon: Icon(Icons.map),
                                  label: Text("Navigate"),
                                  onPressed: () {
                                    _panelControllerForRideBottomSheet
                                        .animatePanelToPosition(
                                          0.0,
                                          duration: Duration(milliseconds: 300),
                                          curve: Curves.easeInOut,
                                        );
                                    navigateToOuterMap(
                                      context: context,
                                      sourceCoords: map_launcher.Coords(
                                        GlobalState.driverPosition!.latitude,
                                        GlobalState.driverPosition!.longitude,
                                      ),
                                      source: "Current location",
                                      destinationCoords: map_launcher.Coords(
                                        double.parse(
                                          onRideRequest.startLatitude!,
                                        ),
                                        double.parse(
                                          onRideRequest.startLongitude!,
                                        ),
                                      ),
                                      destination: onRideRequest.startAddress
                                          .toString(),
                                      wayPoints: _wayPoints,
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const Divider(),
                    RideContactAndDetailsWidget(
                      rider: _ride.value.rider!,
                      onRideRequest: onRideRequest,
                      onRefreshRideDetails: _getCurrentRide,
                      onCancelRide: _cancelRideLogic,
                    ),

                    height10,
                    Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.location_on,
                                color: Colors.blue,
                                size: 18,
                              ),
                              SizedBox(width: 8),
                              Text(
                                "Pickup Location",
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 4),
                          Text(
                            onRideRequest.startAddress ??
                                "No address available",
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        BlocBuilder<RideFlowCubit, RideFlowState>(
          builder: (context, state) {
            if (state is RideFlowLoadingState) {
              return LoaderBottomSheet();
            }
            return SizedBox();
          },
        ),
      ],
    );
  }

  _poolPersonalDataWidget({required OnRideRequest onRideRequest}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: 90,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                height: 85,
                width: 85,
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.blackColor(context)),
                  image: DecorationImage(
                    image: NetworkImage(onRideRequest.riderProfileImage!),
                  ),
                  shape: BoxShape.circle,
                ),
              ),
              Text(
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                onRideRequest.riderName!,
                style: AppTextStyles.text(),
              ),
            ],
          ),
        ),
        width20,
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                maxLines: 4,
                onRideRequest.status!.toUpperCase(),
                style: AppTextStyles.title(color: AppColors.greenColor),
              ),
              height5,
              Text(
                "From:",
                style: TextStyle(fontSize: 10, fontStyle: FontStyle.italic),
              ),
              Text(
                maxLines: 4,
                onRideRequest.startAddress!,
                style: AppTextStyles.text(),
              ),
              height10,
              Text(
                "To:",
                style: TextStyle(fontSize: 10, fontStyle: FontStyle.italic),
              ),
              Text(
                onRideRequest.endAddress!,
                maxLines: 4,
                style: AppTextStyles.text(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  _arrivingPoolwidget({required OnRideRequest onRideRequest}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            _poolPersonalDataWidget(onRideRequest: onRideRequest),
            const Divider(),
            AppButton(
              // width: double.infinity,
              text: language.arrived,
              onPressed: () async {
                _markArrived(
                  rideId: onRideRequest.id!,
                  pickupLocation: LatLng(
                    double.parse(onRideRequest.startLatitude!),
                    double.parse(onRideRequest.startLongitude!),
                  ),
                  currentLocation: GlobalState.driverPosition!,
                );
              },
            ),
            height10,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ZegoCallButton(
                  context: context,
                  riderId: onRideRequest.riderId.toString(),
                  riderName: onRideRequest.riderName!,
                ),
                width20,
                ChatButton(
                  onRiderPlayerIdMissing: () {
                    GlobalMethods.showConfirmationDialog(
                      context: context,
                      onPositiveAction: () {
                        _getCurrentRide();
                      },
                      title: errorMessage + "Please press ok and check agian",
                    );
                  },
                  onRideRequest: onRideRequest,
                ),
                width20,
                AppButton(
                  text: "Map",
                  onPressed: () async {
                    _panelControllerForRideBottomSheet.animatePanelToPosition(
                      0.0,
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                    navigateToOuterMap(
                      context: context,
                      sourceCoords: map_launcher.Coords(
                        GlobalState.driverPosition!.latitude,
                        GlobalState.driverPosition!.longitude,
                      ),
                      source: "Current location",
                      destinationCoords: map_launcher.Coords(
                        double.parse(onRideRequest.startLatitude!),
                        double.parse(onRideRequest.startLongitude!),
                      ),
                      destination: onRideRequest.startAddress.toString(),
                      wayPoints: _wayPoints,
                    );
                  },
                ),
              ],
            ),
          ],
          // ),
        ),
      ),
    );
  }

  Widget _arrivedPoolWidget({required OnRideRequest onRideRequest}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            _poolPersonalDataWidget(onRideRequest: onRideRequest),
            const Divider(),
            ValueListenableBuilder<Set<int>>(
              valueListenable: _arrived_otp_verified,
              builder: (context, value, child) {
                if (onRideRequest.isOtpEnable ?? false) {
                  if (value.contains(onRideRequest.id)) {
                    return AppButton(
                      text: "Verify",
                      onPressed: () async {
                        _otpVerificationDialog(
                          onRideRequest: onRideRequest,
                          context: context,
                          otp: onRideRequest.otp.toString(),
                          controller: _startPasswordController,
                          onSuccess: () {
                            _arrived_otp_verified.value.remove(
                              onRideRequest.id,
                            );
                            _arrived_otp_verified.notifyListeners();
                            _startPasswordController.clear();
                          },
                        );
                      },
                    );
                  } else {
                    return AppButton(
                      text: "Start Trip",
                      onPressed: () async {
                        _markInprogress(
                          arrived_time: DateTime.parse(
                            onRideRequest.arrived_time!,
                          ),
                          pickupLocation: LatLng(
                            double.parse(onRideRequest.startLatitude!),
                            double.parse(onRideRequest.startLongitude!),
                          ),
                          currentLocation: GlobalState.driverPosition!,
                          rideId: onRideRequest.id!,
                        );
                      },
                    );
                  }
                } else {
                  return AppButton(
                    text: language.StartRideText,
                    onPressed: () async {
                      _markInprogress(
                        arrived_time: DateTime.parse(
                          onRideRequest.arrived_time!,
                        ),
                        pickupLocation: LatLng(
                          double.parse(onRideRequest.startLatitude!),
                          double.parse(onRideRequest.startLongitude!),
                        ),
                        currentLocation: GlobalState.driverPosition!,
                        rideId: onRideRequest.id!,
                      );
                    },
                  );
                }
              },
            ),
            height10,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ZegoCallButton(
                  context: context,
                  riderId: onRideRequest.riderId.toString(),
                  riderName: onRideRequest.riderName!,
                ),
                width20,
                ChatButton(
                  onRiderPlayerIdMissing: () {
                    GlobalMethods.showConfirmationDialog(
                      context: context,
                      onPositiveAction: () {
                        _getCurrentRide();
                      },
                      title: errorMessage + "Please press ok and check agian",
                    );
                  },
                  onRideRequest: onRideRequest,
                ),
                width20,
                AppButton(
                  text: "Map",
                  onPressed: () async {
                    _panelControllerForRideBottomSheet.animatePanelToPosition(
                      0.0,
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                    navigateToOuterMap(
                      context: context,
                      sourceCoords: map_launcher.Coords(
                        GlobalState.driverPosition!.latitude,
                        GlobalState.driverPosition!.longitude,
                      ),
                      source: "Current location",
                      destinationCoords: map_launcher.Coords(
                        double.parse(onRideRequest.startLatitude!),
                        double.parse(onRideRequest.startLongitude!),
                      ),
                      destination: onRideRequest.startAddress.toString(),
                      wayPoints: _wayPoints,
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  _driverCancelledPoolWidget({required OnRideRequest onRideRequest}) {
    return _slidingContainer(
      onRideRequest: onRideRequest,
      child: Column(
        children: [
          Row(
            children: [
              _poolPersonalDataWidget(onRideRequest: onRideRequest),
              Spacer(),
              Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: radius(),
                  color: Colors.black,
                ),
                child: CustomText(
                  data: "Cancelled",
                  size: 15,
                  textAlign: TextAlign.center,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  _completdPoolWidget({required OnRideRequest onRideRequest}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            _poolPersonalDataWidget(onRideRequest: onRideRequest),
            const Divider(),
            AppButton(
              text: "Rate",
              onPressed: () async {
                _onPoolRideCompleted(onRideRequest: onRideRequest);
              },
            ),
            height10,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ZegoCallButton(
                  context: context,
                  riderId: onRideRequest.riderId.toString(),
                  riderName: onRideRequest.riderName!,
                ),
                width20,
                ChatButton(
                  onRiderPlayerIdMissing: () {
                    GlobalMethods.showConfirmationDialog(
                      context: context,
                      onPositiveAction: () {
                        _getCurrentRide();
                      },
                      title: errorMessage + "Please press ok and check agian",
                    );
                  },
                  onRideRequest: onRideRequest,
                ),
                width20,
                AppButton(
                  text: "Map",
                  onPressed: () async {
                    _panelControllerForRideBottomSheet.animatePanelToPosition(
                      0.0,
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                    navigateToOuterMap(
                      context: context,
                      sourceCoords: map_launcher.Coords(
                        GlobalState.driverPosition!.latitude,
                        GlobalState.driverPosition!.longitude,
                      ),
                      source: "Current location",
                      destinationCoords: map_launcher.Coords(
                        double.parse(onRideRequest.startLatitude!),
                        double.parse(onRideRequest.startLongitude!),
                      ),
                      destination: onRideRequest.startAddress.toString(),
                      wayPoints: _wayPoints,
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  _reachedPoolWidget({required OnRideRequest onRideRequest}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            _poolPersonalDataWidget(onRideRequest: onRideRequest),
            const Divider(),
            AppButton(
              text: language.endRide,
              onPressed: () async {
                _endRide(onrideRequest: onRideRequest);
              },
            ),
            height10,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ZegoCallButton(
                  context: context,
                  riderId: onRideRequest.riderId.toString(),
                  riderName: onRideRequest.riderName!,
                ),
                width20,
                ChatButton(
                  onRiderPlayerIdMissing: () {
                    GlobalMethods.showConfirmationDialog(
                      context: context,
                      onPositiveAction: () {
                        _getCurrentRide();
                      },
                      title: errorMessage + "Please press ok and check agian",
                    );
                  },
                  onRideRequest: onRideRequest,
                ),
                width20,
                AppButton(
                  text: "Map",
                  onPressed: () async {
                    _panelControllerForRideBottomSheet.animatePanelToPosition(
                      0.0,
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                    navigateToOuterMap(
                      context: context,
                      sourceCoords: map_launcher.Coords(
                        GlobalState.driverPosition!.latitude,
                        GlobalState.driverPosition!.longitude,
                      ),
                      source: "Current location",
                      destinationCoords: map_launcher.Coords(
                        double.parse(onRideRequest.startLatitude!),
                        double.parse(onRideRequest.startLongitude!),
                      ),
                      destination: onRideRequest.startAddress.toString(),
                      wayPoints: _wayPoints,
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  _inProgressPoolWidget({required OnRideRequest onRideRequest}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            _poolPersonalDataWidget(onRideRequest: onRideRequest),
            const Divider(),
            AppButton(
              text: language.reached,
              onPressed: () async {
                _markReached(
                  destinationLocation: LatLng(
                    double.parse(onRideRequest.endLatitude!),
                    double.parse(onRideRequest.endLongitude!),
                  ),
                  currentLocation: GlobalState.driverPosition!,
                  onrideRequest: onRideRequest,
                );
              },
            ),
            height10,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ZegoCallButton(
                  context: context,
                  riderId: onRideRequest.riderId.toString(),
                  riderName: onRideRequest.riderName!,
                ),
                width20,
                ChatButton(
                  onRiderPlayerIdMissing: () {
                    GlobalMethods.showConfirmationDialog(
                      context: context,
                      onPositiveAction: () {
                        _getCurrentRide();
                      },
                      title: errorMessage + "Please press ok and check agian",
                    );
                  },
                  onRideRequest: onRideRequest,
                ),
                width20,
                AppButton(
                  text: "Map",
                  onPressed: () async {
                    _panelControllerForRideBottomSheet.animatePanelToPosition(
                      0.0,
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                    navigateToOuterMap(
                      context: context,
                      sourceCoords: map_launcher.Coords(
                        GlobalState.driverPosition!.latitude,
                        GlobalState.driverPosition!.longitude,
                      ),
                      source: "Current location",
                      destinationCoords: map_launcher.Coords(
                        double.parse(onRideRequest.startLatitude!),
                        double.parse(onRideRequest.startLongitude!),
                      ),
                      destination: onRideRequest.startAddress.toString(),
                      wayPoints: _wayPoints,
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _getArrivedPaidTimeView() {
    if (_adminNotifyWaitingTimeTimerValue.value > 0) {
      return ValueListenableBuilder<int>(
        valueListenable: _adminNotifyWaitingTimeTimerValue,
        builder: (context, state, _) {
          if (state == -2) {
            return custom_loader();
          }
          if (state > 0) {
            return Card(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Paid time will start in...",
                      style: TextStyle(fontSize: 12),
                    ),
                    Text(
                      intToTimeLeftInSeconds(
                        _adminNotifyWaitingTimeTimerValue.value,
                      ),
                      style: AppTextStyles.header(),
                    ),
                  ],
                ),
              ),
            );
          }
          return SizedBox();
        },
      );
    }

    return ValueListenableBuilder<num>(
      valueListenable: _arrivedStateWaitingTimerValue,
      builder: (context, state, _) {
        if (state == -1 || state == -2) {
          return SizedBox();
        } else if (state > 0) {
          return Row(
            children: [
              const SizedBox(width: 20),
              Container(
                width: 140,
                decoration: BoxDecoration(
                  borderRadius: appRadius,
                  color: Colors.white,
                  border: Border.all(),
                ),
                padding: EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      intToTimeLeftInSeconds(
                        _arrivedStateWaitingTimerValue.value.toInt(),
                      ),
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    height5,
                    Text(
                      language.waitTime,
                      style: TextStyle(color: Colors.black, fontSize: 8),
                    ),
                    width5,
                  ],
                ),
              ),
            ],
          );
        }
        return SizedBox();
      },
    );
  }

  Widget _arrivedWidget({required OnRideRequest onRideRequest}) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        SlidingUpPanel(
          maxHeight: 300,
          minHeight: 130,
          controller: _panelControllerForRideBottomSheet,
          defaultPanelState: PanelState.OPEN,
          color: AppColors.whiteColor(context),
          panelBuilder: (sc) {
            return SingleChildScrollView(
              controller: sc,
              child: Padding(
                padding: screenPadding,
                child: Column(
                  children: [
                    SlidingUpHandleContainer(),
                    SizedBox(
                      height: 100,
                      child: Row(
                        children: [
                          Expanded(
                            child: ValueListenableBuilder<Set<int>>(
                              valueListenable: _arrived_otp_verified,
                              builder: (context, value, child) {
                                if (onRideRequest.isOtpEnable ?? false) {
                                  if (_arrived_otp_verified.value.contains(
                                    onRideRequest.id,
                                  )) {
                                    return AppButton(
                                      width: double.infinity,
                                      text: "Verify",
                                      onPressed: () async {
                                        _otpVerificationDialog(
                                          onRideRequest: onRideRequest,
                                          context: context,
                                          otp: onRideRequest.otp.toString(),
                                          controller: _startPasswordController,
                                          onSuccess: () {
                                            _arrived_otp_verified.value.remove(
                                              onRideRequest.id,
                                            );
                                            _arrived_otp_verified
                                                .notifyListeners();
                                            _startPasswordController.clear();
                                          },
                                        );
                                      },
                                    );
                                  } else {
                                    return AppButton(
                                      width: double.infinity,
                                      text: "Start trip",
                                      onPressed: () async {
                                        _markInprogress(
                                          arrived_time: DateTime.parse(
                                            onRideRequest.arrived_time!,
                                          ),
                                          pickupLocation: LatLng(
                                            double.parse(
                                              onRideRequest.startLatitude!,
                                            ),
                                            double.parse(
                                              onRideRequest.startLongitude!,
                                            ),
                                          ),
                                          currentLocation:
                                              GlobalState.driverPosition!,
                                          rideId: onRideRequest.id!,
                                        );
                                      },
                                    );
                                  }
                                } else {
                                  return AppButton(
                                    width: double.infinity,
                                    text: language.StartRideText,
                                    onPressed: () async {
                                      _markInprogress(
                                        arrived_time: DateTime.parse(
                                          onRideRequest.arrived_time!,
                                        ),
                                        pickupLocation: LatLng(
                                          double.parse(
                                            onRideRequest.startLatitude!,
                                          ),
                                          double.parse(
                                            onRideRequest.startLongitude!,
                                          ),
                                        ),
                                        currentLocation:
                                            GlobalState.driverPosition!,
                                        rideId: onRideRequest.id!,
                                      );
                                    },
                                  );
                                }
                              },
                            ),
                          ),
                          width10,
                          _getArrivedPaidTimeView(),
                        ],
                      ),
                    ),
                    const Divider(),
                    RideContactAndDetailsWidget(
                      rider: _ride.value.rider!,
                      onRideRequest: onRideRequest,
                      onRefreshRideDetails: _getCurrentRide,
                      onCancelRide: _cancelRideLogic,
                    ),
                    height10,
                    // Container(
                    //   padding: EdgeInsets.all(12),
                    //   decoration: BoxDecoration(
                    //     color: Colors.grey[100],
                    //     borderRadius: BorderRadius.circular(8),
                    //     border: Border.all(color: Colors.grey[300]!),
                    //   ),
                    //   child: Column(
                    //     crossAxisAlignment: CrossAxisAlignment.start,
                    //     children: [
                    //       Row(
                    //         children: [
                    //           Icon(Icons.location_on,
                    //               color: Colors.blue, size: 18),
                    //           SizedBox(width: 8),
                    //           Text("Drop Off Location",
                    //               style: TextStyle(
                    //                   fontSize: 12,
                    //                   color: Colors.grey[600],
                    //                   fontWeight: FontWeight.w500)),
                    //         ],
                    //       ),
                    //       SizedBox(height: 4),
                    //       Text(
                    //         onRideRequest.endAddress ?? "No address available",
                    //         style: TextStyle(
                    //           fontSize: 14,
                    //           color: Colors.black87,
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                    // ),
                  ],
                ),
              ),
            );
          },
        ),
        BlocBuilder<RideFlowCubit, RideFlowState>(
          builder: (context, state) {
            if (state is RideFlowLoadingState) {
              return LoaderBottomSheet();
            }
            return SizedBox();
          },
        ),
      ],
    );
  }

  void _markStopReached(OnRideRequest onRideRequest) {
    StopsModel selectedStop = onRideRequest.stops!.firstWhere(
      (element) => element.status == null,
    );

    _reachedAtStop(stop: selectedStop);
  }

  Widget _InprogressWidget({required OnRideRequest onRideRequest}) {
    StopsModel? _currentStop = onRideRequest.stop_pending;
    return Stack(
      children: [
        SlidingUpPanel(
          controller: _panelControllerForRideBottomSheet,
          defaultPanelState: PanelState.OPEN,
          maxHeight: 420,
          minHeight: 140,
          color: AppColors.whiteColor(context),
          panelBuilder: (sc) {
            return SingleChildScrollView(
              controller: sc,
              child: Padding(
                padding: screenPadding,
                child: Column(
                  children: [
                    // SlidingUpHandleContainer(),
                    SizedBox(
                      height: 100,
                      child: _currentStop?.status == "arrived"
                          ? _getArrivedAtStopWidget(
                              _currentStop!,
                              showJustActionsView: true,
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                onRideRequest.status == REACHED
                                    ? Text(
                                        "You reached at destination",
                                        style: TextStyle(
                                          color: Colors.green,
                                          fontSize: 12,
                                        ),
                                      )
                                    : const SizedBox(),
                                onRideRequest.status != REACHED
                                    ? Column(
                                        children: [
                                          RideEstimatedTime(
                                            rideId: onRideRequest.id!,
                                          ),
                                          const Divider(),
                                        ],
                                      )
                                    : const SizedBox(),
                                Row(
                                  children: [
                                    Expanded(
                                      child: onRideRequest.stop_pending != null
                                          ? AppButton(
                                              text: "Stop reached",
                                              onPressed: () async {
                                                _markStopReached(onRideRequest);
                                              },
                                            )
                                          : AppButton(
                                              text: "End Trip",
                                              onPressed: () async {
                                                if (onRideRequest.status !=
                                                    REACHED) {
                                                  await _markReached(
                                                    destinationLocation: LatLng(
                                                      double.parse(
                                                        onRideRequest
                                                            .endLatitude!,
                                                      ),
                                                      double.parse(
                                                        onRideRequest
                                                            .endLongitude!,
                                                      ),
                                                    ),
                                                    currentLocation: GlobalState
                                                        .driverPosition!,
                                                    onrideRequest:
                                                        onRideRequest,
                                                  );
                                                } else {
                                                  _endRide(
                                                    onrideRequest:
                                                        onRideRequest,
                                                  );
                                                }
                                              },
                                            ),
                                    ),
                                    width20,
                                    Expanded(
                                      child: OutlinedButton.icon(
                                        icon: Icon(Icons.map),
                                        label: Text("Navigate"),
                                        onPressed: () async {
                                          _panelControllerForRideBottomSheet
                                              .animatePanelToPosition(
                                                0.0,
                                                duration: Duration(
                                                  milliseconds: 300,
                                                ),
                                                curve: Curves.easeInOut,
                                              );

                                          navigateToOuterMap(
                                            context: context,
                                            sourceCoords: map_launcher.Coords(
                                              GlobalState
                                                  .driverPosition!
                                                  .latitude,
                                              GlobalState
                                                  .driverPosition!
                                                  .longitude,
                                            ),
                                            source: "Current location",
                                            destinationCoords:
                                                map_launcher.Coords(
                                                  double.parse(
                                                    onRideRequest.endLatitude!,
                                                  ),
                                                  double.parse(
                                                    onRideRequest.endLongitude!,
                                                  ),
                                                ),
                                            destination: onRideRequest
                                                .endAddress
                                                .toString(),
                                            wayPoints: _wayPoints,
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                    ),
                    const Divider(),
                    RideContactAndDetailsWidget(
                      rider: _ride.value.rider!,
                      onRideRequest: onRideRequest,
                      onRefreshRideDetails: _getCurrentRide,
                      onCancelRide: _cancelRideLogic,
                    ),
                    _destinationChangedView(onRideRequest),
                    onRideRequest.stop_pending != null
                        ? ValueListenableBuilder(
                            valueListenable: _stop_waiting_time_timer_value,
                            builder: (context, value, child) {
                              StopsModel _currentStop =
                                  onRideRequest.stop_pending!;

                              if (_currentStop.status == null) {
                                return Padding(
                                  padding: const EdgeInsets.only(top: 10.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Next Stop"),
                                      Material(
                                        color: Colors.grey.shade300,
                                        child: InkWell(
                                          splashColor: AppColors.primaryColor(
                                            context,
                                          ),
                                          onTap: () {
                                            _markStopReached(onRideRequest);
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.all(8),
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: Row(
                                              children: [
                                                Icon(
                                                  Icons.back_hand,
                                                  color: Colors.red,
                                                ),
                                                width15,
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        "Tap to mark reached at",
                                                        style: TextStyle(
                                                          fontSize: 10,
                                                          color: Colors.black,
                                                        ),
                                                      ),
                                                      Text(
                                                        _currentStop.title,
                                                        maxLines: 3,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: TextStyle(
                                                          // fontSize: 12,
                                                          color: Colors.black,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                InkWell(
                                                  onTap: () {
                                                    _showAllStops(
                                                      onRideRequest.stops!,
                                                    );
                                                  },
                                                  child: Icon(
                                                    Icons.list,
                                                    color: Colors.black,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              } else if (_currentStop.status == "arrived") {
                                return Padding(
                                  padding: const EdgeInsets.only(top: 8.0),
                                  child: _getArrivedAtStopWidget(_currentStop),
                                );
                              }

                              return SizedBox();
                            },
                          )
                        : const SizedBox(),
                    height10,
                    Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.location_on,
                                color: Colors.blue,
                                size: 18,
                              ),
                              SizedBox(width: 8),
                              Text(
                                "Drop Off Location",
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 4),
                          Text(
                            onRideRequest.endAddress ?? "No address available",
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        BlocBuilder<RideFlowCubit, RideFlowState>(
          builder: (context, state) {
            if (state is RideFlowLoadingState) {
              return LoaderBottomSheet();
            }
            return SizedBox();
          },
        ),
      ],
    );
  }

  Widget _getArrivedAtStopWidget(
    StopsModel stop, {
    bool showJustActionsView = false,
  }) {
    late Widget child;

    if (stop.timerStart == null) {
      child = Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text("Wait timer?"),
          Row(
            children: [
              AppButton(
                backgroundColor: Colors.grey.shade300,
                text: "Start",
                onPressed: () async {
                  _startStopTime(id: stop.id);
                  if (!_isStopWaitingTimeNotifyAPICalled) {
                    notifyWaitingTimeStarted(
                      rideId: _ride.value.onRideRequest?.id ?? 0,
                    );
                    _isStopWaitingTimeNotifyAPICalled = true;
                  }
                },
              ),
              width5,
              AppButton(
                text: "Continue ride",
                onPressed: () async {
                  _endStopTime(iscanceled: true, id: stop.id);
                },
              ),
            ],
          ),
        ],
      );
    } else {
      child = Column(
        children: [
          const Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ValueListenableBuilder<int>(
                valueListenable: _stop_waiting_time_timer_value,
                builder: (context, value, child) {
                  return Text(intToTimeLeftInSeconds(value.toInt()));
                },
              ),
              AppButton(
                text: "Continue ride",
                onPressed: () async {
                  _endStopTime(
                    iscanceled: false,
                    id: stop.id,
                    startTime: stop.timerStart!,
                  );
                },
              ),
            ],
          ),
          const Divider(),
        ],
      );
    }

    if (showJustActionsView) {
      return child;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("You are At:", style: TextStyle(fontSize: 10)),
        Text(stop.title),
        Container(
          padding: const EdgeInsets.all(8),
          // color: Colors.grey.shade200,
          child: Row(
            children: [
              Icon(Icons.info),
              width5,
              Expanded(
                child: stop.timerStart == null
                    ? Text(
                        "If it is taking longer, You can start the wait timer.",
                        style: TextStyle(fontStyle: FontStyle.italic),
                      )
                    : Text(
                        "You started the waiting timer...",
                        style: TextStyle(fontStyle: FontStyle.italic),
                      ),
              ),
            ],
          ),
        ),
        // const Divider(),
        // child,
        // const Divider(),
      ],
    );
  }

  Widget _reachedWidget({required OnRideRequest onRideRequest}) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        SlidingUpPanel(
          controller: _panelControllerForRideBottomSheet,
          defaultPanelState: PanelState.OPEN,
          color: AppColors.whiteColor(context),
          panelBuilder: (sc) {
            return SingleChildScrollView(
              controller: sc,
              child: Padding(
                padding: screenPadding,
                child: Column(
                  children: [
                    SlidingUpHandleContainer(),
                    Text(language.reached, style: AppTextStyles.header()),
                    widgetMiddePart(onRideRequest: onRideRequest),
                    AppButton(
                      width: double.infinity,
                      text: language.endRide,
                      onPressed: () async {
                        _endRide(onrideRequest: onRideRequest);
                      },
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        BlocBuilder<RideFlowCubit, RideFlowState>(
          builder: (context, state) {
            if (state is RideFlowLoadingState) {
              return LoaderBottomSheet();
            }
            return SizedBox();
          },
        ),
      ],
    );
  }

  void _otpVerificationDialog({
    required OnRideRequest onRideRequest,
    required BuildContext context,
    required String otp,
    required TextEditingController controller,
    required void Function() onSuccess,
  }) {
    showDialog(
      context: context,
      builder: (_) {
        return AlertDialog(
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: inkWellWidget(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: EdgeInsets.all(4),
                    decoration: BoxDecoration(shape: BoxShape.circle),
                    child: Icon(Icons.close, size: 20, color: Colors.white),
                  ),
                ),
              ),
              SizedBox(height: 8),
              Center(
                child: Text(
                  language.passCode,
                  style: AppTextStyles.header(),
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 16),
              Text(
                language.enterthePasscodewhichIsDisplayingInTheCustomersMobile,
                style: AppTextStyles.title(),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16),
              Pinput(
                controller: controller,
                length: 4,
                onCompleted: (pin) {
                  // _otp = pin;
                },
              ),
              height10,
              AppButton(
                width: double.infinity,
                text: language.verify,
                onPressed: () async {
                  if (controller.text.length < 4 || controller.text != otp) {
                    GlobalMethods.infoToast(
                      context,
                      language.pleaseEnterValidOtp,
                    );
                    controller.clear();
                  } else {
                    Navigator.pop(context);
                    onSuccess();
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _destinationChangedView(OnRideRequest ride) {
    if (ride.destinationPlace == null) {
      return const SizedBox();
    }
    return Padding(
      padding: const EdgeInsets.only(top: 10),
      child: Container(
        padding: const EdgeInsets.all(8),
        color: Colors.grey.shade200,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.airline_stops, color: Colors.red),
                width5,
                Expanded(
                  child: Text(
                    "Destination changed ${ride.destinationPlace!.length} time/s",
                    style: TextStyle(color: Colors.black),
                  ),
                ),
                InkWell(
                  onTap: () {
                    _showAllDestinations(ride.destinationPlace!);
                  },
                  child: Icon(Icons.list, color: Colors.black),
                ),
              ],
            ),
            height10,
            Text(
              "Final destination is",
              style: TextStyle(fontSize: 10, color: Colors.black),
            ),
            Text(
              ride.destinationPlace![ride.destinationPlace!.length - 1].title,
              style: TextStyle(color: Colors.black),
            ),
            (ride.endAddress ?? "") ==
                    ride
                        .destinationPlace![ride.destinationPlace!.length - 1]
                        .title
                ? const SizedBox()
                : Text(
                    ride.endAddress ?? "",
                    style: TextStyle(color: Colors.black),
                  ),
          ],
        ),
      ),
    );
  }

  void _showAllDestinations(List<StopsModel> destinations) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("All Destinations"),
          content: Container(
            width: double.maxFinite,
            child: ListView.separated(
              shrinkWrap: true,
              itemCount: destinations.length,
              separatorBuilder: (BuildContext context, int index) => Divider(),
              itemBuilder: (BuildContext context, int index) {
                return ListTile(
                  title: Text(destinations[index].title),
                  subtitle: Text(destinations[index].currentAdress ?? ""),
                );
              },
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text("Close"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _showAllStops(List<StopsModel> destinations) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("All Stops"),
          content: Container(
            width: double.maxFinite,
            child: ListView.separated(
              shrinkWrap: true,
              itemCount: destinations.length,
              separatorBuilder: (BuildContext context, int index) => Divider(),
              itemBuilder: (BuildContext context, int index) {
                return ListTile(
                  title: Text(destinations[index].title),
                  subtitle: Text(destinations[index].currentAdress ?? ""),
                );
              },
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text("Close"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  poolRideWidget({required List<OnRideRequest> poolrides}) {
    return Stack(
      children: [
        SlidingUpPanel(
          controller: _panelControllerForRideBottomSheet,
          defaultPanelState: PanelState.OPEN,
          color: AppColors.whiteColor(context),
          panelBuilder: (sc) {
            return SingleChildScrollView(
              controller: sc,
              child: Padding(
                padding: screenPadding,
                child: Column(
                  children: [
                    SlidingUpHandleContainer(),
                    Text("Pool ride", style: AppTextStyles.header()),
                    ListView.separated(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        OnRideRequest onRideRequest = poolrides[index];

                        return ValueListenableBuilder<OnRideRequest>(
                          valueListenable: _selectedOnrideRequest,
                          builder: (context, value, child) {
                            return _handlePoolRideBottomSheet(
                              onRideRequest: onRideRequest,
                            );
                          },
                        );
                      },
                      separatorBuilder: (context, index) => height10,
                      itemCount: poolrides.length,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        BlocBuilder<RideFlowCubit, RideFlowState>(
          builder: (context, state) {
            if (state is RideFlowLoadingState) {
              return LoaderBottomSheet();
            }
            return SizedBox();
          },
        ),
      ],
    );
  }

  Widget _slidingContainer({
    required OnRideRequest onRideRequest,
    required Widget child,
  }) {
    return Slidable(
      // Specify a key if the Slidable is dismissible.
      key: const ValueKey(0),

      endActionPane: ActionPane(
        extentRatio: .3,
        motion: ScrollMotion(),
        children: [
          Row(
            children: [
              width20,
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ZegoCallButton(
                    context: context,
                    riderId: onRideRequest.riderId.toString(),
                    riderName: onRideRequest.riderName!,
                  ),
                  ChatButton(
                    onRiderPlayerIdMissing: () {
                      GlobalMethods.showConfirmationDialog(
                        context: context,
                        onPositiveAction: () {
                          _getCurrentRide();
                        },
                        title: errorMessage + "Please press ok and check again",
                      );
                    },
                    onRideRequest: onRideRequest,
                  ),
                  // height20,
                ],
              ),
            ],
          ),
        ],
      ),

      child: InkWell(
        onTap: () {},
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: screenPaddingValue / 2,
            vertical: screenPaddingValue / 2,
          ),
          decoration: BoxDecoration(
            border: _selectedOnrideRequest.value.id == onRideRequest.id
                ? Border.all(width: 2, color: AppColors.greenColor)
                : Border.all(color: AppColors.blackColor(context), width: .5),
          ),
          child: Column(
            children: [
              Align(
                alignment: Alignment.topRight,
                child: ChatCounter(onRideRequest: onRideRequest),
              ),
              child,
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _clearMapAnnotations() async {
    try {
      for (var annotation in _mapAnnotations) {
        await _pointAnnotationManager.delete(annotation);
      }
      _mapAnnotations.clear();
    } catch (e) {
      _mapAnnotations.clear();
    }
  }
}

Widget permissionMessageWidget() {
  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      Text(
        """To make sure you never miss a ride request, we kindly ask for "Allow all the time" background location permission. This will help us: """,
        style: AppTextStyles.LowTitle(),
      ),
    ],
  );
}

Widget mapOverlayerIfNotGranted(BuildContext context) {
  return Container(
    margin: EdgeInsets.all(16),
    padding: EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white.withOpacity(0.15),
      borderRadius: BorderRadius.circular(20),
      boxShadow: [
        BoxShadow(
          // color: Colors.white.withOpacity(0.1),
          blurRadius: 100,
          spreadRadius: 3,
        ),
      ],
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          "Permissions Required",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 8),
        Text(
          "To view your current location on the map, please grant the required permissions.",
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 14, color: Colors.white.withOpacity(0.8)),
        ),
        SizedBox(height: 24),
        AppButton(
          text: "Continue",
          onPressed: () async {
            Navigator.of(context).push<bool>(
              MaterialPageRoute(
                builder: (context) => RequiredPermissionScreen(),
              ),
            );
          },
        ),
      ],
    ),
  );
}
