import 'package:geolocator/geolocator.dart';
import 'package:rooo_driver/global/state/global_state.dart';

class RideScreenUtils {
  static bool isAlreadyInArrived = false;
  static bool isAlreadyInReached = false;

  static Future<void> onNearArrivedLocation(
      {required double currentLatitude,
      required double currentLongitude,
      required double targetLatitude,
      required double targetLongitude,
      required Function callback}) async {
    if (isAlreadyInArrived) {
      return;
    }
    num distanceInMeters = Geolocator.distanceBetween(
      currentLatitude,
      currentLongitude,
      targetLatitude,
      targetLongitude,
    );

    if (distanceInMeters <=
        (GlobalState.appSettingModel?.arrivedGeofencingLimit ?? 100)) {
      isAlreadyInArrived = true;
      callback();
    }
  }

  static Future<void> onNearReachedLocation(
      {required double currentLatitude,
      required double currentLongitude,
      required double targetLatitude,
      required double targetLongitude,
      required Function callback}) async {
    if (isAlreadyInReached) {
      return;
    }
    num distanceInMeters = Geolocator.distanceBetween(
      currentLatitude,
      currentLongitude,
      targetLatitude,
      targetLongitude,
    );

    if (distanceInMeters <=
        (GlobalState.appSettingModel?.arrivedGeofencingLimit ?? 100)) {
      isAlreadyInReached = true;
      callback();
    }
  }

  static num getDistanceBetweenTwoPoints(
      {required double startLatitude,
      required double startLongitude,
      required double endLatitude,
      required double endLongitude}) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  static bool isUserWithinPolyline(
    AppLocationType userLocation,
    List<AppLocationType> polylineCoordinates,
    double deviationThresholdMeters,
  ) {
    if (polylineCoordinates.isEmpty) {
      return true;
    }

    double minDistanceToPolyline = double.infinity;

    // Iterate through each segment of the polyline
    for (int i = 0; i < polylineCoordinates.length - 1; i++) {
      final segmentStart = polylineCoordinates[i];
      final segmentEnd = polylineCoordinates[i + 1];

      // Calculate the shortest distance from the user's location to the current segment
      final distance = _calculateDistancePointToSegment(
        userLocation,
        segmentStart,
        segmentEnd,
      );

      if (distance < minDistanceToPolyline) {
        minDistanceToPolyline = distance;
      }

      // Optimization: If we found a segment that is already within the threshold,
      // we can stop early.
      if (minDistanceToPolyline <= deviationThresholdMeters) {
        return true;
      }
    }

    // After checking all segments, if the minimum distance found is greater than
    // the threshold, the user is off route.
    return minDistanceToPolyline <= deviationThresholdMeters;
  }

  /// Calculates the shortest distance in meters from a point to a line segment
  /// defined by two geographic coordinates (LatLng).
  ///
  /// This function uses a common geometric approach adapted for spherical coordinates
  /// by leveraging `Geolocator.distanceBetween` for final distance calculations.
  ///
  /// [point]: The point (user's location) to measure from.
  /// [segmentStart]: The starting point of the line segment.
  /// [segmentEnd]: The ending point of the line segment.
  ///
  /// Returns the shortest distance in meters.
  static double _calculateDistancePointToSegment(
    AppLocationType point,
    AppLocationType segmentStart,
    AppLocationType segmentEnd,
  ) {
    // Convert LatLng to a simplified representation for projection calculation.
    // We'll use their numerical differences for vector math, then use Haversine
    // for the actual distance at the end. This is a common simplification
    // for relatively short distances where projection onto a local plane is acceptable.
    final pX = point.longitude;
    final pY = point.latitude;
    final sX1 = segmentStart.longitude;
    final sY1 = segmentStart.latitude;
    final sX2 = segmentEnd.longitude;
    final sY2 = segmentEnd.latitude;

    final dx = sX2 - sX1;
    final dy = sY2 - sY1;

    // If the segment is a single point (or very, very short),
    // return distance to one of its endpoints.
    if (dx == 0 && dy == 0) {
      return Geolocator.distanceBetween(
        point.latitude,
        point.longitude,
        segmentStart.latitude,
        segmentStart.longitude,
      );
    }

    // Calculate 't', the projection of 'point' onto the infinite line
    // defined by the segment, as a fraction of the segment's length.
    // This is the dot product of vector S1P and vector S1S2, divided by
    // the squared length of S1S2.
    final t = ((pX - sX1) * dx + (pY - sY1) * dy) / (dx * dx + dy * dy);

    // Determine the closest point on the line segment
    double closestX, closestY;
    if (t < 0) {
      // Closest point is segmentStart
      closestX = sX1;
      closestY = sY1;
    } else if (t > 1) {
      // Closest point is segmentEnd
      closestX = sX2;
      closestY = sY2;
    } else {
      // Closest point is on the segment
      closestX = sX1 + t * dx;
      closestY = sY1 + t * dy;
    }

    // Calculate the actual geodesic distance between the user's point
    // and the closest point on the segment.
    return Geolocator.distanceBetween(
      point.latitude, point.longitude,
      closestY, closestX, // Note: LatLng takes (latitude, longitude)
    );
  }
}

class AppLocationType {
  double latitude = 0;
  double longitude = 0;
  AppLocationType({required this.latitude, required this.longitude});
}
