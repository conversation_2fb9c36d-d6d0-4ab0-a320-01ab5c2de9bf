/* A widget that displays the directions for a ride. It will fetch the directions from MapBox API and will display the directions as the driver/user moves. */

import 'package:rooo_driver/features/ride_flow/model/multiple_stop_model.dart';
import 'package:rooo_driver/features/ride_flow/screen/ride_screen_utils.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:uuid/uuid.dart';

class DirectionsWidget extends StatefulWidget {
  final OnRideRequest? onRideRequest;

  const DirectionsWidget({
    Key? key,
    required this.onRideRequest,
  }) : super(key: key);

  @override
  State<DirectionsWidget> createState() => _DirectionsWidgetState();
}

class _DirectionsWidgetState extends State<DirectionsWidget> {
  String _currentInstruction = "";
  String _currentInstructionType = "";
  List<dynamic> _allInstructions = [];
  List<NavigationInstruction> _navigationInstructions = [];
  int _currentInstructionIndex = 0;
  late String sessionToken;
  bool isLoading = true;
  bool isError = false;
  String _lastRideStatus = "";
  Timer? _fetchedInstructionUpdater;
  double _lastDistanceToInstruction = double.infinity;

  @override
  void initState() {
    sessionToken = const Uuid().v4();
    GlobalState.directionsUpdater = fetchDirections;
    _lastRideStatus = widget.onRideRequest?.status ?? "";
    super.initState();
  }

  @override
  void dispose() {
    _fetchedInstructionUpdater?.cancel();
    GlobalState.directionsUpdater = () {};
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant DirectionsWidget oldWidget) {
    if (widget.onRideRequest?.status != _lastRideStatus) {
      fetchDirections();
    }
    super.didUpdateWidget(oldWidget);
  }

  Future<void> fetchDirections() async {
    if (widget.onRideRequest == null || GlobalState.latestLocation == null) {
      return;
    }

    setState(() {
      isLoading = true;
      isError = false;
    });

    try {
      String endLongitude = widget.onRideRequest!.endLongitude!;
      String endLatitude = widget.onRideRequest!.endLatitude!;

      // Handle multiple stops - find the next unvisited stop
      if (widget.onRideRequest!.stops != null &&
          widget.onRideRequest!.stops!.isNotEmpty) {
        StopsModel? nextStop;
        for (var stop in widget.onRideRequest!.stops!) {
          if (stop.isArrived != true) {
            nextStop = stop;
            break;
          }
        }
        if (nextStop != null) {
          endLongitude = nextStop.stopLng.toString();
          endLatitude = nextStop.stopLat.toString();
        }
      }

      // Fetch directions from Mapbox API
      final url = 'https://api.mapbox.com/directions/v5/mapbox/driving/'
          '${GlobalState.latestLocation!.longitude},${GlobalState.latestLocation!.latitude};'
          '${endLongitude},${endLatitude}?'
          'geometries=geojson&overview=full&steps=true&'
          'access_token=${AppCred.mapBoxPublicTokenKey}';

      Response response = await buildHttpResponse(url, method: HttpMethod.GET);

      if (response.statusCode == 200 && response.body.isJson()) {
        final data = jsonDecode(response.body);

        if (data['routes'] == null || data['routes'].isEmpty) {
          setState(() {
            isError = true;
            isLoading = false;
          });
          return;
        }

        // Store raw instructions for backward compatibility
        _allInstructions = data['routes'][0]['legs'][0]['steps'];

        // Convert to our model class for better handling
        _navigationInstructions = [];
        for (var step in _allInstructions) {
          _navigationInstructions.add(NavigationInstruction.fromMapboxStep(step));
        }

        // Reset instruction index
        _currentInstructionIndex = 0;

        // Set initial instruction
        if (_navigationInstructions.isNotEmpty) {
          _currentInstruction = _navigationInstructions[0].instruction;
          _currentInstructionType = _navigationInstructions[0].type;
        }

        // Cancel existing timer and start a new one
        _fetchedInstructionUpdater?.cancel();
        _fetchedInstructionUpdater = Timer.periodic(
          const Duration(seconds: 2), // More frequent updates
          (timer) => _updateCurrentInstruction()
        );
      } else {
        setState(() {
          isError = true;
        });
      }
    } catch (e) {
      setState(() {
        isError = true;
      });
      print("Error fetching directions: $e");
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  void _updateCurrentInstruction() {
    if (GlobalState.latestLocation == null ||
        _navigationInstructions.isEmpty ||
        _currentInstructionIndex >= _navigationInstructions.length) {
      return;
    }

    // Check if ride is completed
    if (widget.onRideRequest?.status == REACHED ||
        widget.onRideRequest?.status == COMPLETED) {
      setState(() {
        _currentInstruction = "You have arrived at your destination.";
        _currentInstructionType = "arrive";
      });
      _fetchedInstructionUpdater?.cancel();
      return;
    }

    final currentInstruction = _navigationInstructions[_currentInstructionIndex];
    final currentLocation = LatLng(
      GlobalState.latestLocation!.latitude!,
      GlobalState.latestLocation!.longitude!,
    );

    // Calculate distance to current instruction point
    final distanceToInstruction = RideScreenUtils.getDistanceBetweenTwoPoints(
      startLatitude: currentLocation.latitude,
      startLongitude: currentLocation.longitude,
      endLatitude: currentInstruction.location.latitude,
      endLongitude: currentInstruction.location.longitude,
    );

    // Threshold for moving to next instruction (30 meters)
    const double completionThreshold = 30.0;

    // Check if we've passed the current instruction point
    if (distanceToInstruction < completionThreshold) {
      // Move to next instruction
      _currentInstructionIndex++;

      if (_currentInstructionIndex < _navigationInstructions.length) {
        // Update to next instruction
        final nextInstruction = _navigationInstructions[_currentInstructionIndex];
        setState(() {
          _currentInstruction = nextInstruction.instruction;
          _currentInstructionType = nextInstruction.type;
        });

        // Reset distance tracking
        _lastDistanceToInstruction = double.infinity;
      } else {
        // No more instructions - arrived at destination
        setState(() {
          _currentInstruction = "You have arrived at your destination.";
          _currentInstructionType = "arrive";
        });
        _fetchedInstructionUpdater?.cancel();
      }
    } else {
      // Update distance tracking for potential future enhancements
      _lastDistanceToInstruction = distanceToInstruction.toDouble();

      // Ensure current instruction is displayed (in case of state inconsistency)
      if (_currentInstruction != currentInstruction.instruction ||
          _currentInstructionType != currentInstruction.type) {
        setState(() {
          _currentInstruction = currentInstruction.instruction;
          _currentInstructionType = currentInstruction.type;
        });
      }
    }
  }

  /// Get advance notification for upcoming maneuvers
  String _getAdvanceNotification() {
    if (_currentInstructionIndex + 1 < _navigationInstructions.length) {
      final nextInstruction = _navigationInstructions[_currentInstructionIndex + 1];
      final currentInstruction = _navigationInstructions[_currentInstructionIndex];

      // Calculate distance to current instruction
      if (GlobalState.latestLocation != null) {
        final distanceToCurrentInstruction = RideScreenUtils.getDistanceBetweenTwoPoints(
          startLatitude: GlobalState.latestLocation!.latitude!,
          startLongitude: GlobalState.latestLocation!.longitude!,
          endLatitude: currentInstruction.location.latitude,
          endLongitude: currentInstruction.location.longitude,
        );

        // Show advance notification when within 200 meters of current instruction
        if (distanceToCurrentInstruction < 200) {
          return "Then ${nextInstruction.instruction.toLowerCase()}";
        }
      }
    }
    return "";
  }

  /// Get formatted instruction text with advance notification
  String _getFormattedInstruction() {
    String mainInstruction = _currentInstruction.isNotEmpty
        ? _currentInstruction
        : "Follow the route";

    String advanceNotification = _getAdvanceNotification();

    if (advanceNotification.isNotEmpty) {
      return "$mainInstruction\n$advanceNotification";
    }

    return mainInstruction;
  }

  IconData _getInstructionIcon(String instructionType) {
    switch (instructionType) {
      case "turn-right":
        return Icons.turn_right;
      case "turn-left":
        return Icons.turn_left;
      case "roundabout":
      case "roundabout-right":
        return Icons.roundabout_right;
      case "roundabout-left":
        return Icons.roundabout_left;
      case "fork-right":
        return Icons.fork_right;
      case "fork-left":
        return Icons.fork_left;
      case "depart":
        return Icons.departure_board;
      case "arrive":
        return Icons.location_on;
      case "straight":
      case "continue":
        return Icons.straight;
      case "slight-right":
        return Icons.turn_slight_right;
      case "slight-left":
        return Icons.turn_slight_left;
      case "sharp-right":
        return Icons.turn_sharp_right;
      case "sharp-left":
        return Icons.turn_sharp_left;
      case "uturn":
        return Icons.u_turn_right;
      case "merge":
        return Icons.merge;
      case "on-ramp":
        return Icons.ramp_right;
      case "off-ramp":
        return Icons.ramp_left;
      default:
        return Icons.drive_eta;
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget content;
    IconData leadingIcon = Icons.drive_eta; // Default icon

    if (widget.onRideRequest == null) {
      return const SizedBox();
    } else if (isLoading) {
      content = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: const [
          CircularProgressIndicator.adaptive(),
          SizedBox(width: 16),
          Text(
            "Getting directions...",
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      );
    } else if (isError) {
      leadingIcon = Icons.error_outline;
      content = Text(
        "Unable to get directions. Please check your connection.",
        style:
            TextStyle(fontSize: 16, color: Theme.of(context).colorScheme.error),
        textAlign: TextAlign.center,
      );
    } else {
      leadingIcon = _getInstructionIcon(_currentInstructionType);

      content = Row(
        children: [
          Icon(leadingIcon, size: 28, color: Theme.of(context).primaryColor),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _getFormattedInstruction(),
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
              softWrap: true,
            ),
          ),
        ],
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Card(
        elevation: 8,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        child: SizedBox(
          width: MediaQuery.sizeOf(context).width * 0.9,
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: content,
          ),
        ),
      ),
    );
  }
}

/// Model class to represent a navigation instruction
class NavigationInstruction {
  final String instruction;
  final String type;
  final LatLng location;
  final double distance;
  final double duration;
  final bool isCompleted;

  NavigationInstruction({
    required this.instruction,
    required this.type,
    required this.location,
    required this.distance,
    required this.duration,
    this.isCompleted = false,
  });

  factory NavigationInstruction.fromMapboxStep(Map<String, dynamic> step) {
    final maneuver = step['maneuver'];
    final location = maneuver['location'];

    return NavigationInstruction(
      instruction: maneuver['instruction'] ?? '',
      type: maneuver['type'] ?? '',
      location: LatLng(location[1], location[0]),
      distance: (step['distance'] ?? 0.0).toDouble(),
      duration: (step['duration'] ?? 0.0).toDouble(),
    );
  }

  NavigationInstruction copyWith({
    String? instruction,
    String? type,
    LatLng? location,
    double? distance,
    double? duration,
    bool? isCompleted,
  }) {
    return NavigationInstruction(
      instruction: instruction ?? this.instruction,
      type: type ?? this.type,
      location: location ?? this.location,
      distance: distance ?? this.distance,
      duration: duration ?? this.duration,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}
