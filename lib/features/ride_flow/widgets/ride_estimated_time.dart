import 'dart:async';

import 'package:flutter/material.dart';
import 'package:rooo_driver/network/RestApis.dart';

class RideEstimatedTime extends StatefulWidget {
  final int rideId;
  const RideEstimatedTime({super.key, required this.rideId});

  @override
  State<RideEstimatedTime> createState() => _RideEstimatedTimeState();
}

class _RideEstimatedTimeState extends State<RideEstimatedTime> {
  DriverEstimatedTimeData? data = DriverEstimatedTimeData(
    time: "11:00 AM",
    minutes: "8",
    distance: "6",
  );

  Timer? _timer;
  @override
  void initState() {
    _getTime();

    _timer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _getTime();
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 35,
      child: data == null
          ? Center(
              child: Text(
                "Updating ETA...",
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: _indicator(
                    value: data!.time,
                    title: "ETA",
                  ),
                ),
                Expanded(
                  child: _indicator(
                    value: data!.minutes,
                    title: "min",
                  ),
                ),
                Expanded(
                  child: _indicator(
                    value: data!.distance,
                    title: "km",
                  ),
                ),
              ],
            ),
    );
  }

  Widget _indicator({
    required String value,
    required String title,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          title,
          style: const TextStyle(
            fontSize: 10,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _getTime() async {
    setState(() {
      data = null;
    });
    var response = await getDriverEstimatedTime(
      rideId: widget.rideId,
    );
    if (response.status) {
      setState(() {
        data = response.data;
      });
    }
  }
}

class DriverEstimatedTimeResponse {
  bool status;
  String message;
  DriverEstimatedTimeData? data;

  DriverEstimatedTimeResponse({
    required this.status,
    required this.message,
    required this.data,
  });

  factory DriverEstimatedTimeResponse.fromMap(Map<String, dynamic> map) {
    return DriverEstimatedTimeResponse(
      status: map['status'] as bool,
      message: map['message'] as String,
      data: map['data'] != null
          ? DriverEstimatedTimeData.fromMap(map['data'] as Map<String, dynamic>)
          : null,
    );
  }
}

class DriverEstimatedTimeData {
  String time;
  String minutes;
  String distance;

  DriverEstimatedTimeData({
    required this.time,
    required this.minutes,
    required this.distance,
  });

  factory DriverEstimatedTimeData.fromMap(Map<String, dynamic> map) {
    return DriverEstimatedTimeData(
      time: map['arrival_time'] as String,
      minutes: map['duration_minutes'] as String,
      distance: map['distance_km'] as String,
    );
  }
}
