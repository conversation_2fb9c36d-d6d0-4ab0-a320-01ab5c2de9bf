import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:rooo_driver/features/care/screens/add_care_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/empty_widget.dart';
import 'package:rooo_driver/screens/ride_history_details_screen.dart';
import 'package:rooo_driver/utils/Extensions/Loader.dart';

class RideHistoryCompleted extends StatefulWidget {
  final String? status;
  const RideHistoryCompleted({super.key, required this.status});

  @override
  State<RideHistoryCompleted> createState() => _RideHistoryCompletedState();
}

class _RideHistoryCompletedState extends State<RideHistoryCompleted> {
  List<OnRideRequest> ride_history_list = [];
  int currentPage = 1;
  int totalPage = 1;
  String? api_message;

  void getRideHistoryCompletedList() async {
    setState(() {
      appStore.setLoading(true);
    });
    await getRideHistoryApi(
          status: widget.status.toString(),
          driver_id: sharedPref.getInt(USER_ID) ?? -1,
        )
        .then((value) {
          setState(() {
            appStore.setLoading(false);
          });
          if (value.data != null) {
            ride_history_list = value.data!;
            api_message = value.message;
          }

          setState(() {
            currentPage = value.pagination!.currentPage!;
          });
        })
        .catchError((error) {
          setState(() {
            appStore.setLoading(false);
          });
        });
  }

  @override
  void initState() {
    getRideHistoryCompletedList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Padding(
          padding: screenPadding,
          child: ListView.separated(
            shrinkWrap: true,
            itemBuilder: (context, index) {
              return InkWell(
                onTap: () {
                  GlobalMethods.pushScreen(
                    context: context,
                    screen: RideHistoryDetailScreen(
                      id: ride_history_list[index].id ?? 0,
                    ),
                    screenIdentifier: ScreenIdentifier.RideHistoryDetailScreen,
                  );
                },
                child: Card(
                  elevation: 2,
                  
                  child: Container(
                    padding: screenPadding,
                    child: Column(
                      children: [
                        HeaderkeyValue(
                          key: "Source",
                          value: ride_history_list[index].startAddress
                              .toString(),
                        ),
                        height10,
                        HeaderkeyValue(
                          key: "Destination",
                          value: ride_history_list[index].endAddress.toString(),
                        ),
                        height10,
                        Divider(thickness: 1),

                        keyValue(
                          key: "Ride Id",
                          value: ride_history_list[index].id.toString(),
                        ),
                        height10,

                        keyValue(
                          key: language.TimeTxt,
                          value: formatDate(
                            ride_history_list[index].datetime!,
                            showTime: true,
                          ),
                        ),

                        height10,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: CustomText(
                                data: language.statusTxt,
                                size: 12,
                              ),
                            ),
                            Expanded(
                              child: Container(
                                alignment: Alignment.center,
                                padding: EdgeInsets.all(5),
                                color: widget.status == "canceled"
                                    ? Colors.red
                                    : Colors.green,
                                child: CustomText(
                                  color: Colors.white,
                                  data: widget.status == "canceled"
                                      ? language.cancelled
                                      : language.completedTxt,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
            separatorBuilder: (context, index) => height10,
            itemCount: ride_history_list.length,
          ),
        ),
        Observer(
          builder: (context) {
            return Visibility(
              visible: !appStore.isLoading && ride_history_list.isEmpty,
              child: RooEmptyWidegt(title: "No data"),
            );
          },
        ),
        Observer(
          builder: (context) {
            return Visibility(visible: appStore.isLoading, child: Loader());
          },
        ),
      ],
    );
  }

  HeaderkeyValue({required String key, required String value}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // Text(key,style: AppTextStyles.title(),),
        Expanded(
          child: Text(
            key,
            style: AppTextStyles.title(),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: AppTextStyles.subtitle(),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  keyValue({required String key, required String value}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(child: CustomText(data: key, size: 12)),
          Expanded(
            child: CustomText(
              size: 12,
              data: value,
              fontweight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
