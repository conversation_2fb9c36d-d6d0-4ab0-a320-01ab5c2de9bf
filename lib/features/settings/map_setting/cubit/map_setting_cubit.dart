import 'package:rooo_driver/global/export/app_export.dart';

abstract class MapSettingState {}

class MapSettingInitState extends MapSettingState {}

class MapSettingLoadingState extends MapSettingState {}

class MapSettingDeletedState extends MapSettingState {}

class MapSettingActivatedState extends MapSettingState {}

class MapSettingDetailLoadedState extends MapSettingState {
  final String message;

  MapSettingDetailLoadedState({required this.message});
}

class MapSettingSavedState extends MapSettingState {}

class LoginSettingSavedState extends MapSettingState {}

class MapSettingDeleteState extends MapSettingState {}

class MapSettingDetailLoaded extends MapSettingState {
  final String data;

  MapSettingDetailLoaded({required this.data});
}

class MapSettingLoadedState extends MapSettingState {
  final MapSettingResponseModel mapSettingResponseModel;

  MapSettingLoadedState({required this.mapSettingResponseModel});
}
// class MapSettingTypeLoadedState extends MapSettingState {
//   final MapSettingTypeResponseModel map_settingResponseModel;

//   MapSettingTypeLoadedState({
//     required this.map_settingResponseModel,
//   });
// }

class MapSettingErrorState extends MapSettingState {
  final String message;
  MapSettingErrorState({required this.message});
}

class MapSettingCubit extends Cubit<MapSettingState> {
  MapSettingCubit() : super(MapSettingInitState());

  MapSettingsRepository map_settingRepository = MapSettingsRepository();

  // void deleteMapSetting({required int map_settingId}) async {
  //   emit(MapSettingLoadingState());
  //   await map_settingRepository
  //       .deleteMapSettingApi(map_settingId: map_settingId)
  //       .then((value) {
  //     if (value.status) {
  //         emit(MapSettingDeletedState());

  //     } else {
  //       emit(MapSettingErrorState(message: value.message));
  //     }
  //   }).onError((error, stackTrace) {
  //     emit(MapSettingErrorState(message: "Server error"));
  //   });
  // }
  //  void activateMapSetting({required int map_settingId}) async {
  //   emit(MapSettingLoadingState());
  //   await map_settingRepository
  //       .activeMapSettingApi(map_settingId: map_settingId)
  //       .then((value) {
  //     if (value.status) {
  //         emit(MapSettingActivatedState());

  //     } else {
  //       emit(MapSettingErrorState(message: value.message));
  //     }
  //   }).onError((error, stackTrace) {
  //     emit(MapSettingErrorState(message: "Server error"));
  //   });
  // }

  Future<void> getMapSetting({required int userId}) async {
    emit(MapSettingLoadingState());
    await map_settingRepository
        .getMapSettingListApi(userId: userId)
        .then((value) {
          if (value.status) {
            if (value.data != null) {
              GlobalState.selectedMap = value.data!.selected_map;
              emit(MapSettingLoadedState(mapSettingResponseModel: value));
            } else {
              emit(MapSettingErrorState(message: serverErrorMessage));
            }
          } else {
            emit(MapSettingErrorState(message: value.message));
          }
        })
        .onError((error, stackTrace) {
          emit(MapSettingErrorState(message: "Server error"));
        });
  }

  //  void getMapSettingType({required int regionId}) async {
  //   emit(MapSettingLoadingState());
  //   await map_settingRepository
  //       .getMapSettingTypeListApi(regionId:regionId )
  //       .then((value) {
  //     if (value.status) {
  //       if (value.data != null) {
  //         emit(MapSettingTypeLoadedState(map_settingResponseModel: value));
  //       } else {
  //         emit(MapSettingErrorState());
  //       }
  //     } else {
  //       emit(MapSettingErrorState(message: value.message));
  //     }
  //   }).onError((error, stackTrace) {
  //     emit(MapSettingErrorState(message: "Server error"));
  //   });
  // }

  void saveMapSetting({required Map<String, dynamic> request}) async {
    emit(MapSettingLoadingState());
    await map_settingRepository
        .saveMapSettingApi(request: request)
        .then((value) {
          if (value.status) {
            emit(MapSettingSavedState());
          } else {
            emit(MapSettingErrorState(message: value.message));
          }
        })
        .onError((error, stackTrace) {
          emit(MapSettingErrorState(message: "Server error"));
        });
  }

  void saveLoginSetting({required Map<String, dynamic> request}) async {
    emit(MapSettingLoadingState());
    await map_settingRepository
        .saveMapSettingApi(request: request)
        .then((value) {
          if (value.status) {
            emit(LoginSettingSavedState());
          } else {
            emit(MapSettingErrorState(message: value.message));
          }
        })
        .onError((error, stackTrace) {
          emit(MapSettingErrorState(message: "Server error"));
        });
  }

  //    getMapSettingDetailsMapSetting({required int id}) async {    emit(MapSettingLoadingState());
  //   await MapSettingRepository.getMapSettingDetailsApi(id: id).then((value) {
  //     emit(MapSettingDetailLoaded(data: value.description.toString()));
  //   }).onError((error, stackTrace) {
  //     emit(MapSettingErrorState(error_message: "Server error"));

  //   });
  // }
}
