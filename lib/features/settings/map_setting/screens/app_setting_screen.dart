
// import 'package:map_launcher/map_launcher.dart';

import 'package:rooo_driver/components/setting_black_container.dart';
import 'package:rooo_driver/features/emergency_contacts/screens/emergency_contact_screen.dart';
import 'package:rooo_driver/features/home_route_rides/screens/home_route_screen.dart';
import 'package:rooo_driver/features/push_notification_pref/screens/push_notification_screen.dart';
import 'package:rooo_driver/features/settings/map_setting/screens/dark_mode_screen.dart';
import 'package:rooo_driver/features/settings/map_setting/screens/map_setting_screen.dart';
import 'package:rooo_driver/features/settings/map_setting/screens/password_setting_screen.dart';
import 'package:rooo_driver/features/verify_otp/screens/change_password_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';


class AppSettingScreen extends StatefulWidget {
  final MapSettingModel? vehicleModel;
  const AppSettingScreen({super.key, this.vehicleModel});

  @override
  State<AppSettingScreen> createState() => _AppSettingScreenState();
}

class _AppSettingScreenState extends State<AppSettingScreen> {

  // String ?_selectedMap ;



  // ValueNotifier<MapSettingTypeModel> _isSelectedService =
  //     ValueNotifier(MapSettingTypeModel(id: -1, service_image: "", name: ""));

  // List<MapSettingTypeModel> _vehicleTypeList = [];


 


  _onPullToRefresh() {
    _init();
  }

  _init() async {
    // if (widget.vehicleModel != null) {
    //   _nameController.text = widget.vehicleModel!.name!;
    //   _transmissionController.text = widget.vehicleModel!.transmission;
    //   _plateNumberController.text = widget.vehicleModel!.transmission;
    //   _isSelectedService.value.id = widget.vehicleModel!.serviceId;
    // }
  }

  _dispose() {
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return  Scaffold(
            appBar: RoooAppbar(title: "App Settings"),
            body: SingleChildScrollView(
                  child: Column(
                    children: [
                      SettingsBlackContainer(
                          icon: Icon(Icons.location_on),
                          title: "Route match",
                          onTap: () {
                            GlobalMethods.pushScreen(
                                context: context,
                                screen: HomeRouteScreen(),
                                screenIdentifier:
                                    ScreenIdentifier.HomeRouteScreen);

                            // launchScreen(context, TermsAndConditionsScreen(),
                            //     pageRouteAnimation: PageRouteAnimation.Slide);
                          }),
                      SettingsBlackContainer(
                          icon: Icon(Icons.map),
                          title: "Map settings",
                          onTap: () {
                            GlobalMethods.pushScreen(
                                screen: MapSettingScreen(),
                                context: context,
                                screenIdentifier:
                                    ScreenIdentifier.MapSettingScreen);

                            // launchScreen(context, TermsAndConditionsScreen(),
                            //     pageRouteAnimation: PageRouteAnimation.Slide);
                          }),
                      SettingsBlackContainer(
                          icon: Icon(Icons.password),
                          title: "Password settings",
                          onTap: () {
                            GlobalMethods.pushScreen(
                                context: context,
                                screen: PasswordSettingScreen(),
                                screenIdentifier:
                                    ScreenIdentifier.PasswordSettingScreen);

                            // launchScreen(context, TermsAndConditionsScreen(),
                            //     pageRouteAnimation: PageRouteAnimation.Slide);
                          }),
                             SettingsBlackContainer(
                          icon: Icon(Icons.password),
                          title: "Change password",
                          onTap: () {
                            GlobalMethods.pushScreen(
                                context: context,
                                screen: NewChangePasswordScreen(),
                                screenIdentifier:
                                    ScreenIdentifier.NewChangePasswordScreen);

                            // launchScreen(context, TermsAndConditionsScreen(),
                            //     pageRouteAnimation: PageRouteAnimation.Slide);
                          }),
                      SettingsBlackContainer(
                          icon: Icon(Icons.notification_important_sharp),
                          title: "Push notifications",
                          onTap: () {
                            GlobalMethods.pushScreen(
                                context: context,
                                screen: PushNotificationPrefScreeen(),
                                screenIdentifier: ScreenIdentifier
                                    .PushNotificationPrefScreeen);

                            // launchScreen(context, TermsAndConditionsScreen(),
                            //     pageRouteAnimation: PageRouteAnimation.Slide);
                          }),
                               SettingsBlackContainer(
                          icon: Icon(Icons.safety_check),
                          title: "Emergency contacts",
                          onTap: () {
                            GlobalMethods.pushScreen(
                                context: context,
                                screen: EmergencyContactScreen(),
                                screenIdentifier: ScreenIdentifier
                                    .EmergencyContactScreen);

                            // launchScreen(context, TermsAndConditionsScreen(),
                            //     pageRouteAnimation: PageRouteAnimation.Slide);
                          }),
                          //          SettingsBlackContainer(
                          // icon: Icon(Icons.dark_mode),
                          // title: "Dark mode",
                          // onTap: () {
                          //   GlobalMethods.pushScreen(
                          //       context: context,
                          //       screen: DarkModeScreen(),
                          //       screenIdentifier: ScreenIdentifier
                          //           .DarkModeScreen);

                          //   // launchScreen(context, TermsAndConditionsScreen(),
                          //   //     pageRouteAnimation: PageRouteAnimation.Slide);
                          // }),
                    ],
                  ),
                ));
}

//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////
}