// import 'package:map_launcher/map_launcher.dart';
import 'package:map_launcher/map_launcher.dart' as map_launcher;

import 'package:rooo_driver/features/settings/map_setting/cubit/map_setting_cubit.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';

class MapSettingScreen extends StatefulWidget {
  final MapSettingModel? vehicleModel;
  const MapSettingScreen({super.key, this.vehicleModel});

  @override
  State<MapSettingScreen> createState() => _MapSettingScreenState();
}

class _MapSettingScreenState extends State<MapSettingScreen> {
  map_launcher.AvailableMap? _selectedMap;

  List<map_launcher.AvailableMap> _availableMaps = [];
  

  _saveMapSeting() {
    Map<String, dynamic> request = {"selected_map": _selectedMap!.mapType.name};
    BlocProvider.of<MapSettingCubit>(context).saveMapSetting(request: request);
  }

  _getMapSetting() {
    BlocProvider.of<MapSettingCubit>(
      context,
    ).getMapSetting(userId: sharedPref.getInt(USER_ID)!);
  }

  _onDataLoaded({required MapSettingResponseModel data}) async {
    if (data.data != null) {
      GlobalState.selectedMap = data.data!.selected_map;
    }

    if (GlobalState.selectedMap != null &&
        (AppOfferedMaps
                .where(
                  (element) => element.mapType.name == GlobalState.selectedMap,
                )
                .isEmpty ||
            _availableMaps
                .where(
                  (element) => element.mapType.name == GlobalState.selectedMap,
                )
                .isEmpty)) {
      GlobalMethods.showInfoDialogNew(
        context: context,
        onClick: () {
          Navigator.of(context).pop();
        },
        title:
            "The map you previously selected is not available on this device. Please update your default map.",
      );
    }
  }

  _init() async {
    map_launcher.MapLauncher.installedMaps.then((installedMaps) {
      setState(() {
        _availableMaps = installedMaps;
      });
    });
    _getMapSetting();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<MapSettingCubit, MapSettingState>(
      listener: (context, state) {
        if (state is MapSettingLoadedState) {
          _onDataLoaded(data: state.mapSettingResponseModel);
        }
        if (state is MapSettingErrorState) {
          GlobalMethods.errorToast(context, state.message);
        } else if (state is MapSettingSavedState) {
          GlobalState.selectedMap = _selectedMap!.mapType.name;
          _getMapSetting();
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: RoooAppbar(title: "Map Settings"),
          body: ScreenBody(
            isLoading: state is MapSettingLoadingState,
            isEmpty: false,
            emptyMessage: "No data",
            child: ListView.separated(
              separatorBuilder: (context, index) => const Divider(),
              shrinkWrap: true,
              itemCount: AppOfferedMaps.length,
              itemBuilder: (context, index) {
                map_launcher.AvailableMap mapToRender = AppOfferedMaps[index];

                bool isAvailable = _availableMaps.any(
                  (element) => element.mapType.name == mapToRender.mapType.name,
                );

                return Row(
                  children: [
                    Expanded(
                      child: RadioListTile<String>(
                        title: Text(mapToRender.mapName),
                        value: mapToRender.mapType.name,
                        groupValue: GlobalState.selectedMap,
                        secondary: isAvailable
                            ? Text(
                                "Available",
                                style: AppTextStyles.text(
                                  color: AppColors.greenColor,
                                ),
                              )
                            : Text(
                                "Not Available",
                                style: AppTextStyles.text(color: Colors.red),
                              ),
                        onChanged: (value) {
                          if (!isAvailable) {
                            return;
                          }
                          _selectedMap = mapToRender;
                          _saveMapSeting();
                        },
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        );
      },
    );
  }
}

//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////
class MyModel {
  String title;
  String type;

  MyModel({required this.title, required this.type});

  // Factory method to create a model from a JSON object
  factory MyModel.fromJson(Map<String, dynamic> json) {
    return MyModel(title: json['title'], type: json['type']);
  }

  // Method to convert a model instance to a JSON object
  Map<String, dynamic> toJson() {
    return {'title': title, 'type': type};
  }
}
