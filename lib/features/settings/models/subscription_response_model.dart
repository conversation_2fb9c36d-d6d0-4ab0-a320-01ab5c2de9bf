import 'package:rooo_driver/global/models/response_model.dart';

class SubscriptionResponseModel extends ResponseModel<String> {
  SubscriptionResponseModel({
    required bool status,
    required String message,
    required String? data,
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new SubscriptionResponseModel instance from a map.
  factory SubscriptionResponseModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionResponseModel(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] as String?,
    );
  }

  // Method to convert a SubscriptionResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data,
    };
  }
}
