import 'package:rooo_driver/features/vehicles/models/driver_vehicle_model.dart';
import 'package:rooo_driver/features/vehicles/models/vehicle_brand_model.dart';
import 'package:rooo_driver/features/vehicles/models/vehicle_model_model.dart';
import 'package:rooo_driver/features/vehicles/screens/vehicle_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/utils/Extensions/app_textfield.dart';

class AddVehiclesScreen extends StatefulWidget {
  final DriverVehicleModel? vehicleModel;
  const AddVehiclesScreen({super.key, this.vehicleModel});

  @override
  State<AddVehiclesScreen> createState() => _AddVehiclesScreenState();
}

class _AddVehiclesScreenState extends State<AddVehiclesScreen> {
  ValueNotifier<int> _selectedVehicleBrandId = ValueNotifier(-1);
  // ValueNotifier<int> _selectedVehicleModelId = ValueNotifier(-1);

  ValueNotifier<VehicleModelModel> _selectedVehicleModel = ValueNotifier(
    VehicleModelModel(id: -1, seat: -1, name: "", makeId: -1),
  );

  List<VehicleBrandModel> _vehicleBrandList = [];
  List<VehicleBrandModel> _filteredVehicleBrandList = [];
  List<VehicleModelModel> _vehicleModelList = [];
  List<VehicleModelModel> _filteredVehicleModelList = [];

  // ValueNotifier<VehicleTypeModel> _isSelectedService =
  //     ValueNotifier(VehicleTypeModel(id: -1, service_image: "", name: ""));

  ValueNotifier<String> _selected_transmission = ValueNotifier("Automatic");
  List<String> _tranmission_list = ["Automatic", "Electrical", "Manual"];

  ValueNotifier<int> _selectedProductionYear = ValueNotifier(
    DateTime.now().year,
  );
  List<int> _productionYearList = List.generate(8, (v) {
    return (DateTime.now().year - v);
  });

  // List<VehicleTypeModel> _vehicleTypeList = [];
  String _emptyMesssage = "";

  // TextEditingController _nameController = TextEditingController();
  // TextEditingController _transmissionController = TextEditingController();
  TextEditingController _plateNumberController = TextEditingController();
  TextEditingController _vehicleBrandSearchController = TextEditingController();
  TextEditingController _vehicleModelSearchController = TextEditingController();
  FocusNode _vehicleBrandSearchFocusNode = FocusNode();
  FocusNode _vehicleModelSearchFocusNode = FocusNode();

  ValueNotifier<File> _vehicleImage = ValueNotifier(File("path"));
  ValueNotifier<File> _vehicleInspectionReportImage = ValueNotifier(
    File("path"),
  );

  ValueNotifier<File> _vehicleRegistrationImage = ValueNotifier(File("path"));
  ValueNotifier<bool> _isDashCamAvailable = ValueNotifier(false);

  _saveVehicle({required MultipartRequest multiPartRequest}) {
    BlocProvider.of<VehicleCubit>(
      context,
    ).saveVehicle(multiPartRequest: multiPartRequest);
  }

  _getVehicleBrandList() {
    BlocProvider.of<VehicleCubit>(context).getVehicleBrandList();
  }

  _getVehicleModelList({required int vehicleBrandList}) {
    BlocProvider.of<VehicleCubit>(
      context,
    ).getVehicleModelList(vehicle_brand_id: vehicleBrandList);
  }

  _onPullToRefresh() {
    _init();
  }

  _init() {
    if (widget.vehicleModel != null) {
      _registratonExpiryDate = widget.vehicleModel!.registrationDocExpirayDate;
      _inspectionExpiryDate = widget.vehicleModel!.inspectionDocExpirayDate;

      _selected_transmission.value =
          widget.vehicleModel?.transmission ?? _tranmission_list[0];
      _plateNumberController.text = widget.vehicleModel!.plateNumber;
      _selectedProductionYear.value =
          (widget.vehicleModel?.productionYear ?? DateTime.now().year);

      _selectedVehicleBrandId.value = widget.vehicleModel?.make_id ?? -1;

      _selectedVehicleModel.value = VehicleModelModel(
        id: widget.vehicleModel!.model_id,
        seat: widget.vehicleModel!.seats,
        name: widget.vehicleModel!.model_name,
        makeId: widget.vehicleModel!.make_id,
      );
      // _isSelectedService.value.id=widget.vehicleModel!.serviceId;
      if (widget.vehicleModel!.isDashCamAvailable == "true") {
        _isDashCamAvailable.value = true;
      } else {
        _isDashCamAvailable.value = false;
      }
      ;
    }

    _getVehicleBrandList();
  }

  _dispose() {
    _vehicleBrandSearchController.dispose();
    _vehicleBrandSearchFocusNode.dispose();
    _vehicleModelSearchController.dispose();
    _vehicleModelSearchFocusNode.dispose();
  }

  @override
  void initState() {
    super.initState();
    _init();

    _vehicleBrandSearchFocusNode.addListener(() {
      if (!_vehicleBrandSearchFocusNode.hasFocus) {
        setState(() {
          _filteredVehicleBrandList = [];
        });
      } else {
        Scrollable.ensureVisible(
          _vehicleBrandSearchFocusNode.context!,
          alignment: 0.1,
          duration: Duration(milliseconds: 300),
        );

        if (_selectedVehicleBrandId.value == -1) {
          _filteredVehicleBrandList = _getFilteredVehicleBrandList("");
          setState(() {});
        }
      }
    });

    _vehicleModelSearchFocusNode.addListener(() {
      if (!_vehicleModelSearchFocusNode.hasFocus) {
        setState(() {
          _filteredVehicleModelList = [];
        });
      } else {
        Scrollable.ensureVisible(
          _vehicleModelSearchFocusNode.context!,
          alignment: 0.1,
          duration: Duration(milliseconds: 300),
        );

        if (_selectedVehicleModel.value.id == -1) {
          _filteredVehicleModelList = _getFilteredVehicleModelList("");
          setState(() {});
        }
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<VehicleCubit, VehicleState>(
      listener: (context, state) {
        if (state is VehicleBrandLoadedState) {
          _vehicleBrandList = state.vehicleBrandList;
          if (_selectedVehicleBrandId.value == -1) {
            _selectedVehicleBrandId.value = _vehicleBrandList[0].id;
            // Set the search controller text to the selected brand name
            VehicleBrandModel selectedBrand = _vehicleBrandList.firstWhere(
              (brand) => brand.id == _selectedVehicleBrandId.value,
              orElse: () => _vehicleBrandList[0],
            );
            _vehicleBrandSearchController.text = selectedBrand.name;
          } else if (widget.vehicleModel != null) {
            // If editing an existing vehicle, set the search controller text to the selected brand name
            VehicleBrandModel selectedBrand = _vehicleBrandList.firstWhere(
              (brand) => brand.id == _selectedVehicleBrandId.value,
              orElse: () => _vehicleBrandList[0],
            );
            _vehicleBrandSearchController.text = selectedBrand.name;
          }
          _getVehicleModelList(vehicleBrandList: _selectedVehicleBrandId.value);
        } else if (state is VehicleModelLoadedState) {
          _vehicleModelList = state.vehicleModelList;

          if (_selectedVehicleModel.value.id == -1) {
            _selectedVehicleModel.value = _vehicleModelList[0];
            _vehicleModelSearchController.text = _vehicleModelList[0].name;
          } else if (widget.vehicleModel != null) {
            VehicleModelModel selectedModel = _vehicleModelList.firstWhere(
              (model) => model.id == _selectedVehicleModel.value.id,
              orElse: () => _vehicleModelList[0],
            );
            _vehicleModelSearchController.text = selectedModel.name;
          }
        }
        if (state is VehicleErrorState) {
          GlobalMethods.errorToast(
            context,
            state.message ?? "Something went wrong",
          );
        } else if (state is VehicleSavedState) {
          Navigator.pop(context, true);
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: RoooAppbar(
            title: widget.vehicleModel == null ? "Add Vehicle" : "Edit Vehicle",
          ),
          body: ScreenBody(
            onPullToRefresh: () async => await _onPullToRefresh(),
            isLoading: state is VehicleLoadingState,
            isEmpty: false,
            emptyMessage: _emptyMesssage,
            child: Padding(
              padding: screenPadding,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ValueListenableBuilder<int>(
                      valueListenable: _selectedVehicleBrandId,
                      builder: (context, value, child) {
                        if (_vehicleBrandList.isEmpty) {
                          return SizedBox();
                        }
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Make", style: AppTextStyles.title()),
                            height3,
                            TextFormField(
                              focusNode: _vehicleBrandSearchFocusNode,
                              decoration: InputDecoration(
                                hintText: "Type to search...",
                                suffixIcon: Icon(Icons.search),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide(
                                    width: 1,
                                    color: AppColors.primaryColor(context),
                                  ),
                                ),
                              ),
                              controller: _vehicleBrandSearchController,
                              onChanged: (value) {
                                _filteredVehicleBrandList =
                                    _getFilteredVehicleBrandList(value);
                                if (_filteredVehicleBrandList.isEmpty) {
                                  _filteredVehicleBrandList.add(
                                    VehicleBrandModel(
                                      id: -1,
                                      name: "No make found",
                                      status: "",
                                    ),
                                  );
                                }
                                setState(() {});
                              },
                            ),
                            Card(
                              child: Container(
                                color:
                                    Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? Colors.grey.shade400
                                    : Colors.grey.shade100,
                                height: _filteredVehicleBrandList.length > 0
                                    ? 200
                                    : 0,
                                child: ListView.separated(
                                  itemBuilder: (context, index) {
                                    return ListTile(
                                      title: Text(
                                        _filteredVehicleBrandList[index].name,
                                        style: TextStyle(color: Colors.black),
                                      ),
                                      onTap: () {
                                        if (_filteredVehicleBrandList[index]
                                                .id ==
                                            -1) {
                                          return;
                                        }
                                        _selectedVehicleBrandId.value =
                                            _filteredVehicleBrandList[index].id;
                                        _vehicleBrandSearchController.text =
                                            _filteredVehicleBrandList[index]
                                                .name;
                                        _selectedVehicleModel.value.id = -1;
                                        _getVehicleModelList(
                                          vehicleBrandList:
                                              _selectedVehicleBrandId.value,
                                        );
                                        setState(() {
                                          _filteredVehicleBrandList = [];
                                        });
                                      },
                                    );
                                  },
                                  separatorBuilder: (context, index) =>
                                      const Divider(),
                                  itemCount: _filteredVehicleBrandList.length,
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                    height10,

                    ValueListenableBuilder<VehicleModelModel>(
                      valueListenable: _selectedVehicleModel,
                      builder: (context, vehicle_value, child) {
                        if (_vehicleModelList.isEmpty ||
                            _vehicleBrandList.isEmpty) {
                          return SizedBox();
                        }
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Model", style: AppTextStyles.title()),
                            height3,
                            TextFormField(
                              focusNode: _vehicleModelSearchFocusNode,
                              decoration: InputDecoration(
                                hintText: "Type to search...",
                                suffixIcon: Icon(Icons.search),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide(
                                    width: 1,
                                    color: AppColors.primaryColor(context),
                                  ),
                                ),
                              ),
                              controller: _vehicleModelSearchController,
                              onChanged: (value) {
                                _filteredVehicleModelList =
                                    _getFilteredVehicleModelList(value);
                                if (_filteredVehicleModelList.isEmpty) {
                                  _filteredVehicleModelList.add(
                                    VehicleModelModel(
                                      id: -1,
                                      seat: 0,
                                      name: "No model found",
                                      makeId: _selectedVehicleBrandId.value,
                                    ),
                                  );
                                }
                                setState(() {});
                              },
                            ),
                            Card(
                              child: Container(
                                color:
                                    Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? Colors.grey.shade400
                                    : Colors.grey.shade100,
                                height: _filteredVehicleModelList.length > 0
                                    ? 200
                                    : 0,
                                child: ListView.separated(
                                  itemBuilder: (context, index) {
                                    return ListTile(
                                      title: Text(
                                        _filteredVehicleModelList[index].name,
                                        style: TextStyle(color: Colors.black),
                                      ),
                                      subtitle: Text(
                                        _filteredVehicleModelList[index].seat
                                                .toString() +
                                            " seater",
                                        style: TextStyle(color: Colors.black),
                                      ),
                                      onTap: () {
                                        if (_filteredVehicleModelList[index]
                                                .id ==
                                            -1) {
                                          return;
                                        }
                                        setState(() {
                                          _selectedVehicleModel.value =
                                              _filteredVehicleModelList[index];
                                          _vehicleModelSearchController.text =
                                              _filteredVehicleModelList[index]
                                                  .name;
                                          _filteredVehicleModelList = [];
                                        });
                                      },
                                    );
                                  },
                                  separatorBuilder: (context, index) =>
                                      const Divider(),
                                  itemCount: _filteredVehicleModelList.length,
                                ),
                              ),
                            ),
                            height10,
                          ],
                        );
                      },
                    ),

                    ValueListenableBuilder<String>(
                      valueListenable: _selected_transmission,
                      builder: (context, value, child) {
                        // if (_tranmission_list.isEmpty) {
                        //   return SizedBox();
                        // }
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Transmission", style: AppTextStyles.title()),
                            height3,
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                // color: Colors.grey,
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  width: 1,
                                  color: AppColors.primaryColor(
                                    context,
                                  ).withOpacity(.6),
                                ),
                              ),
                              child: DropdownButtonHideUnderline(
                                child: DropdownButton<String?>(
                                  isExpanded: true,
                                  value: value.toLowerCase(),
                                  items: _tranmission_list.map((e) {
                                    return DropdownMenuItem<String>(
                                      value: e.toLowerCase(),
                                      child: Text(e.toString()),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    _selected_transmission.value = value!
                                        .toLowerCase();
                                  },
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                    height10,
                    ValueListenableBuilder<int>(
                      valueListenable: _selectedProductionYear,
                      builder: (context, value, child) {
                        // if (_tranmission_list.isEmpty) {
                        //   return SizedBox();
                        // }
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Production year",
                              style: AppTextStyles.title(),
                            ),
                            height3,
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                // color: Colors.grey,
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  width: 1,
                                  color: AppColors.primaryColor(
                                    context,
                                  ).withOpacity(.6),
                                ),
                              ),
                              child: DropdownButtonHideUnderline(
                                child: DropdownButton<int?>(
                                  isExpanded: true,
                                  value: value,
                                  items: _productionYearList.map((e) {
                                    return DropdownMenuItem<int>(
                                      value: e,
                                      child: Text(e.toString()),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    _selectedProductionYear.value = value!;
                                  },
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                    height10,
                    // Text(
                    //   "Name",
                    //   style: AppTextStyles.title(),
                    // ),
                    // TextFormField(
                    //   controller: _nameController,
                    // ),
                    // height20,
                    Text("Plate number", style: AppTextStyles.title()),
                    height3,

                    TextFormField(
                      controller: _plateNumberController,
                      maxLength: 9,
                      inputFormatters: [
                        TextInputFormatter.withFunction((oldValue, newValue) {
                          return TextEditingValue(
                            text: newValue.text.toUpperCase(),
                          );
                        }),
                      ],
                    ),

                    height10,
                    Divider(),
                    height10,

                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Is Dashcam Available?",
                          style: AppTextStyles.title(),
                        ),
                        ValueListenableBuilder<bool>(
                          valueListenable: _isDashCamAvailable,
                          builder: (context, value, child) {
                            return Switch(
                              value: value,
                              onChanged: (val) {
                                _isDashCamAvailable.value = val;
                              },
                            );
                          },
                        ),
                      ],
                    ),
                    height20,
                    Divider(),
                    Text(
                      "Documents",
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    height20,
                    Container(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey.shade400
                          : Colors.grey.shade100,
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Vehicle registration image",
                                      style: AppTextStyles.title(
                                        color: Colors.black,
                                      ),
                                    ),
                                    getStatus(
                                      status: widget
                                          .vehicleModel
                                          ?.vehicleRegistrationStatus,
                                    ),
                                    widget
                                                .vehicleModel
                                                ?.vehicleRegistrationRejectionText ==
                                            null
                                        ? SizedBox()
                                        : Text(
                                            widget
                                                    .vehicleModel
                                                    ?.vehicleRegistrationRejectionText ??
                                                "",
                                            style: TextStyle(
                                              color: Colors.black,
                                            ),
                                          ),
                                  ],
                                ),
                              ),
                              width20,
                              InkWell(
                                onTap: () {
                                  GlobalMethods.checkCameraPermission(
                                    onSuccess: () async {
                                      if (widget
                                              .vehicleModel
                                              ?.vehicleRegistrationStatus !=
                                          "1") {
                                        showCameraGalleryBottomSheet(
                                          _vehicleRegistrationImage,
                                        );
                                      }
                                    },
                                    context: context,
                                  );
                                },
                                child: ValueListenableBuilder(
                                  valueListenable: _vehicleRegistrationImage,
                                  builder: (context, value, child) {
                                    return Container(
                                      height: 100,
                                      width: 100,
                                      decoration: BoxDecoration(
                                        borderRadius: appRadius,
                                        border: Border.all(
                                          color: AppColors.primaryColor(
                                            context,
                                          ),
                                        ),
                                      ),
                                      child:
                                          _vehicleRegistrationImage
                                                  .value
                                                  .path ==
                                              "path"
                                          ? widget.vehicleModel != null
                                                ? Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                          8.0,
                                                        ),
                                                    child: Stack(
                                                      alignment:
                                                          Alignment.bottomRight,
                                                      children: [
                                                        commonCachedNetworkImage(
                                                          widget
                                                              .vehicleModel!
                                                              .vehicleRegistration,
                                                          width:
                                                              double.infinity,
                                                          height:
                                                              double.infinity,
                                                          fit: BoxFit.cover,
                                                        ),
                                                        if (widget
                                                                .vehicleModel
                                                                ?.vehicleRegistrationStatus !=
                                                            "1")
                                                          Container(
                                                            margin:
                                                                screenPadding /
                                                                2,
                                                            padding:
                                                                screenPadding /
                                                                2,
                                                            decoration:
                                                                BoxDecoration(
                                                                  shape: BoxShape
                                                                      .circle,
                                                                  color: Colors
                                                                      .white,
                                                                ),
                                                            child: Icon(
                                                              Icons.edit,
                                                              color:
                                                                  AppColors.primaryColor(
                                                                    context,
                                                                  ),
                                                              size: 15,
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  )
                                                : Icon(
                                                    Icons.add,
                                                    color:
                                                        AppColors.primaryColor(
                                                          context,
                                                        ),
                                                  )
                                          : Stack(
                                              alignment: Alignment.bottomRight,
                                              children: [
                                                Container(
                                                  decoration: BoxDecoration(
                                                    image: DecorationImage(
                                                      fit: BoxFit.cover,
                                                      image: FileImage(
                                                        _vehicleRegistrationImage
                                                            .value,
                                                      ),
                                                    ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          10,
                                                        ),
                                                    border: Border.all(
                                                      color:
                                                          AppColors.whiteColor(
                                                            context,
                                                          ),
                                                    ),
                                                  ),
                                                ),
                                                Container(
                                                  margin: screenPadding / 2,
                                                  padding: screenPadding / 2,
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: Colors.white,
                                                  ),
                                                  child: Icon(
                                                    Icons.edit,
                                                    color:
                                                        AppColors.primaryColor(
                                                          context,
                                                        ),
                                                    size: 15,
                                                  ),
                                                ),
                                              ],
                                            ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                          height20,
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  "Select expiry date",
                                  style: AppTextStyles.title(
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: AppTextField(
                                  key: UniqueKey(),

                                  textFieldType: TextFieldType.PHONE,
                                  readOnly: true,
                                  onTap: () {
                                    _getRegisgrationExpiryDate();
                                  },
                                  initialValue: _registratonExpiryDate == null
                                      ? ""
                                      : GlobalMethods.dateToUIString(
                                          _registratonExpiryDate!,
                                        ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    height20,
                    Container(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey.shade400
                          : Colors.grey.shade100,
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Vehicle inspection report",
                                      style: AppTextStyles.title(
                                        color: Colors.black,
                                      ),
                                    ),
                                    getStatus(
                                      status: widget
                                          .vehicleModel
                                          ?.vehicleInspectionStatus,
                                    ),
                                    widget
                                                .vehicleModel
                                                ?.vehicleInspectionRejectionText ==
                                            null
                                        ? SizedBox()
                                        : Text(
                                            widget
                                                    .vehicleModel
                                                    ?.vehicleInspectionRejectionText ??
                                                "",
                                            style: TextStyle(
                                              color: Colors.black,
                                            ),
                                          ),
                                  ],
                                ),
                              ),
                              width20,
                              InkWell(
                                onTap: () {
                                  GlobalMethods.checkCameraPermission(
                                    onSuccess: () async {
                                      if (widget
                                              .vehicleModel
                                              ?.vehicleInspectionStatus !=
                                          "1") {
                                        showCameraGalleryBottomSheet(
                                          _vehicleInspectionReportImage,
                                        );
                                      }
                                    },
                                    context: context,
                                  );
                                },
                                child: ValueListenableBuilder(
                                  valueListenable:
                                      _vehicleInspectionReportImage,
                                  builder: (context, value, child) {
                                    return Container(
                                      height: 100,
                                      width: 100,
                                      decoration: BoxDecoration(
                                        borderRadius: appRadius,
                                        border: Border.all(
                                          color: AppColors.primaryColor(
                                            context,
                                          ),
                                        ),
                                      ),
                                      child:
                                          _vehicleInspectionReportImage
                                                  .value
                                                  .path ==
                                              "path"
                                          ? widget.vehicleModel != null
                                                ? Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                          8.0,
                                                        ),
                                                    child: Stack(
                                                      alignment:
                                                          Alignment.bottomRight,
                                                      children: [
                                                        commonCachedNetworkImage(
                                                          widget
                                                              .vehicleModel!
                                                              .vehicleInspectionReport,
                                                          width:
                                                              double.infinity,
                                                          height:
                                                              double.infinity,
                                                          fit: BoxFit.cover,
                                                        ),
                                                        if (widget
                                                                .vehicleModel
                                                                ?.vehicleInspectionStatus !=
                                                            "1")
                                                          Container(
                                                            margin:
                                                                screenPadding /
                                                                2,
                                                            padding:
                                                                screenPadding /
                                                                2,
                                                            decoration:
                                                                BoxDecoration(
                                                                  shape: BoxShape
                                                                      .circle,
                                                                  color: Colors
                                                                      .white,
                                                                ),
                                                            child: Icon(
                                                              Icons.edit,
                                                              color:
                                                                  AppColors.primaryColor(
                                                                    context,
                                                                  ),
                                                              size: 15,
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  )
                                                : Icon(
                                                    Icons.add,
                                                    color:
                                                        AppColors.primaryColor(
                                                          context,
                                                        ),
                                                  )
                                          : Stack(
                                              alignment: Alignment.bottomRight,
                                              children: [
                                                Container(
                                                  decoration: BoxDecoration(
                                                    image: DecorationImage(
                                                      fit: BoxFit.cover,
                                                      image: FileImage(
                                                        _vehicleInspectionReportImage
                                                            .value,
                                                      ),
                                                    ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          10,
                                                        ),
                                                    border: Border.all(
                                                      color:
                                                          AppColors.whiteColor(
                                                            context,
                                                          ),
                                                    ),
                                                  ),
                                                ),
                                                Container(
                                                  margin: screenPadding / 2,
                                                  padding: screenPadding / 2,
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: Colors.white,
                                                  ),
                                                  child: Icon(
                                                    Icons.edit,
                                                    color:
                                                        AppColors.primaryColor(
                                                          context,
                                                        ),
                                                    size: 15,
                                                  ),
                                                ),
                                              ],
                                            ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                          height20,
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  "Select expiry date",
                                  style: AppTextStyles.title(
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: AppTextField(
                                  key: UniqueKey(),

                                  textFieldType: TextFieldType.PHONE,
                                  readOnly: true,
                                  onTap: () {
                                    _getInspectionExpiryDate();
                                  },
                                  initialValue: _inspectionExpiryDate == null
                                      ? ""
                                      : GlobalMethods.dateToUIString(
                                          _inspectionExpiryDate!,
                                        ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    height20,
                    AppButton(
                      width: double.infinity,
                      text: "Save",
                      onPressed: () async {
                        if (_plateNumberController.text.isEmpty) {
                          GlobalMethods.errorToast(
                            context,
                            "Please fill plate number",
                          );
                        } else if (_vehicleRegistrationImage.value.path ==
                                "path" &&
                            widget.vehicleModel == null) {
                          GlobalMethods.errorToast(
                            context,
                            "Please upload vehicle registration image",
                          );
                        } else if (_registratonExpiryDate == null) {
                          GlobalMethods.errorToast(
                            context,
                            "Please select registration expiry date",
                          );
                        } else if (_vehicleInspectionReportImage.value.path ==
                                "path" &&
                            widget.vehicleModel == null) {
                          GlobalMethods.errorToast(
                            context,
                            "Please upload vehicle inspection report image",
                          );
                        } else if (_inspectionExpiryDate == null) {
                          GlobalMethods.errorToast(
                            context,
                            "Please select inspection expiry date",
                          );
                        } else {
                          MultipartRequest multiPartRequest =
                              await getMultiPartRequest("vehicles/add");

                          if (widget.vehicleModel != null) {
                            multiPartRequest.fields['id'] = widget
                                .vehicleModel!
                                .id
                                .toString();
                          }

                          multiPartRequest.fields['make_id'] =
                              _selectedVehicleBrandId.value.toString();
                          multiPartRequest.fields['model_id'] =
                              _selectedVehicleModel.value.id.toString();
                          multiPartRequest.fields['model_name'] =
                              _selectedVehicleModel.value.name.toString();
                          multiPartRequest.fields['seats'] =
                              _selectedVehicleModel.value.seat.toString();

                          multiPartRequest.fields['production_year'] =
                              _selectedProductionYear.value.toString();

                          multiPartRequest.fields['plateNumber'] =
                              _plateNumberController.text;
                          multiPartRequest.fields['transmission'] =
                              _selected_transmission.value;
                          multiPartRequest.fields['is_dash_cam_available'] =
                              _isDashCamAvailable.value.toString();
                          if (_vehicleImage.value.path != "path") {
                            multiPartRequest.files.add(
                              await MultipartFile.fromPath(
                                'image',
                                _vehicleImage.value.path,
                              ),
                            );
                          }

                          if (_vehicleRegistrationImage.value.path != "path") {
                            multiPartRequest.files.add(
                              await MultipartFile.fromPath(
                                'vehicle_registration',
                                _vehicleRegistrationImage.value.path,
                              ),
                            );
                          }
                          if (_vehicleInspectionReportImage.value.path !=
                              "path") {
                            multiPartRequest.files.add(
                              await MultipartFile.fromPath(
                                'vehicle_inspection_report',
                                _vehicleInspectionReportImage.value.path,
                              ),
                            );
                          }

                          multiPartRequest.fields['registration_expiry'] =
                              GlobalMethods.dateToAPIString(
                                _registratonExpiryDate!,
                              );
                          multiPartRequest.fields['inspection_expiry'] =
                              GlobalMethods.dateToAPIString(
                                _inspectionExpiryDate!,
                              );

                          _saveVehicle(multiPartRequest: multiPartRequest);
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  DateTime? _registratonExpiryDate;
  DateTime? _inspectionExpiryDate;

  void _getRegisgrationExpiryDate() {
    if (_registratonExpiryDate != null &&
        _registratonExpiryDate!.isBefore(DateTime.now())) {
      _registratonExpiryDate = DateTime.now().add(Duration(days: 1));
    }

    showDatePicker(
      context: context,
      initialDate: DateTime.now().add(Duration(days: 1)),
      firstDate: DateTime.now().add(Duration(days: 1)),
      lastDate: DateTime.now().add(Duration(days: 365 * 50)),
    ).then((value) {
      if (value != null && value != _registratonExpiryDate) {
        setState(() {
          _registratonExpiryDate = value;
        });
      }
    });
  }

  void _getInspectionExpiryDate() {
    if (_inspectionExpiryDate != null &&
        _inspectionExpiryDate!.isBefore(DateTime.now())) {
      _inspectionExpiryDate = DateTime.now().add(Duration(days: 1));
    }

    showDatePicker(
      context: context,
      initialDate: DateTime.now().add(Duration(days: 1)),
      firstDate: DateTime.now().add(Duration(days: 1)),
      lastDate: DateTime.now().add(Duration(days: 365 * 50)),
    ).then((value) {
      if (value != null && value != _inspectionExpiryDate) {
        setState(() {
          _inspectionExpiryDate = value;
        });
      }
    });
  }

  showCameraGalleryBottomSheet(ValueNotifier<File> image) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          height: 150,
          padding: screenPadding,
          child: Row(
            children: [
              Column(
                children: [
                  IconButton(
                    onPressed: () async {
                      final ImagePicker picker = ImagePicker();

                      final XFile? photo = await picker.pickImage(
                        source: ImageSource.camera,
                        imageQuality: 10,
                      );

                      if (photo != null) {
                        closeScreen(context);

                        bool value = await GlobalMethods.checkImageSize(
                          result: photo,
                          context: context,
                        );
                        if (value) {
                          if (image == _vehicleInspectionReportImage) {
                            _vehicleInspectionReportImage.value = File(
                              photo.path,
                            );
                          } else if (image == _vehicleRegistrationImage) {
                            _vehicleRegistrationImage.value = File(photo.path);
                          }
                        }
                      }
                    },
                    icon: Icon(Icons.camera),
                  ),
                  height10,
                  Text("From camera"),
                ],
              ),
              width20,
              Column(
                children: [
                  IconButton(
                    onPressed: () async {
                      final ImagePicker picker = ImagePicker();

                      final XFile? photo = await picker.pickImage(
                        source: ImageSource.gallery,
                        imageQuality: 10,
                      );

                      if (photo != null) {
                        closeScreen(context);

                        image.value = File(photo.path);
                      }
                    },
                    icon: Icon(Icons.image),
                  ),
                  height10,
                  Text("From gallery"),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget getStatus({String? status}) {
    if (status == "0") {
      return Text(
        "Pending",
        style: AppTextStyles.text(color: AppColors.primaryColor(context)),
      );
    } else if (status == "1") {
      return Text(
        "Approved",
        style: AppTextStyles.text(color: AppColors.greenColor),
      );
    } else if (status == "2") {
      return Text("Rejected", style: AppTextStyles.text(color: Colors.red));
    }
    return SizedBox();
  }

  List<VehicleBrandModel> _getFilteredVehicleBrandList(String text) {
    return _vehicleBrandList
        .where((brand) => brand.name.toLowerCase().contains(text.toLowerCase()))
        .toList();
  }

  List<VehicleModelModel> _getFilteredVehicleModelList(String text) {
    return _vehicleModelList
        .where((model) => model.name.toLowerCase().contains(text.toLowerCase()))
        .toList();
  }
}



//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////

