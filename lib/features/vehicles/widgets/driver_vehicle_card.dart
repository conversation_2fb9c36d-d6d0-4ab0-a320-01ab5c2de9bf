import 'package:cached_network_image/cached_network_image.dart';
import 'package:rooo_driver/features/vehicles/models/driver_vehicle_model.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/icon_container.dart';

class DriverVehicleCard extends StatelessWidget {
  final DriverVehicleModel data;
  final void Function() onEdit;
  final Function() ondelete;
  final Function() onActivate;

  const DriverVehicleCard(
      {super.key,
      required this.data,
      required this.onEdit,
      required this.onActivate,
      required this.ondelete});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.grey.shade100,
      child: Padding(
        padding: screenPadding / 2,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              data: data.plateNumber.toString(),
              textStyle: TextStyle(color: Colors.black),
              size: 20,
              fontweight: FontWeight.bold,
            ),
            Divider(
              color: Colors.black,
            ),
            height5,
            Row(
              children: [
                Text(
                  "Model",
                  style: TextStyle(color: Colors.black),
                ),
                width10,
                Expanded(
                  child: Text(
                    data.model_name,
                    textAlign: TextAlign.end,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.black),
                  ),
                ),
              ],
            ),
            height5,
            Row(
              children: [
                Text(
                  "Dashcam",
                  style: TextStyle(color: Colors.black),
                ),
                width10,
                Expanded(
                  child: Text(
                    data.isDashCamAvailable.toString() == "true" ? "Yes" : "No",
                    textAlign: TextAlign.end,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.black),
                  ),
                ),
              ],
            ),
            height5,
            Row(
              children: [
                Text(
                  "Transmission",
                  style: TextStyle(color: Colors.black),
                ),
                width10,
                Expanded(
                  child: Text(
                    data.transmission,
                    textAlign: TextAlign.end,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.black),
                  ),
                ),
              ],
            ),
            height5,
            const Divider(),
            Text(
              "Service Type/s",
              style: TextStyle(color: Colors.black),
            ),
            height5,
            Text(
              data.serviceName.join(", "),
              maxLines: 8,
              overflow: TextOverflow.ellipsis,
              style:
                  TextStyle(fontWeight: FontWeight.bold, color: Colors.black),
            ),
            height5,
            data.vehicleRegistrationStatus == "1" &&
                    data.vehicleInspectionStatus == "1"
                ? Text(
                    "Your vehicle has been approved. The assigned service types are shown above. If you find any discrepancies, please contact us.",
                    style: TextStyle(
                        fontStyle: FontStyle.italic,
                        fontSize: 10,
                        color: Colors.black),
                  )
                : Text(
                    "On approval, we will assign appropriate service types for your vehicle. If you find any discrepancies, please contact us.",
                    style: TextStyle(
                        fontStyle: FontStyle.italic,
                        fontSize: 10,
                        color: Colors.black),
                  ),
            Divider(
              color: Colors.black,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Registration document details",
                  style: TextStyle(
                      fontStyle: FontStyle.italic, color: Colors.black),
                ),
                height10,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Expiry date",
                      style: TextStyle(color: Colors.black),
                    ),
                    Text(
                      GlobalMethods.dateToUIString(
                          data.registrationDocExpirayDate),
                      style: TextStyle(color: Colors.black),
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Approval status",
                      style: TextStyle(color: Colors.black),
                    ),
                    getStatus(
                        status: data.vehicleRegistrationStatus,
                        context: context)
                  ],
                ),
                data.vehicleRegistrationRejectionText == null
                    ? SizedBox()
                    : Text(
                        data.vehicleRegistrationRejectionText ?? "",
                        style: TextStyle(color: Colors.black),
                      ),
              ],
            ),
            Divider(
              color: Colors.black,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Inspection document details",
                  style: TextStyle(
                      fontStyle: FontStyle.italic, color: Colors.black),
                ),
                height10,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Expiry date",
                      style: TextStyle(color: Colors.black),
                    ),
                    Text(
                      GlobalMethods.dateToUIString(
                          data.inspectionDocExpirayDate),
                      style: TextStyle(color: Colors.black),
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Approval status",
                      style: TextStyle(color: Colors.black),
                    ),
                    getStatus(
                        status: data.vehicleInspectionStatus, context: context)
                  ],
                ),
                data.vehicleInspectionRejectionText == null
                    ? SizedBox()
                    : Text(
                        data.vehicleInspectionRejectionText ?? "",
                        style: TextStyle(color: Colors.black),
                      ),
              ],
            ),
            const Divider(
              color: Colors.black,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                data.vehicleInspectionStatus == "2" ||
                        data.vehicleRegistrationStatus == "2" ||
                        data.vehicleInspectionStatus == "3" ||
                        data.vehicleRegistrationStatus == "3"
                    ? IconContainer(onTap: onEdit, icon: Icon(Icons.edit))
                    : const SizedBox(),
                data.isSelected
                    ? SizedBox()
                    : IconContainer(onTap: ondelete, icon: Icon(Icons.delete)),
                data.isSelected
                    ? Container(
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(color: AppColors.greenColor)),
                        child: Center(
                            child: Icon(
                          Icons.check,
                          color: AppColors.greenColor,
                        )),
                      )
                    : AppButton(
                        text: "Activate",
                        onPressed: () async {
                          // if(data.vehicleInspectionStatus =="1" && data.vehicleRegistrationStatus == "1")
                          // {

                          onActivate();
                          // }
                          // else{
                          // GlobalMethods.errorToast(context, message)
                          // }
                        }),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget getStatus({String? status, required BuildContext context}) {
    if (status == "0") {
      return Text(
        "Pending",
        style: AppTextStyles.text(color: Colors.red),
      );
    } else if (status == "1") {
      return Text(
        "Approved",
        style: AppTextStyles.text(color: AppColors.greenColor),
      );
    } else if (status == "2") {
      return Text(
        "Rejected",
        style: AppTextStyles.text(color: Colors.red),
      );
    }
    else if (status == "3") {
      return Text(
        "Expired",
        style: AppTextStyles.text(color: Colors.red),
      );
    }
    return Text("Pending");
  }
}
