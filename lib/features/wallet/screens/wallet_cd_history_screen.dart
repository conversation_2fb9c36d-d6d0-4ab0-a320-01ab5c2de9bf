import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/WalletListModel.dart';

class WalletCDHistoryScreen extends StatefulWidget {
  const WalletCDHistoryScreen({super.key});

  @override
  WalletCDHistoryScreenState createState() => WalletCDHistoryScreenState();
}

class WalletCDHistoryScreenState extends State<WalletCDHistoryScreen> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  TextEditingController addMoneyController = TextEditingController();
  ScrollController scrollController = ScrollController();
  int currentPage = 1;
  int totalPage = 1;
  int currentIndex = -1;

  List<WalletModel> walletData = [];

  num totalAmount = 0;

  @override
  void initState() {
    appStore.setLoading(true);
    init();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (currentPage < totalPage) {
          currentPage++;
          init();
        }
      }
    });
    super.initState();
  }

  void init() async {
    setState(() {
      appStore.setLoading(true);
    });
    await getWalletList(page: currentPage)
        .then((value) {
          if (value == null) {
            GlobalMethods.errorToast(context, "Server error");
            return;
          }
          currentPage = value.pagination!.currentPage!;
          totalPage = value.pagination!.totalPages!;
          totalAmount = value.walletBalance!.totalAmount ?? 0;
          if (currentPage == 1) {
            walletData.clear();
          }
          walletData.addAll(value.data!);
          setState(() {
            appStore.setLoading(false);
          });
        })
        .catchError((error) {
          setState(() {
            appStore.setLoading(false);
          });
        });
  }

  @override
  dispose() {
    scrollController.dispose();
    appStore.setLoading(false);
    super.dispose();
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: "Wallet History"),
      body: Stack(
        children: [
          SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            padding: const EdgeInsets.fromLTRB(16, 20, 16, 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(bottom: 16),
                  width: MediaQuery.sizeOf(context).width,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Theme.of(context).primaryColor,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        "Current Balance",
                      ),
                      const SizedBox(height: 8),
                      Text(
                        totalAmount.toString(),
                        style: boldTextStyle(
                          color: Colors.black,
                          size: 32,
                        ),
                      ),
                    ],
                  ),
                ),
                Text("Recent Transactions", style: primaryTextStyle()),
                walletData.isEmpty
                    ? const SizedBox(
                        height: 300,
                        child: Center(
                          child: Text(
                            "No Transactions Yet",
                            style: TextStyle(fontSize: 28),
                          ),
                        ),
                      )
                    : AnimationLimiter(
                        child: ListView.builder(
                          padding: const EdgeInsets.only(top: 8, bottom: 8),
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: walletData.length,
                          shrinkWrap: true,
                          itemBuilder: (_, index) {
                            WalletModel data = walletData[index];
                            return AnimationConfiguration.staggeredList(
                              delay: const Duration(milliseconds: 200),
                              position: index,
                              duration: const Duration(milliseconds: 375),
                              child: SlideAnimation(
                                child: Container(
                                  margin: const EdgeInsets.only(
                                    top: 8,
                                    bottom: 8,
                                  ),
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade100,
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Row(
                                    children: [
                                      Container(
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(
                                            10,
                                          ),
                                          color: Colors.white,
                                        ),
                                        padding: const EdgeInsets.all(8),
                                        child: Icon(
                                          data.type == "credit"
                                              ? Icons.add
                                              : Icons.remove,
                                          color: data.type == "credit"
                                              ? Colors.green
                                              : Colors.red,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              data.type == "credit"
                                                  ? "Money Credited"
                                                  : "Money Debited",
                                              style: TextStyle(
                                                fontSize: 16,
                                                color: Colors.black,
                                              ),
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              formatDate(
                                                data.createdAt!,
                                                showTime: true,
                                              ),
                                              style: const TextStyle(
                                                fontSize: 12,
                                                color: Colors.black,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Text(
                                        'AUD ${data.amount}',
                                        style: TextStyle(
                                          color: data.type == "credit"
                                              ? Colors.green
                                              : Colors.red,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
              ],
            ),
          ),
          Observer(
            builder: (context) {
              return Visibility(
                visible: appStore.isLoading,
                child: loaderWidget(),
              );
            },
          ),
        ],
      ),
    );
  }
}
