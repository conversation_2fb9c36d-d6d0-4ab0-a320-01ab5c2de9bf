import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:rooo_driver/features/help/screens/help_screen2.dart';
import 'package:rooo_driver/features/withdraw/screeens/withdraw_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/WalletListModel.dart';
import 'package:rooo_driver/utils/Extensions/Loader.dart';

class MyWalletScreen extends StatefulWidget {
  @override
  MyWalletScreenState createState() => MyWalletScreenState();
}

class MyWalletScreenState extends State<MyWalletScreen> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  TextEditingController addMoneyController = TextEditingController();
  ScrollController scrollController = ScrollController();
  int currentPage = 1;
  int totalPage = 1;

  int currentIndex = -1;

  List<WalletModel> walletData = [];

  num totalAmount = 0;

  @override
  void initState() {
    super.initState();
    getWithDrawList();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (currentPage < totalPage) {
          appStore.setLoading(true);
          currentPage++;
          setState(() {});

          getWithDrawList();
        }
      }
    });
    afterBuildCreated(() => appStore.setLoading(true));
  }

  void getWithDrawList() async {
    await getWithDrawListApi(page: currentPage).then((value) {
      appStore.setLoading(false);

      currentPage = value.pagination!.currentPage!;
      totalPage = value.pagination!.totalPages!;
      totalAmount = value.wallet_balance!.totalAmount!;

      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);
    });
  }

  void getWalletList() async {
    await getWalletListApi(pageData: currentPage).then((value) {
      appStore.setLoading(false);

      currentPage = value.pagination!.currentPage!;
      totalPage = value.pagination!.totalPages!;
      totalAmount = value.walletBalance!.totalAmount!.toInt();
      if (currentPage == 1) {
        walletData.clear();
      }
      walletData.addAll(value.data!);
      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: AppBar(
      //   title:
      //       Text(language.myWallet, style: boldTextStyle(color: Colors.white)),
      // ),
      body: Observer(builder: (context) {
        return Stack(
          children: [
            SingleChildScrollView(
              physics: BouncingScrollPhysics(),
              padding: EdgeInsets.fromLTRB(16, 20, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: radius(),
                    ),
                    child: Column(
                      //smainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 15, left: 20),
                              child: Text(
                                language.balanceTxt,
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                            Padding(
                              padding:
                                  const EdgeInsets.only(left: 20, right: 20),
                              child: Row(
                                children: [
                                  Text(
                                    appStore.currencyCode +
                                        totalAmount.toString(),
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 25),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        Padding(
                            padding: EdgeInsets.all(16.0),
                            child: totalAmount <= 0
                                ? const SizedBox()
                                : AppButton(
                                    text: language.cashOutTxt,
                                    icon:
                                        Icon(Icons.arrow_forward_ios, size: 15),
                                    onPressed: ()  async {
                                      GlobalMethods.pushScreen(
                                          context: context,
                                          screen: WithDrawScreen(onTap: () {
                                            getWalletList();
                                          }),
                                          screenIdentifier:
                                              ScreenIdentifier.WithDrawScreen);
                                    },
                                  )),
                        height20,
                      ],
                    ),
                  ),
                  Divider(
                    color: Colors.grey[300],
                  ),
                  ListTile(
                    leading: Icon(
                      Icons.wallet,
                    ),
                    title: Text(
                      language.WalletHistoryTxt,
                      style: TextStyle(),
                    ),
                    onTap: () {
                      GlobalMethods.pushScreen(
                          context: context,
                          screen: MyWalletScreen(),
                          screenIdentifier:
                              ScreenIdentifier.WalletHistoryScreen);
                    },
                  ),
                  Divider(
                    color: Colors.grey[300],
                  ),
                  ListTile(
                    leading: Icon(
                      Icons.help,
                    ),
                    title: Text(
                      language.helpTxt,
                    ),
                    onTap: () {
                      GlobalMethods.pushScreen(
                          context: context,
                          screen: HelpScreen(),
                          screenIdentifier: ScreenIdentifier.HelpScreen);
                    },
                  ),
                  Divider(
                    color: Colors.grey[300],
                  ),
                ],
              ),
            ),
            Visibility(
              visible: appStore.isLoading,
              child: Loader(),
            ),
            // !appStore.isLoading && walletData.isEmpty
            //     ? emptyWidget()
            //     : SizedBox(),
          ],
        );
      }),
      // bottomNavigationBar: IosPadding(
      //   // padding: EdgeInsets.all(16),
      //   child: Row(
      //     children: [
      //       if (totalAmount != 0)
      //         Expanded(
      //           child: AppButtonWidget(
      //             text: language.withDraw,
      //             textStyle: boldTextStyle(color: Colors.white),
      //             width: MediaQuery.of(context).size.width,
      //             color: getPrimaryColor(),
      //             onTap: () {
      //               if (totalAmount > 0) {
      //                 launchScreen(context, WithDrawScreen(
      //                   onTap: () {
      //                     getWalletList();
      //                   },
      //                 ));
      //               } else {
      //                 toast(language.LowBalanceTxt);
      //               }
      //             },
      //           ),
      //         ),
      //       // if (totalAmount != 0) SizedBox(width: 16),
      //       // Expanded(
      //       //   child: AppButtonWidget(
      //       //     text: language.addMoney,
      //       //     textStyle: boldTextStyle(color: Colors.white),
      //       //     color: getPrimaryColor(),
      //       //     onTap: () {
      //       //       showModalBottomSheet(
      //       //         context: context,
      //       //         shape: RoundedRectangleBorder(
      //       //             borderRadius: BorderRadius.only(
      //       //                 topLeft: Radius.circular(defaultRadius),
      //       //                 topRight: Radius.circular(defaultRadius))),
      //       //         builder: (_) {
      //       //           return Form(
      //       //             key: formKey,
      //       //             child: StatefulBuilder(
      //       //               builder:
      //       //                   (BuildContext context, StateSetter setState) {
      //       //                 return Padding(
      //       //                   padding: MediaQuery.of(context).viewInsets,
      //       //                   child: SingleChildScrollView(
      //       //                     padding: EdgeInsets.all(16),
      //       //                     child: Column(
      //       //                       mainAxisSize: MainAxisSize.min,
      //       //                       children: [
      //       //                         Text(language.addMoney,
      //       //                             style: IS_DARK_MODE_ON
      //       //                                 ? boldTextStyleNight()
      //       //                                 : boldTextStyle()),
      //       //                         SizedBox(height: 16),
      //       //                         AppTextField(
      //       //                           textStyle: IS_DARK_MODE_ON
      //       //                               ? TextStyle(color: Colors.black)
      //       //                               : null,
      //       //                           controller: addMoneyController,
      //       //                           textFieldType: TextFieldType.PHONE,
      //       //                           keyboardType: TextInputType.number,
      //       //                           errorThisFieldRequired:
      //       //                               language.thisFieldRequired,
      //       //                           onChanged: (val) {
      //       //                             //
      //       //                           },
      //       //                           validator: (String? val) {
      //       //                             if (appStore.minAmountToAdd != null &&
      //       //                                 int.parse(val!) <
      //       //                                     appStore.minAmountToAdd!) {
      //       //                               addMoneyController.text = appStore
      //       //                                   .minAmountToAdd
      //       //                                   .toString();
      //       //                               addMoneyController.selection =
      //       //                                   TextSelection.fromPosition(
      //       //                                       TextPosition(
      //       //                                           offset: appStore
      //       //                                               .minAmountToAdd
      //       //                                               .toString()
      //       //                                               .length));
      //       //                               return "Minimum ${appStore.minAmountToAdd} required";
      //       //                             } else if (appStore.maxAmountToAdd !=
      //       //                                     null &&
      //       //                                 int.parse(val!) >
      //       //                                     appStore.maxAmountToAdd!) {
      //       //                               addMoneyController.text = appStore
      //       //                                   .maxAmountToAdd
      //       //                                   .toString();
      //       //                               addMoneyController.selection =
      //       //                                   TextSelection.fromPosition(
      //       //                                       TextPosition(
      //       //                                           offset: appStore
      //       //                                               .maxAmountToAdd
      //       //                                               .toString()
      //       //                                               .length));
      //       //                               return "Maximum ${appStore.maxAmountToAdd} required";
      //       //                             }
      //       //                             return null;
      //       //                           },
      //       //                           decoration: inputDecoration(context,
      //       //                               hint: language.amount,
      //       //                               hintTextStyle:
      //       //                                   TextStyle(color: Colors.black)),
      //       //                         ),
      //       //                         SizedBox(height: 16),
      //       //                         Wrap(
      //       //                           runSpacing: 8,
      //       //                           spacing: 8,
      //       //                           children: appStore.walletPresetTopUpAmount
      //       //                               .split('|')
      //       //                               .map((e) {
      //       //                             return inkWellWidget(
      //       //                               onTap: () {
      //       //                                 currentIndex = appStore
      //       //                                     .walletPresetTopUpAmount
      //       //                                     .split('|')
      //       //                                     .indexOf(e);
      //       //                                 if (appStore.minAmountToAdd !=
      //       //                                         null &&
      //       //                                     int.parse(e) <
      //       //                                         appStore.minAmountToAdd!) {
      //       //                                   addMoneyController.text = appStore
      //       //                                       .minAmountToAdd
      //       //                                       .toString();
      //       //                                   addMoneyController.selection =
      //       //                                       TextSelection.fromPosition(
      //       //                                           TextPosition(
      //       //                                               offset: appStore
      //       //                                                   .minAmountToAdd
      //       //                                                   .toString()
      //       //                                                   .length));
      //       //                                   toast(
      //       //                                       "Minimum ${appStore.minAmountToAdd} required");
      //       //                                 } else if (appStore
      //       //                                             .minAmountToAdd !=
      //       //                                         null &&
      //       //                                     int.parse(e) <
      //       //                                         appStore.minAmountToAdd! &&
      //       //                                     appStore.maxAmountToAdd !=
      //       //                                         null &&
      //       //                                     int.parse(e) >
      //       //                                         appStore.maxAmountToAdd
      //       //                                             .toString()
      //       //                                             .length) {
      //       //                                   addMoneyController.text = appStore
      //       //                                       .maxAmountToAdd
      //       //                                       .toString();
      //       //                                   addMoneyController.selection =
      //       //                                       TextSelection.fromPosition(
      //       //                                           TextPosition(
      //       //                                               offset: e.length));
      //       //                                   toast(
      //       //                                       "Maximum ${appStore.maxAmountToAdd} required");
      //       //                                 } else {
      //       //                                   addMoneyController.text = e;
      //       //                                   addMoneyController.selection =
      //       //                                       TextSelection.fromPosition(
      //       //                                           TextPosition(
      //       //                                               offset: e.length));
      //       //                                 }

      //       //                                 setState(() {});
      //       //                               },
      //       //                               child: Container(
      //       //                                 padding: EdgeInsets.all(8),
      //       //                                 decoration: BoxDecoration(
      //       //                                   color: currentIndex ==
      //       //                                           appStore
      //       //                                               .walletPresetTopUpAmount
      //       //                                               .split('|')
      //       //                                               .indexOf(e)
      //       //                                       ? getPrimaryColor()
      //       //                                       : IS_DARK_MODE_ON
      //       //                                           ? Colors.grey
      //       //                                           : Colors.white,
      //       //                                   border: Border.all(
      //       //                                       color: currentIndex ==
      //       //                                               appStore
      //       //                                                   .walletPresetTopUpAmount
      //       //                                                   .split('|')
      //       //                                                   .indexOf(e)
      //       //                                           ? getPrimaryColor()
      //       //                                           : Colors.grey),
      //       //                                   borderRadius:
      //       //                                       BorderRadius.circular(
      //       //                                           defaultRadius),
      //       //                                 ),
      //       //                                 child: Text(
      //       //                                     appStore.currencyPosition ==
      //       //                                             LEFT
      //       //                                         ? '${appStore.currencyCode} $e'
      //       //                                         : '$e ${appStore.currencyCode}',
      //       //                                     style: boldTextStyle(
      //       //                                         color: currentIndex ==
      //       //                                                 appStore
      //       //                                                     .walletPresetTopUpAmount
      //       //                                                     .split('|')
      //       //                                                     .indexOf(e)
      //       //                                             ? Colors.white
      //       //                                             : getPrimaryColor())),
      //       //                               ),
      //       //                             );
      //       //                           }).toList(),
      //       //                         ),
      //       //                         SizedBox(height: 16),
      //       //                         Row(
      //       //                           children: [
      //       //                             Expanded(
      //       //                               child: AppButtonWidget(
      //       //                                 text: language.cancelTxt,
      //       //                                 textStyle: boldTextStyle(
      //       //                                     color: Colors.white),
      //       //                                 width: MediaQuery.of(context)
      //       //                                     .size
      //       //                                     .width,
      //       //                                 color: Colors.red,
      //       //                                 onTap: () {
      //       //                                   Navigator.pop(context);
      //       //                                 },
      //       //                               ),
      //       //                             ),
      //       //                             SizedBox(width: 16),
      //       //                             Expanded(
      //       //                               child: AppButtonWidget(
      //       //                                 text: language.addMoney,
      //       //                                 textStyle: boldTextStyle(
      //       //                                     color: Colors.white),
      //       //                                 width: MediaQuery.of(context)
      //       //                                     .size
      //       //                                     .width,
      //       //                                 color: getPrimaryColor(),
      //       //                                 onTap: () {
      //       //                                   if (addMoneyController
      //       //                                       .text.isNotEmpty) {
      //       //                                     if (formKey.currentState!
      //       //                                             .validate() &&
      //       //                                         addMoneyController
      //       //                                             .text.isNotEmpty) {
      //       //                                       Navigator.pop(context);
      //       //                                       launchScreen(
      //       //                                           context,
      //       //                                           PaymentScreen(
      //       //                                               amount: int.parse(
      //       //                                                   addMoneyController
      //       //                                                       .text)),
      //       //                                           pageRouteAnimation:
      //       //                                               PageRouteAnimation
      //       //                                                   .SlideBottomTop);
      //       //                                     } else {
      //       //                                       toast(language
      //       //                                           .pleaseSelectAmount);
      //       //                                     }
      //       //                                   } else {
      //       //                                     toast(language
      //       //                                         .pleaseSelectAmount);
      //       //                                   }
      //       //                                 },
      //       //                               ),
      //       //                             ),
      //       //                           ],
      //       //                         )
      //       //                       ],
      //       //                     ),
      //       //                   ),
      //       //                 );
      //       //               },
      //       //             ),
      //       //           );
      //       //         },
      //       //       );
      //       //     },
      //       //   ),
      //       // ),
      //     ],
      //   ),
      // ),
    );
  }
}
