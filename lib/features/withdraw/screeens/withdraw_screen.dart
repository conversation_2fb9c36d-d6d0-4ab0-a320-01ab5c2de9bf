import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/ios_padding.dart';
import 'package:rooo_driver/model/WithDrawListModel.dart';
import 'package:rooo_driver/utils/Extensions/Loader.dart';
import 'package:rooo_driver/utils/Extensions/app_textfield.dart';

class WithDrawScreen extends StatefulWidget {
  final Function() onTap;

  WithDrawScreen({required this.onTap});

  @override
  WithDrawScreenState createState() => WithDrawScreenState();
}

class WithDrawScreenState extends State<WithDrawScreen> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  ScrollController scrollController = ScrollController();
  TextEditingController addMoneyController = TextEditingController();

  int currentPage = 1;
  int totalPage = 1;

  List<WithDrawModel> withDrawData = [];

  num totalAmount = 0;
  int currentIndex = -1;

  String? api_message;

  ValueNotifier<bool> _isInstantPay = ValueNotifier(false);

  Color _getStatusColor(int status) {
    if (status == 1) {
      return Colors.green;
    } else if (status == 0) {
      return Colors.blue;
    } else if (status == 2) {
      return Colors.red;
    }
    return Colors.grey;
  }

  String _getStatusText(int status) {
    if (status == 1) {
      return language.approved;
    } else if (status == 0) {
      return language.requested;
    } else if (status == 2) {
      return language.rejectedTxt;
    }
    return "Unknown";
  }

  @override
  void initState() {
    super.initState();
    getWithDrawList();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (currentPage < totalPage) {
          appStore.setLoading(true);
          currentPage++;
          setState(() {});

          getWithDrawList();
        }
      }
    });
    afterBuildCreated(() => appStore.setLoading(true));
  }

  void getWithDrawList() async {
    await getWithDrawListApi(page: currentPage).then((value) {
      appStore.setLoading(false);
      api_message = value.message;

      currentPage = value.pagination!.currentPage!;
      totalPage = value.pagination!.totalPages!;
      totalAmount = value.wallet_balance!.totalAmount!;
      if (currentPage == 1) {
        withDrawData.clear();
      }
      withDrawData.addAll(value.data!);
      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);
    });
  }

  Future<void> withDrawRequest({int? userId, double? amount}) async {
    appStore.setLoading(true);
    Map req = {
      "user_id": sharedPref.getInt(USER_ID),
      "currency": "aud",
      "amount": amount,
      "status": "0",
      "instant_pay": _isInstantPay.value
    };
    await saveWithDrawRequest(req).then((value) {
      if (value.status) {
        GlobalMethods.succesToast(context, value.message.toString());
        Navigator.pop(context);
        widget.onTap.call();
        getWithDrawList();
      } else {
        GlobalMethods.errorToast(context, value.message.toString());
        Navigator.pop(context);
        setState(() {
          appStore.setLoading(false);
        });
      }
    }).catchError((error) {
      Navigator.pop(context);
      GlobalMethods.errorToast(context, "Server error");
      appStore.setLoading(false);
    });
  }

  getStatus({required int status}) {
    if (status == 1) {
      return Text(language.approved,
          style: secondaryTextStyle(color: Colors.green));
    } else if (status == 0) {
      return Text(language.requested,
          style: secondaryTextStyle(color: Colors.blue));
    } else if (status == 2) {
      return Text(language.rejectedTxt,
          style: secondaryTextStyle(color: Colors.red));
    }
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: language.withDraw),
      body: Observer(builder: (context) {
        return Form(
          key: formKey,
          child: Stack(
            children: [
              SingleChildScrollView(
                padding: EdgeInsets.all(16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(20),
                      margin: EdgeInsets.only(bottom: 20),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppColors.primaryColor(context),
                            AppColors.primaryColor(context).withOpacity(0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primaryColor(context)
                                .withOpacity(0.2),
                            blurRadius: 10,
                            offset: Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            language.availableBalance,
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.9),
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          height15,
                          Text(
                            appStore.currencyPosition == LEFT
                                ? '${appStore.currencyCode} ${totalAmount.toStringAsFixed(2)}'
                                : '${totalAmount.toStringAsFixed(2)} ${appStore.currencyCode}',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      language.withdrawHistory,
                      style: AppTextStyles.header().copyWith(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    height15,
                    withDrawData.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.history,
                                  size: 48,
                                  color: Theme.of(context)
                                      .hintColor
                                      .withOpacity(0.5),
                                ),
                                height10,
                                Text(
                                  "No withdrawal history",
                                  style: TextStyle(
                                    color: Theme.of(context).hintColor,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: withDrawData.length,
                            shrinkWrap: true,
                            itemBuilder: (_, index) {
                              WithDrawModel data = withDrawData[index];
                              return Container(
                                margin: EdgeInsets.only(bottom: 12),
                                padding: EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).cardColor,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Theme.of(context)
                                        .dividerColor
                                        .withOpacity(0.1),
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Theme.of(context)
                                          .shadowColor
                                          .withOpacity(0.05),
                                      blurRadius: 10,
                                      offset: Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          appStore.currencyPosition == LEFT
                                              ? '${appStore.currencyCode} ${data.amount}'
                                              : '${data.amount} ${appStore.currencyCode}',
                                          style:
                                              AppTextStyles.header().copyWith(
                                            fontSize: 18,
                                          ),
                                        ),
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 6),
                                          decoration: BoxDecoration(
                                            color: _getStatusColor(
                                                    data.status ?? -1)
                                                .withOpacity(0.1),
                                            borderRadius:
                                                BorderRadius.circular(20),
                                          ),
                                          child: Text(
                                            _getStatusText(data.status!),
                                            style: TextStyle(
                                              color:
                                                  _getStatusColor(data.status!),
                                              fontSize: 12,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    height10,
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          formatDate(data.created_at!,
                                              showTime: true),
                                          style: TextStyle(
                                            color: Theme.of(context).hintColor,
                                            fontSize: 14,
                                          ),
                                        ),
                                        if (data.instant_pay ?? false)
                                          Container(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 4),
                                            decoration: BoxDecoration(
                                              color: AppColors.primaryColor(
                                                      context)
                                                  .withOpacity(0.1),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(
                                                  Icons.bolt,
                                                  size: 14,
                                                  color: AppColors.primaryColor(
                                                      context),
                                                ),
                                                width5,
                                                Text(
                                                  "Instant pay",
                                                  style: TextStyle(
                                                    color:
                                                        AppColors.primaryColor(
                                                            context),
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                  ],
                ),
              ),
              Visibility(
                visible: appStore.isLoading,
                child: Loader(),
              ),
            ],
          ),
        );
      }),
      bottomNavigationBar: Visibility(
        visible: totalAmount > 0,
        child: IosPadding(
          child: Padding(
            padding: EdgeInsets.all(16),
            child: AppButton(
              text: language.withDraw,
              onPressed: () async {
                _showDialog();
              },
            ),
          ),
        ),
      ),
    );
  }

  void _showDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text("Withdraw", style: boldTextStyle()),
              SizedBox(height: 16),
              TextField(
                controller: addMoneyController,
              ),
             
              SizedBox(height: 16),
              Wrap(
                runSpacing: 8,
                spacing: 8,
                children: appStore.walletPresetTopUpAmount.split('|').map((e) {
                  return inkWellWidget(
                    onTap: () {
                      currentIndex = appStore.walletPresetTopUpAmount
                          .split('|')
                          .indexOf(e);

                      addMoneyController.text = appStore.walletPresetTopUpAmount
                          .split("|")[currentIndex];

                    
                    },
                    child: Container(
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        // color: currentIndex ==
                        //         appStore.walletPresetTopUpAmount
                        //             .split('|')
                        //             .indexOf(e)
                        //     ? AppColors.primaryColor(context)
                        //     : AppColors.whiteColor(context),
                        border: Border.all(
                            color: currentIndex ==
                                    appStore.walletPresetTopUpAmount
                                        .split('|')
                                        .indexOf(e)
                                ? Colors.grey
                                : Colors.grey),
                        borderRadius: BorderRadius.circular(defaultRadius),
                      ),
                      child: Text(
                          appStore.currencyPosition == LEFT
                              ? '${appStore.currencyCode} $e'
                              : '$e ${appStore.currencyCode}',
                          style: boldTextStyle(
                              color: currentIndex ==
                                      appStore.walletPresetTopUpAmount
                                          .split('|')
                                          .indexOf(e)
                                  ? Colors.white
                                  : AppColors.primaryColor(context))),
                    ),
                  );
                }).toList(),
              ),
              SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Instant pay",
                    style: AppTextStyles.header(),
                  ),
                  ValueListenableBuilder<bool>(
                    valueListenable: _isInstantPay,
                    builder: (context, value, child) {
                      return Checkbox(
                          value: value,
                          onChanged: (value) {
                            if (value == true) {
                              GlobalMethods.showConfirmationDialog(
                                  context: context,
                                  onPositiveAction: () {
                                    _isInstantPay.value = value ?? false;
                                  },
                                  title:
                                      "Are you sure you want to instant pay ? Amount will be deducted incase of instant pay.");
                            } else {
                              _isInstantPay.value = value ?? false;
                            }
                          });
                    },
                  )
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: AppButton(
                      text: language.cancelTxt,
                      width: MediaQuery.of(context).size.width,
                      onPressed: () async {
                        Navigator.pop(context);
                      },
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: AppButton(
                      text: language.withDraw,
                      width: MediaQuery.of(context).size.width,
                      onPressed: () async {
                        if (addMoneyController.text.isNotEmpty) {
                          if (num.parse(addMoneyController.text) >
                              totalAmount) {
                            GlobalMethods.errorToast(context,
                                "Amount can not be greater than wallet balance");
                            return;
                          } else if (num.parse(addMoneyController.text) < 10) {
                            GlobalMethods.errorToast(
                                context, "Amount can not be less than AUD 10");
                            return;
                          }
                          await withDrawRequest(
                              amount: double.parse(addMoneyController.text));
                        } else {
                          GlobalMethods.errorToast(
                              context, "Please enter a valid amount");
                        }
                      },
                    ),
                  ),
                ],
              )
            ],
          ),
        );
      },
    );
  }
}
