class AppCred {
  static const _googleMapAPIKey = 'AIzaSyBE1ks4PYI4zjuLEph1qCo5201O2XKrqM8';

  static const mapBoxPublicTokenKey =
      'pk.eyJ1Ijoicm9vby1hdSIsImEiOiJjbTVxZWVnbzIwYnI4MmpxN3JwcXNqNmh3In0.NDxJ9Dy56Dh2fuMmLZXqtA';

  static const LIVE_URL =
      'https://software.rooo.com.au'; // Don't add slash at the end of the url

  static const TESTING_URL = "https://development.rooo.com.au";

  // static const baseUrl = "$LIVE_URL/api/";
  static const baseUrl = "$TESTING_URL/api/";

// late final MqttServerClient client;

  static const oneSignalAppIdRider = '************************************';
  static const oneSignalRestKeyRider =
      '************************************************';

  static const zegoAppId = 697103749;
  static const zegoAppSign =
      '2f51d4c8da2997643f81c52fa7cf43ee5f5e2636752a4474115ff7ec666a7074';

  static const mOneSignalAppIdDriver = '************************************';
  static const mOneSignalRestKeyDriver =
      '************************************************';

  static const zegoResorceId = "rider_zego_resource_1";
  static const zegoNoUserError = "6000281";

  static String mqttUserName = "hivemq.webclient.1729573412945";
  static String mqttPassword = "3cNh4p\$n>5LAoQ9,#gSB";
  static String mqttHost = "roooaustralia-bqzjha.a03.euc1.aws.hivemq.cloud";
}
