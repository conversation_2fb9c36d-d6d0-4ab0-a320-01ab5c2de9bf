import 'package:audioplayers/audioplayers.dart';
import 'package:rooo_driver/background_tasks/LocalNotifications.dart';

class BackgroundAudio {
  static bool _isPlayingNewRideAudio = false;
  static late final player;

  static Future<void> init() async {
    await AudioPlayer.global.setAudioContext(
      AudioContextConfig(focus: AudioContextConfigFocus.duckOthers).build(),
    );
    player = AudioPlayer();
  }

  static Future<void> playNewRideAudio() async {
    try {
      if (_isPlayingNewRideAudio) {
        return;
      }
      // await player.play(AssetSource('audio/new_ride.mp3'));
      // _isPlayingNewRideAudio = true;
      print("audio-> playing new ride audio");
    } catch (e) {
      print("audio-> error playing new ride audio $e");
    }
  }

  static Future<void> playOfflineAudio() async {
    try {
      await player.play(AssetSource('audio/offline.mp3'));
      print("audio-> playing offline audio");
    } catch (e) {
      print("audio-> error playing offline audio $e");
    }
  }

  static Future<void> playErrorAudio() async {
    try {
      await player.play(AssetSource('audio/error.mp3'));
      print("audio-> playing error audio");
    } catch (e) {
      print("audio-> error playing error audio $e");
    }
  }

  static Future<void> playOnlineAudio() async {
    try {
      await player.play(AssetSource('audio/online.mp3'));
      print("audio-> playing online audio");
    } catch (e) {
      print("audio-> error playing online audio $e");
    }
  }
  static Future<void> stopAudio() async {
    try {
      LocalNotifications.cancelNewRideNotifications();
      await player.stop();
      print("audio-> stopping audio");
    } catch (e) {
      print("audio-> error stopping audio $e");
    }
  }
}
