import 'package:disable_battery_optimization/disable_battery_optimization.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class BatteryOptimizationsService {
  static bool _isNativeCase = false;

  static Future<void> initPrompt() async {
    bool result = await GlobalMethods.showConfirmationDialog(
      context: navigatorKey.currentContext!,
      positiveText: "Yes",
      negativeText: "No",
      onPositiveAction: () {},

      onNegativeAction: () {},

      title:
          "Please disable battery optimizations to allow the app to function properly in the background.\nDo you want to disable battery optimizations?",
    );
    if (result) {
      promptUserToDisableOptimizations();
    }
  }

  static Future<bool> areAllOptimizationsDisabled() async {
    if (Platform.isAndroid) {
      bool checker =
          await DisableBatteryOptimization.isBatteryOptimizationDisabled ??
          true;

      if (!checker) {
        _isNativeCase = true;
        return false;
      }

      checker =
          await DisableBatteryOptimization
              .isManufacturerBatteryOptimizationDisabled ??
          true;
      return checker;
    }
    return true;
  }

  static Future<void> promptUserToDisableOptimizations() async {
    if (_isNativeCase) {
      await DisableBatteryOptimization.showDisableBatteryOptimizationSettings();
    } else {
      await DisableBatteryOptimization.showDisableManufacturerBatteryOptimizationSettings(
        "Please disable battery optimizations",
        "To allow the app to function properly in the background, please disable battery optimizations. Without this, the app may not be able to receive ride requests in the background.",
      );
      ;
    }
  }
}
