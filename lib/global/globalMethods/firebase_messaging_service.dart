import 'package:firebase_messaging/firebase_messaging.dart';

class FMS {
  static bool _init = false;
  static bool _tokenSentToServer = false;
  static String? _token;

  static init() async {
    if (!_init) {
      try {
        _token = await FirebaseMessaging.instance.getToken();
        if (_token != null) {
          _init = true;
          sendTokenToServer();
        }
      } catch (e) {
        //
      }
    }
    if (!_tokenSentToServer) {
      sendTokenToServer();
    }
  }

  static sendTokenToServer() {}
}
