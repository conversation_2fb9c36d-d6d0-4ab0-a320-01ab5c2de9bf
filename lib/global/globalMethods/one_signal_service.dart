import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:rooo_driver/background_tasks/BackgroundTasks.dart';
import 'package:rooo_driver/background_tasks/LocalNotifications.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/network/logs_screen.dart';

class OneSignalService {
  static bool _isInitialised = false;
  static bool _isSentToServer = false;
  static String _fcmToken = "";
  static init() async {
    try {
      /* fcm callback */
      FirebaseMessaging.onMessage.listen((message) async {
        await BackgroundTasks.handleNewRideDataNotification(message);
        await BackgroundTasks.updateDriverStatus(message);
      });
      _fcmToken =
          await FirebaseMessaging.instance
              .getToken()
              .then((v) => v)
              .catchError((onError) => "") ??
          "";

      FirebaseMessaging.instance.onTokenRefresh.listen((token) async {
        _fcmToken = token;
        // if(Platform.isIOS)
        // {
        //   await FirebaseMessaging.instance.getAPNSToken().then((value) {
        //     _fcmToken = value ?? "";
        //   });
        // }
        await updatePlayerId(
          playerId: GlobalState.playerId,
          timezone: await FlutterTimezone.getLocalTimezone(),
          fcmtoken: _fcmToken,
        );
      });

      if (!_isInitialised) {
        OneSignal.initialize(AppCred.mOneSignalAppIdDriver);
        OneSignal.Notifications.addForegroundWillDisplayListener((event) {
          if (GlobalState.lastNotificationId !=
              event.notification.notificationId) {
            GlobalState.lastNotificationId = event.notification.notificationId;

            GlobalMethods.handleNotification(event.notification, false);
          }
        });

        OneSignal.Notifications.addClickListener((event) {
          GlobalMethods.handleNotification(event.notification, true);
        });
        _isInitialised = true;
      }

      if (!_isSentToServer) {
        OneSignal.User.pushSubscription.addObserver((observer) async {
          if (_isSentToServer) {
            return;
          }
          final String currentTimeZone =
              await FlutterTimezone.getLocalTimezone();
          String? id = observer.current.id;
          if ((id ?? "").trim().isNotEmpty) {
            GlobalState.playerId = id!;
            var response = await updatePlayerId(
              playerId: GlobalState.playerId,
              timezone: currentTimeZone,
              fcmtoken: _fcmToken,
            );
            _isSentToServer = response.status && _fcmToken.isNotEmpty;
            /* create notification channel */
            if (Platform.isAndroid) {
              await LocalNotifications.instance
                  .resolvePlatformSpecificImplementation<
                    AndroidFlutterLocalNotificationsPlugin
                  >()!
                  .createNotificationChannel(
                    AndroidNotificationChannel(
                      "new_ride_channel",
                      "New Rides",
                      description: 'Get new ride request alerts',
                      importance: Importance.max,
                      playSound: true,
                      sound: RawResourceAndroidNotificationSound('new_ride'),
                    ),
                  );
            }
            OneSignal.User.addTagWithKey(
              "user_id",
              sharedPref.getInt(USER_ID).toString(),
            );
            if (areLogsEnabled) {
              appLogs.add(
                "Player id sent to server on " + DateTime.now().toString(),
              );
            }
          }
        });

        await OneSignal.User.pushSubscription.optIn();

        if (!_isSentToServer) {
          String? id = await OneSignal.User.pushSubscription.id;

          if ((id ?? "").trim().isNotEmpty) {
            final String currentTimeZone =
                await FlutterTimezone.getLocalTimezone();

            GlobalState.playerId = id!;
            var response = await updatePlayerId(
              playerId: GlobalState.playerId,
              timezone: currentTimeZone,
              fcmtoken: _fcmToken,
            );
            _isSentToServer = response.status && _fcmToken.isNotEmpty;
            OneSignal.User.addTagWithKey(
              "user_id",
              sharedPref.getInt(USER_ID).toString(),
            );
            if (areLogsEnabled) {
              appLogs.add(
                "Player id sent to server at " + DateTime.now().toString(),
              );
            }
          }
        }
      }
      return OneSignalServiceStatus.initialised;
    } catch (e) {
      return OneSignalServiceStatus.initError;

      //
    }
  }

  static Future<void> dispose() async {
    try {
      _isInitialised = false;
      _isSentToServer = false;
      OneSignal.Notifications.clearAll();
      OneSignal.User.pushSubscription.optOut();
      OneSignal.logout();
    } catch (e) {
      //
    }
  }
}

enum OneSignalServiceStatus { permissionDenied, initError, initialised }
