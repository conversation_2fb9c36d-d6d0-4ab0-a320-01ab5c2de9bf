import 'dart:io';

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:zego_uikit/zego_uikit.dart';
import 'package:zego_uikit_prebuilt_call/zego_uikit_prebuilt_call.dart';
import 'package:zego_uikit_signaling_plugin/zego_uikit_signaling_plugin.dart';

class ZegoVoiceCallService {
  static ValueNotifier<int> isinitialized = ValueNotifier(0);
  static bool _isMicrophonePermissionGranted = false;
  static bool _isSystemAlertPermissionGranted = false;

  static Future<ZegoVoiceCallServiceStatus> call() async {
    if (!_isMicrophonePermissionGranted) {
      if (!(await _checkMicrophonePermissions())) {
        return ZegoVoiceCallServiceStatus.microphonePermissionDenied;
      }
    }
    if (Platform.isAndroid) {
      if (!_isSystemAlertPermissionGranted) {
        if (!(await _checkSystemAlertPermissions())) {
          return ZegoVoiceCallServiceStatus.systemAlertPermissionDenied;
        }
      }
    }

    if (isinitialized.value == 1) {
      return ZegoVoiceCallServiceStatus.initialised;
    }

    return ZegoVoiceCallServiceStatus.initError;
  }


  static Future<bool> _checkMicrophonePermissions() async {
    PermissionStatus status = await Permission.microphone.status;

    if (status != PermissionStatus.granted) {
      status = await Permission.microphone.request();
    }
    _isMicrophonePermissionGranted = (status == PermissionStatus.granted);
    return _isMicrophonePermissionGranted;
  }

  static Future<bool> _checkSystemAlertPermissions() async {
    PermissionStatus status = await Permission.systemAlertWindow.status;

    if (status != PermissionStatus.granted) {
      status = await Permission.systemAlertWindow.request();
    }
    _isSystemAlertPermissionGranted = (status == PermissionStatus.granted);
    return _isSystemAlertPermissionGranted;
  }

  static Future<ZegoVoiceCallServiceStatus> init({
    required GlobalKey<NavigatorState> navigatorKey,
    required int appId,
    required String appSign,
    required String callerId,
    required String callerName,
  }) async {
    ZegoUIKitPrebuiltCallInvitationService().setNavigatorKey(navigatorKey);

    ZegoUIKitPrebuiltCallInvitationService().useSystemCallingUI([
      ZegoUIKitSignalingPlugin(),
    ]);

    await ZegoUIKitPrebuiltCallInvitationService()
        .init(
          appID: appId,
          appSign: appSign,
          userID: callerId,
          userName: callerName,
          plugins: [ZegoUIKitSignalingPlugin()],
          requireConfig: (data) {
            var config = ZegoUIKitPrebuiltCallConfig.oneOnOneVoiceCall();
            config.topMenuBar.isVisible = true;
            config.topMenuBar.hideAutomatically = false;
            config.topMenuBar.hideByClick = false;
            config.topMenuBar.buttons.insert(
              0,
              ZegoCallMenuBarButtonName.minimizingButton,
            );
            config.bottomMenuBar.margin = const EdgeInsets.only(bottom: 100);
            config.bottomMenuBar.hideAutomatically = false;
            config.bottomMenuBar.hideByClick = false;

            return config;
          },
        )
        .catchError((onError) {
          isinitialized.value = -1;

          return ZegoVoiceCallServiceStatus.initError;
        });
    isinitialized.value = 1;
    return ZegoVoiceCallServiceStatus.initialised;
  }

  static Future<void> dispose() async {
    try {
      ZegoUIKitPrebuiltCallInvitationService().uninit();
    } catch (e) {
      //
    }
  }
}

enum ZegoVoiceCallServiceStatus {
  microphonePermissionDenied,
  systemAlertPermissionDenied,
  initError,
  initialised,
}
