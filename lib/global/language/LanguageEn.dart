import 'BaseLanguage.dart';

class LanguageEn extends BaseLanguage {
  @override
  String get appName => 'Driver';

  @override
  String get welcomeBack => 'Welcome Back !';

  @override
  String get signInYourAccount => 'Sign in your account';

  @override
  String get thisFieldRequired => 'This field is required';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get logIn => 'Log In';

  @override
  String get orLogInWith => 'Or LogIn With';

  @override
  String get donHaveAnAccount => 'Don\'t have an account?';

  @override
  String get signUp => 'Sign Up';

  @override
  String get createAccount => 'Create Account';

  @override
  String get createYourAccountToContinue => 'Create your account to continue';

  @override
  String get firstName => 'First Name';

  @override
  String get lastName => 'Last Name';

  @override
  String get userName => 'Username';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get alreadyHaveAnAccount => 'Already have an account?';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get purchase => 'Purchase';

  @override
  String get changePassword => 'Change Password';

  @override
  String get oldPassword => 'Old Password';

  @override
  String get newPassword => 'New Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get passwordDoesNotMatch => 'Password does not match';

  @override
  String get passwordInvalid => 'Minimum password length should be 8';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get writeMessage => 'Write Message....';

  @override
  String get enterTheEmailAssociatedWithYourAccount =>
      'Enter the email associated with your account';

  @override
  String get submit => 'Submit';

  @override
  String get language => 'Language';

  @override
  String get notification => 'Notification';

  @override
  String get otpVeriFiCation => 'OTP VeriFiCation';

  @override
  String get weHaveSentDigitCode => 'We have sent a 4 digit code';

  @override
  String get contactLength =>
      'Contact number length must be of 10 or 12 digits.';

  @override
  String get about => 'About';

  @override
  String get useInCaseOfEmergency => 'USE IN CASE OF EMERGENCY';

  @override
  String get notifyAdmin => 'Notify Admin';

  @override
  String get notifiedSuccessfully => 'Notified Successfully';

  @override
  String get complain => 'Complain';

  @override
  String get pleaseEnterSubject => 'Please enter subject';

  @override
  String get writeDescription => 'Write a description....';

  @override
  String get saveComplain => 'Save Complain';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get gender => 'Gender';

  @override
  String get addressTxt => 'Address';

  @override
  String get updateProfile => 'Update Profile';

  @override
  String get notChangeUsername =>
      'You can change username by clicking on edit button';

  @override
  String get notChangeEmail =>
      'You can Change email id by clicking on edit button';

  @override
  String get profileUpdateMsg => 'Profile updated successfully';

  @override
  String get emergencyContact => 'Emergency Contact';

  @override
  String get areYouSureYouWantDeleteThisNumber =>
      'Are you sure you want delete this number?';

  @override
  String get addContact => 'Add Contact';

  @override
  String get googleMap => 'Google Map';

  @override
  String get save => 'Save';

  @override
  String get myRides => 'Rides';

  @override
  String get myWallet => 'My Wallet';

  @override
  String get availableBalance => 'Available Balance';

  @override
  String get recentTransactions => 'Recent Transactions';

  @override
  String get moneyDeposited => 'Money Deposited';

  @override
  String get addMoney => 'Add Money';

  @override
  String get cancelTxt => 'Cancel';

  @override
  String get pleaseSelectAmount => 'Please select amount';

  @override
  String get amount => 'Amount';

  @override
  String get capacity => 'Capacity';

  @override
  String get paymentMethod => 'Payment Method';

  @override
  String get chooseYouPaymentLate => 'Choose you payment now or late';

  @override
  String get enterPromoCode => 'Enter Promo Code';

  @override
  String get confirm => 'Confirm';

  @override
  String get forInstantPayment => 'For Instant Payment';

  @override
  String get bookNow => 'Book Now';

  @override
  String get wallet => 'Wallet';

  @override
  String get paymentDetail => 'Payment Detail';

  @override
  String get rideId => 'Ride Id';

  @override
  String get createdAt => 'Created at';

  @override
  String get viewHistory => 'View History';

  @override
  String get paymentDetails => 'Payment Details';

  @override
  String get paymentType => 'Payment Type';

  @override
  String get paymentStatus => 'Payment Status';

  @override
  String get priceDetail => 'Price Detail';

  @override
  String get basePrice => 'Base Price';

  @override
  String get distancePrice => 'Distance Price';

  @override
  String get timePrice => 'Time Price';

  @override
  String get waitTime => 'Wait Time';

  @override
  String get extraCharges => 'Extra Charges';

  @override
  String get couponDiscount => 'Coupon Discount';

  @override
  String get total => 'Total';

  @override
  String get payment => 'Payment';

  @override
  String get cash => 'Cash';

  @override
  String get updatePaymentStatus => 'Update Payment Status';

  @override
  String get waitingForDriverConformation =>
      'WAITING FOR RIDER TO CONFORMATION';

  @override
  String get continueNewRide => 'Continue new ride';

  @override
  String get payToPayment => 'Pay to payment';

  @override
  String get tip => 'Tip';

  @override
  String get pay => 'Pay';

  @override
  String get howWasYourRide => 'How was your Ride?';

  @override
  String get wouldYouLikeToAddTip => 'Would you like to add tip?';

  @override
  String get addMoreTip => 'Add More Tip';

  @override
  String get addMore => 'Add More';

  @override
  String get addReviews => 'Add Reviews';

  @override
  String get writeYourComments => 'Write your reviews....';

  @override
  String get continueD => 'Continue';

  @override
  String get detailScreen => 'Ride detail';
  @override
  String get helpdetail => 'Help detail';

  @override
  String get aboutDriver => 'About Driver';

  @override
  String get rideHistory => 'Ride History';

  @override
  String get myProfile => 'My Profile';

  @override
  String get myTrips => 'My Trips';

  @override
  String get emergencyContacts => 'Emergency Contacts';

  @override
  String get logOut => 'Logout';

  @override
  String get areYouSureYouWantToLogoutThisApp =>
      'Are you sure you want to logout?';

  @override
  String get whatWouldYouLikeToGo => 'What would you like to go?';

  @override
  String get enterYourDestination => 'Enter your destination';

  @override
  String get currentLocation => 'Destination location';

  @override
  String get destinationLocation => 'Destination location';

  @override
  String get chooseOnMap => 'Choose on map';

  @override
  String get profile => 'Profile';

  @override
  String get theme => 'Theme';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get termsConditions => 'Terms & Conditions';

  @override
  String get aboutUs => 'About Us';

  @override
  String get lookingForNearbyDrivers => 'Looking for nearby drivers';

  @override
  String get weAreLookingForNearDriversAcceptsYourRide =>
      'We are looking for nearby drivers to\naccepts your ride';

  @override
  String get areYouSureYouWantToCancelThisRide =>
      'Are you sure you want to cancel this ride?';

  @override
  String get serviceDetail => 'Service Detail';

  @override
  String get get => 'Get';

  @override
  String get rides => 'Rides';

  @override
  String get people => 'People';

  @override
  String get fare => 'Fare';

  @override
  String get done => 'Done';

  @override
  String get availableOffers => 'Available Offers';

  @override
  String get off => 'Off';

  @override
  String get sendOTP => 'Send verification code';

  @override
  String get otpVerification => 'Otp Verification';

  @override
  String get enterTheCodeSendTo => 'Enter The Code SendTo';

  @override
  String get didNotReceiveTheCode => 'Did not Receive The Code';

  @override
  String get resend => 'Resend';

  @override
  String get drivingExperience => 'Driving Experience (Years)';

  @override
  String get sos => 'SOS';

  @override
  String get driverReview => 'Driver Review';

  @override
  String get signInUsingYourMobileNumber => 'Sign In Using Your\nMobile Number';

  @override
  String get otp => 'Verification code';

  @override
  String get newRideRequested => 'New Ride Requested';

  @override
  String get accepted => 'Accepted';

  @override
  String get arriving => 'Arriving';

  @override
  String get arrived => 'Arrived';

  @override
  String get inProgress => 'In Progress';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get completed => 'Completed';

  @override
  String get pleaseEnableLocationPermission =>
      'Please enable location Permission';

  @override
  String get pending => "Pending";

  @override
  String get failed => "Failed";

  @override
  String get paid => "Paid";

  @override
  String get male => "Male";

  @override
  String get female => "Female";

  @override
  String get other => "Other";

  @override
  String get addExtraCharges => "Add Extra Charges";

  @override
  String get enterAmount => "Enter amount";

  @override
  String get pleaseAddedAmount => "Please added amount";

  @override
  String get title => "Title";

  @override
  String get charges => "Charges";

  @override
  String get saveCharges => "Save Charges";

  @override
  String get bankDetail => 'Bank Detail';

  @override
  String get bankName => 'Bank Name';

  @override
  String get bankCode => 'Bank Code';

  @override
  String get accountHolderName => 'Account Holder Name';

  @override
  String get accountNumber => 'Account Number';

  @override
  String get updateBankDetail => 'Update Bank Detail';

  @override
  String get addBankDetail => 'Add Bank Detail';

  @override
  String get bankInfoUpdateSuccessfully => 'Bank info update successfully';

  @override
  String get vehicleDetail => 'Vehicle Detail';

  @override
  String get document => 'Documents';

  @override
  String get setting => 'Settings';

  @override
  String get youAreOnlineNow => "You're online now";

  @override
  String get youAreOfflineNow => "You're offline now";

  @override
  String get requests => 'Requests';

  @override
  String get areYouSureYouWantToCancelThisRequest =>
      'Are you sure you want to cancel this request?';

  @override
  String get decline => 'Decline';

  @override
  String get accept => 'Accept';

  @override
  String get areYouSureYouWantToAcceptThisRequest =>
      'Are you sure you want to accept this request?';

  @override
  String get call => 'Call';

  @override
  String get chat => 'Chat';

  @override
  String get applyExtraFree => 'Apply extra free?';

  @override
  String get areYouSureYouWantToArriving =>
      'Are you sure you want to mark arriving?';

  @override
  String get areYouSureYouWantToArrived => 'Are you sure you have arrived ?';

  @override
  String get enterOtp => 'Enter verification code';

  @override
  String get enterTheOtpDisplayInCustomersMobileToStartTheRide =>
      'Enter the verification code display in customers mobile to start the ride';

  @override
  String get enterTheOtpDisplayInCustomersMobileToEndTheRide =>
      'Enter the verification code display in customers mobile to end the ride';

  @override
  String get pleaseEnterValidOtp => 'Please enter valid verification code';

  @override
  String get areYouSureYouWantToCompletedThisRide =>
      'Are you sure you want to completed this ride?';

  @override
  String get updateBankInfo => 'Update Bank Info';

  @override
  String get regisTRation => 'Registration';

  @override
  String get pleaseSelectVehiclePreferences =>
      'Please Select Vehicle Preferences';

  @override
  String get userDetail => 'ROOO Detail';

  @override
  String get selectVehiclePreferences => 'Select Vehicle Preferences';

  @override
  String get selectGender => 'Select Gender';

  @override
  String get age => 'Age';

  @override
  String get socialSecurityNumber => 'Social Security Number';

  @override
  String get nightDrivingPreference => 'Night Driving Preference';

  @override
  String get withDraw => 'Withdraw';

  @override
  String get withdrawHistory => 'Withdraw History';

  @override
  String get approved => 'Approved';

  @override
  String get requested => 'Requested';

  @override
  String get updateVehicle => 'Update Vehicle';

  @override
  String get userNotApproveMsg =>
      'Your profile is under review. Wait some time or contact to your administrator.';

  @override
  String get uploadFileConfirmationMsg =>
      'Are you sure you want to upload this file?';

  @override
  String get selectDocument => 'Select Document';

  @override
  String get addDocument => 'Add Document';

  @override
  String get areYouSureYouWantToDeleteThisDocument =>
      'Are you sure you want to delete this document?';

  @override
  String get expireDate => 'Expire Date';

  @override
  String get goDashBoard => 'Go to DashBoard';

  @override
  String get deleteAccount => 'Delete Account';

  @override
  String get account => 'Account ';

  @override
  String get areYouSureYouWantPleaseReadAffect =>
      'Are you sure you want to delete your account? Please read how account deletion will affect.';

  @override
  String get deletingAccountEmail =>
      'Deleting your account removes personal information from our database. Your email becomes permanently reserved and same email cannot be re-used to register a new account';

  @override
  String get areYouSureYouWantDeleteAccount =>
      'Are you sure you want to delete Account?';

  @override
  String get yourInternetIsNotWorking => 'Your Internet is not working';

  @override
  String get allow => 'Allow';

  @override
  String get mostReliableMightyDriverApp => 'Most Reliable ROOO Driver App';

  @override
  String get toEnjoyYourRideExperiencePleaseAllowPermissions =>
      'To enjoy your ride experience\nPlease allow us the following permissions';

  @override
  String get cashCollected => 'Cash Collected';

  @override
  String get areYouSureCollectThisPayment =>
      'Are you sure collect this payment?';

  @override
  String get txtURLEmpty => "URL is empty";

  @override
  String get lblFollowUs => "Follow Us";

  @override
  String get bankInfo => "Bank Info";

  @override
  String get duration => 'Duration';

  @override
  String get paymentVia => 'Payment via';

  @override
  String get moneyDebit => 'Money Debit';

  @override
  String get roooCare => 'ROOO Care';

  @override
  String get demoMsg => 'Tester role not allowed to perform this action';

  @override
  String get youCannotChangePhoneNumber =>
      'You can change Phone Number by clicking on edit button';

  @override
  String get offLine => 'Offline';

  @override
  String get online => 'Online';

  @override
  String get walletLessAmountMsg =>
      'You can\'t ride because you have less money in your wallet. So you have to add money to your wallet so that you can ride later.';

  @override
  String get aboutRider => 'About Rider';

  @override
  String get pleaseEnterMessage => 'Please Enter Message';

  @override
  String get complainList => 'Complain List';

  @override
  String get viewAll => 'View All';

  @override
  String get pleaseSelectRating => 'Please select rating';

  @override
  String get serviceInfo => 'Service Info';

  @override
  String get youCannotChangeService => 'You Cannot Change service';

  @override
  String get vehicleInfoUpdateSucessfully => 'Vehicle info update sucessfully';

  @override
  String get subscription => 'Subscription';

  @override
  String get yourCurrentBalanceIs => 'Your current balance is:';

  @override
  String get yourSubscriptionPlanIsOver =>
      'Your subscription plan is over. Please subscribe to use this application';

  @override
  String get perDay => 'PER DAY';

  @override
  String get renew => 'Renew';

  @override
  String get yourWalletDoNotHaveEnoughBalance =>
      'Your wallet do not have enough balance. Please add balance in Wallet then Renew your subscription plan.';

  @override
  String get addWallet => 'Add Wallet';

  @override
  String get yourDailyAppUseLimitHasBeenExpired =>
      'Your daily app use limit has been expired. Tap to recharge and use the app.';

  @override
  String get recharge => 'Recharge';

  @override
  String get isMandatoryDocument => '* is a mandatory document.';

  @override
  String get someRequiredDocumentAreNotUploaded =>
      'Some required document are not uploaded. Please upload all required documents.';

  @override
  String get areYouCertainOffline => 'Are you certain you want to go offline?';

  @override
  String get areYouCertainOnline => 'Are you certain you want to go online?';

  @override
  String get pleaseAcceptTermsOfServicePrivacyPolicy =>
      'Please accept Terms of service & Privacy Policy';

  @override
  String get rememberMe => 'Remember Me';

  @override
  String get agreeToThe => 'I agree to the';

  @override
  String get invoice => 'Invoice';

  @override
  String get riderInformation => 'RIDER INFORMATION';

  @override
  String get customerName => 'Customer Name';

  @override
  String get sourceLocation => 'Source Location';

  @override
  String get invoiceNo => 'Invoice No';

  @override
  String get invoiceDate => 'Invoice Date';

  @override
  String get orderedDate => 'Ordered Date';

  @override
  String get totalCash => 'Total Cash';

  @override
  String get totalRide => 'Total ride amount';

  @override
  String get totalWallet => 'Total Wallet';

  @override
  String get totalEarning => 'Total Earnings';

  @override
  String get pleaseSelectFromDateAndToDate =>
      'Please select from date and to date';

  @override
  String get from => 'From';

  @override
  String get fromDate => 'From Date';

  @override
  String get to => 'To';

  @override
  String get toDate => 'To Date';

  @override
  String get ride => 'Ride';

  @override
  String get todayRide => 'Today Ride';

  @override
  String get weeklyOrderCount => 'Weekly Order Count';

  @override
  String get distance => 'Distance';

  @override
  String get rideInformation => 'Ride Information';

  @override
  String get iAgreeToThe => 'I agree to the';

  @override
  String get today => 'Today';

  @override
  String get weekly => 'Weekly';

  @override
  String get report => 'Report';

  @override
  String get earning => 'Earnings';

  @override
  String get todayEarning => 'Today Earnings';

  @override
  String get available => 'Available';

  @override
  String get notAvailable => 'Not Available';

  @override
  String get youWillReceiveNewRidersAndNotifications =>
      'You will receive new riders and notifications';

  @override
  String get youWillNotReceiveNewRidersAndNotifications =>
      'You will not receive new riders and notifications';

  @override
  String get yourAccountIs => 'Your account is';

  @override
  String get pleaseContactSystemAdministrator =>
      'Please contact system administrator';

  @override
  String get youCanNotThisActionsPerformBecauseYourCurrentRideIsNotCompleted =>
      'You can not this actions perform,because your current ride is not completed';

  @override
  String get selectDisplayPicture => 'Select profile picture';

  @override
  String get invalidAge => 'Age must be 18 years and older';
  //   \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
  @override
  String get go => "Go";

  @override
  String get inbox => "Inbox";

  @override
  String get referralCompleted => 'REFERRAL COMPLETED';

  @override
  String get referralEarning => 'REFERRAL EARNING';

  @override
  String get continueText => 'Continue';

  @override
  String get enterYourMobileNumber => 'Enter your mobile number';

  @override
  String get orText => 'OR';

  @override
  String get signInWithFacebook => 'Sign in with facebook';

  @override
  String get signInWithGoogle => 'Sign up with Google';

  // ////////////////////////////////////////////

  @override
  String get inBoxTxt => 'Inbox';

  @override
  String get referralsTxt => 'Referrals';
  @override
  String get opportunityTxt => 'Opportunities';
  @override
  String get accountTxt => 'Account';
  @override
  String get earningTxt => 'Earnings';
  @override
  String get helpTxt => 'Help';
  @override
  String get learningCenterTxt => 'Learning Center';

  @override
  String get completedTxt => 'Completed';
  @override
  String get faqsTxt => 'FAQs';
  @override
  String get settingTxt => 'Settings';
  @override
  String get nightModeTxt => 'Night Mode';
  @override
  String get helpWithTripTxt => 'Help with a trip';

  @override
  String get verifyOTP => "Verify";

  @override
  String get verifyOTPHeading => 'Enter the 6 digit code sent to your number';

  @override
  String get enterOTP => 'Please enter the verification code';

  @override
  String get invalidOTP => 'Invalid verification code';

  @override
  String get referralCode => 'Referral code';
  ///////////////////////
  //////////////////////
  ///////////////////////
  ///////////////////////
  @override
  String get allMessageTxt => 'All messages';

  @override
  String get bankInfoTxt => "Bank info";
  @override
  String get offerTxt => "Offer";
  @override
  String get statusTxt => "Status";

  @override
  String get balanceTxt => "Balance";
  @override
  String get paymentScheduledTxt => "Payout scheduled";
  @override
  String get paymentMethodsTxt => "Payment methods";

  @override
  String get cashOutTxt => "Cash out";

  @override
  String get walletTxt => "Wallet";

  @override
  String get verifyPhoneMsg => 'Please verify your phone';

  @override
  String get errorMsg => 'Something went wrong';

  //   @override
  // String get learningCenterTxt => 'Learning Center';

  //     @override
  // String get inBoxTxt => 'Inbox';

  //   @override
  // String get referralsTxt => 'Referrals';
  //   @override
  // String get opportunityTxt => 'Opportunity';
  //   @override
  // String get accountTxt => 'Account';
  //   @override
  // String get earningTxt => 'Earnings';
  //   @override
  // String get helpTxt => 'Help';
  //   @override
  // String get learningCenterTxt => 'Learning Center';

  @override
  String get SorryYouDontHaveAnyOpportunitiesTxt =>
      "Sorry you don't have any opportunities";
  @override
  String get NewOpportunitiesAreComingTxt => "New opportunities are coming";

  @override
  String get stayTunedTxt => "stay tuned";
  @override
  String get roooCareTxt => "ROOO Care";
  @override
  String get NotificationsTxt => "Notifications";
  @override
  String get WeeklyOrderCountTxt => "Weekly Order Count";
  @override
  String get cashoutTxt => "Cashout";
  @override
  String get TotalCashTxt => "Total Cash";
  @override
  String get TotalWalletTxt => "Total Wallet";
  @override
  String get TotalRideTxt => "Total Ride";
  @override
  String get adminNotifiedTxt => "admin notified";

  @override
  String get YouAreOnlineNowTxt => "You are Online now";

  @override
  String get AreYouSureYouWantToNotifyAdminTxt =>
      "Are you sure you want to notify admin";

  @override
  String get PleaseWaitForTimerTxt => "Please Wait for timer";

  @override
  String get NotifiedAdminTxt => "Notified Admin";

  @override
  String get BookYourroooIn3EasyStepsTxt => "Book Your ROOO in 3 easy steps";

  @override
  String get NowBookingYourroooIsEasyTxt => "Now Booking your rooo is easy!";

  @override
  String get PleaseUploadAllTheDocumentsAndWaitForVerificationTxt =>
      "Please Upload all the documents and wait for verification";

  @override
  String get OkTxt => "Ok";

  @override
  String get goOfflineTxt => "GO OFFLINE";

  @override
  String get goTxt => "GO";

  @override
  String get clickCarPhotographsTxt => "Click car photographs";

  @override
  String get frontTxt => "Front";
  @override
  String get backTxt => "Back";
  @override
  String get leftTxt => "Left";
  @override
  String get rightTxt => "Right";

  @override
  String get WelcomeTxt => "Welcome";

  @override
  String get UserTxt => "User";

  @override
  String get pleaseUploadImageProperlyTxt => "Please upload all images";

  @override
  String get IhaventRecievedACodeTxt => "I haven't recieved a code";
  @override
  String get CONTINUETxt => "CONTINUE";
  @override
  String get NotUploadedTxt => "Not Uploaded";
  @override
  String get CompletedTxt => "Completed";
  @override
  String get rejectedTxt => "Rejected";
  @override
  String get pendingTxt => "Pending";
  @override
  String get resendOTPTxt => "Resend verification code";
  @override
  String get uploadTxt => "Upload";

  @override
  String get accountHolderNameTxt =>
      "Account holder name (exactly as it looks on your bank statements)";
  @override
  String get pleaseEnterTheBenificieryNameTxt =>
      "Please enter the beneficiery name on a bank account, If a account is registered to a business, please put a business name on the account";
  @override
  String get PleaseEnterTheBeneficieryAddressOnABankAccountTxt =>
      "Please enter the beneficiery Address on a bank account";
  @override
  String get cityTxt => "City";
  @override
  String get PleaseEnterYourCityTxt => "Please enter your City";
  @override
  String get postalTxt => "Postal Code";
  @override
  String get PleaseEnterTheBeneficieryPostalCodeTxt =>
      "Please enter the beneficiery postal code on the bank account, If a account is registered to a business, please put the business postal code on the account.";
  @override
  String get DateOfBirthTxt => "Date of birth";
  @override
  String get BankNameTxt => "Bank name";
  @override
  String get BankameOthersTxt => "Bank name (Others)";
  @override
  String get BankOfNovoScotioTxt => "e.g Bank of Nova Scotio(BNS)";
  @override
  String get InstituitionNumberTxt => "Instituition number";
  @override
  String get InstitutionNumberMustContainAtLeastDdigitsTxt =>
      "Institution number must contain atleast 3 digits";
  @override
  String get TransitNumberTxt => "Transit number";
  @override
  String get TransitNumberMustContainAtLeastDigitsTxt =>
      "transit number must contain atleast 5 digits";
  @override
  String get BankAccountNumberTxt => "Bank Account number";
  @override
  String get AccountNumberMustContainAtLeastDigitsTxt =>
      "Account number must contain atleast 7 digits";
  @override
  String get FillCorrectAccountNumberTxt => "Fill Correct account number";
  @override
  String get ReEnterBankAccountNumberTxt => "Re-enter Bank Account number";
  @override
  String get PleaseReEnterYourAccountNumberTxt =>
      "Please re-enter your account number so that we can validate the account number you ghave filled in";
  @override
  String get TermsTxt => "Terms";
  @override
  String get ShareYourLinkTxt => 'Share your link';
  @override
  String get ReEnteredAccountIsWrongTxt => "Re-entered password is wrong";

  @override
  String get waitingTime => "Waiting time";

  @override
  String get waitingTimeOverTxt => "waiting time over";

  @override
  String get cancelTimerTxt => "End timer";

  @override
  String get wouldYouLikeToStartIdleTimeTxt =>
      "Would you like to start idle time";

  String get aboutUsTxt =>
      "ROOO: Your trusted ride companion. We connect you with top-notch drivers for a seamless and safe journey. Your comfort, our priority";

  @override
  //  : implement closedTxt

  @override
  //  : implement wouldYouLikeToStartIdleTime
  String get referralTxt => "Referrals";

  @override
  //  : implement wouldYouLikeToStartIdleTime
  String get promotionsTxt => "Promotions";

  @override
  //  : implement wouldYouLikeToStartIdleTime
  String get inviteTxt => "Invite";

  @override
  //  : implement wouldYouLikeToStartIdleTime
  String get inviteFromContactsTxt => "Invite From Contacts";
  @override
  //  : implement wouldYouLikeToStartIdleTime
  String get termsApplyTxt => "Terms apply";

  @override
  //  : implement wouldYouLikeToStartIdleTime
  String get totalReferralsTxt => "Total Referrals";

  @override
  //  : implement closedTxt
  String get closedTxt => "Closed";
  @override
  //  : implement closedTxt
  String get AdminalreadynotifiedTxt => "Admin already notified";

  @override
  //  : implement closedTxt
  String get PleasewaitTxt => "Please wait";
  @override
  //  : implement closedTxt
  String get NotifyToAdminAboutLateRideTxt => "Notify admin about late ride";
  @override
  //  : implement closedTxt
  String get YouHaveReachedTheDestinationTxt =>
      " Have you reached at the destination";
  @override
  //  : implement closedTxt
  String get upload4ImagesWarningTxt =>
      "Please upload images as illustrated, otherwise your account will be temporarily disabled or there might be a permanent ban";
  @override
  //  : implement closedTxt
  String get CodecopiedTxt => "Code copied";
  @override
  //  : implement closedTxt
  String get RideAcceptedTxt => "Ride accepted";

  @override
  //  : implement closedTxt
  String get PostalCodeShouldHave6DigitTxt => "Postal code should have 6 digit";
  @override
  //  : implement closedTxt
  String get LowBalanceTxt => "Low Balance";
  @override
  //  : implement closedTxt
  String get automaticText => "Automatic";
  @override
  //  : implement closedTxt
  String get timeOfDayTxt => "time of day";
  @override
  //  : implement closedTxt
  String get alwaysOnTxt => "Always on";
  @override
  //  : implement closedTxt
  String get AlwaysOffTxt => "Always off";
  @override
  //  : implement closedTxt
  String get UsePhoneSettingsTxt => "Use phone settings";
  @override
  //  : implement closedTxt
  String get PleaseEnterValidDetailsTxt => "Please enter valid details";
  @override
  //  : implement closedTxt
  String get AddTxt => "Add";
  @override
  //  : implement closedTxt
  String get messageTxt => "Message";
////////////////////////////
  ///
  ///
  ///
  ///
  ///
  ///
///////////////////////////////////////

  String get PleaseEnterMobileNumberTxt => "Please enter mobile number";
  @override
  //  : implement closedTxt
  String get InvalidMobileNumberTxt => "Invalid Mobile Number";
  @override
  //  : implement closedTxt
  String get ServerErrorTxt => "Server error";
  @override
  //  : implement closedTxt

  @override
  //  : implement Message
  String get Message => "message";

  @override
  //  : implement closedTxt
  String get OpportunityDetailTxt => "Opportunity detail";
  @override
  //  : implement closedTxt
  String get DistanceTxt => "Distance";
  @override
  //  : implement closedTxt
  String get TimeTxt => "Time";
  @override
  //  : implement closedTxt
  String get PickupLocationTxt => "Pickup Location";

  @override
  String get passCode => 'Passcode';
  @override
  String get TakePhoto => 'Take photo';

  @override
  String get CopyrightText => 'Copyright';

  @override
  String get roooTxt => 'ROOO';

  @override
  String get roooDriverTxt => 'ROOO Driver';

  @override
  String get ChangeEmailTxt => 'Change Email';

  @override
  String get NewEmailTxt => 'New email';

  @override
  String get EditTxt => 'Edit';

  @override
  String get PleaseEnterValidNameTxt => '"Please enter valid name"';

  @override
  String get RequireAdminPermission => 'Require admin permission';

  @override
  String get UnverifiedEmailTxt => 'Unverified Email';

  @override
  String get VerifiedEmailTxt => 'Verified email';

  @override
  String get RequestChangeTxt => 'Request Change';

  @override
  String get ChangeMobileTxt => "Change mobile";

  @override
  String get MobileNumberTxt => 'Mobile Number';

  @override
  String get optionalTxt => 'optional';

  @override
  String get NoResultsFoundTxt => 'No results found';

  @override
  String get PleaseTryDifferentKeywordTxt => "Please try different keyword";

  @override
  String get ManageProfileTxt => "Profile";

  @override
  String get NumberAlreadyExistPleaseEnterOrUpdateNewNumbertxt =>
      'Number already exist, please enter or update new number.';

  @override
  String get WalletHistoryTxt => 'Wallet History';
  @override
  String get DescriptionTxt => "Description";
  @override
  String get ClickAProperProfilePictureWithYourFaceClearAndVisibleProfilePictureCantBeChangedOnceUploadedTxt =>
      "Click a proper profile picture with your face clear and visible, profile picture cannot be changed once uploaded.";

  @override
  String get PleaseUploadImagesAsIllustratedOtherwiseYourAccountWillBeTemporarilyDisabledOrThereMightBeAPermanentBanText =>
      'Please upload images as illustrated, otherwise your account will be temporarily disabled or there might be a permanent ban.';

  @override
  String get enterthePasscodewhichIsDisplayingInTheCustomersMobile =>
      "Enter the Passcode which is displaying in the customer's mobile";

  @override
  // TODO: implement requiredTxt
  String get requiredTxt => "required ";

  @override
  // TODO: implement startNavigationText
  String get startNavigationText => "Start Navigation";

  @override
  // TODO: implement startNavigationText
  String get signInWithApple => "Sign In with apple";

  @override
  String get locationDeniedText1 =>
      "Location permission for " +
      '\n"Allow all the time"\n' +
      "is required for the app's functionality. Without this location permission, new ride booking and current ride functionality will not work.";

  @override
  String get locationDeniedText2 =>
      'You can change the permission \n"Allow all the time" \nfrom the settings';

  @override
  String get deleteText => "Delete";

  @override
  String get updateText => "Update";

  @override
  String get retryText => "Retry";

  @override
  String get AreYouSureWantToPerformThisAction =>
      "Are you sure want to perform this action?";

  @override
  String get DoYouWantToDelete => "Do you want to delete?";

  @override
  String get DoYouWantToUpdate => "Do you want to update?";

  @override
  String get DoYouWantToAdd => "Do you want to add?";

  @override
  String get DoYouWantToAccept => "Do you want to accept?";

  @override
  String get ClickToRetry => "Click to retry";
  @override
  String get NewTripText => "New Trips";
  @override
  String get AcceptedTripText => "Accepted Trips";
  @override
  String get adminDidNotApproved =>
      "Admin did not approve you request please continue ride";

  @override
  String get ImageUploadedSuccessfullyText => "Image uploaded successfully";
  @override
  String get OKText => "OK";
  @override
  String get NotifyAdminAboutLateRiderText => "Notify admin about late rider";
  @override
  String get AdminNotifiedText => "Admin notified";
  @override
  String get CallText => "Call";
  @override
  String get StartRideText => "Start ride";
  @override
  String get GetVerificationCodeText => "Enter Verification code";
  @override
  String get YouRequestedForRideCancellationText =>
      "You requested for ride cancellation";
  @override
  String get RideCanceledText => "Ride cancelled";

  @override
  String get AdminApproveYourRequestPleaseStayOnlineText =>
      "Admin approve your request please stay online";
  @override
  String get YouHaveAlreadyOneRideDriverSafelyText =>
      "You have already one ride, \nDriver safely";
  @override
  String get YouForgotToReviewPleaseGiveYourReviewAndFeedbackAboutRideText =>
      "You forgot to review. \nPlease give your review and feedback about ride";
  @override
  String get KeepYourselfOnlineToGetMoreRidesText =>
      "Keep yourself online to get more rides";
  @override
  String get youHaveCanceledTheRidePleaseWaitForAdminApproval =>
      "You have canceled the ride please wait for admin approval";
  @override
  String get reached => "Reached";

  @override
  String get pleaseCancelWaitingTimer => "Please cancel waiting timer";
  @override
  String get waitingTimeStarted => "Waiting time started";

  @override
  String get waitingTimeEnded => "Waiting time ended";
  @override
  String get newRide => "New Ride ";

  @override
  String get pleaseAcceptTheRide => "Please accept the ride";
  @override
  String get endRide => "End ride";

  @override
  String get careInfo =>
      'If you have any other query or need any support you can email us on';

  @override
  // TODO: implement deleted
  String get deleted => "deleted";

  @override
  // TODO: implement details
  String get details => "details";

  @override
  // TODO: implement pleaeCompleteThisRideFirst
  String get pleaseCompleteThisRideFirst => "Please complete this ride first";

  @override
  // TODO: implement AreYouSureYouWantToEndWaitingTime
  String get AreYouSureYouWantToEndWaitingTime =>
      "Are You Sure You Want To End WaitingTime";

  @override
  // TODO: implement handleMiddimgregionId
  String get handleMiddimgregionId =>
      "Your region data is missing.\nPlease update your region.";

  @override
  // TODO: implement TripEarning
  String get TripEarning => "Trip earnings";

  @override
  String get handleInCompleteProfile =>
      "Please complete your profile before proceeding";
  @override
  String get call_911 => "Call 911";
  @override
  String get enterYourNewEmail => "enter your new email";

  // String get UserTxt => "User";
  @override
  String get invalidReferalCode => "Invalid referal code";
  @override
  String get noSuggestedMessage => "No suggested message";
  @override
  String get pleaseAddDescription => "Please add description";
  @override
  String get pleaseSelectProvinceAndRegionId =>
      "Please select region id and province id";
  @override
  String get sendEmailLinkOn => "Send an email verification link on";
  @override
  String get pleaseSelectProvinceId => "Please select province id";
  @override
  String get pleaseSelectRegionId => "Please select region id";
  @override
  String get provinceId => "Province Id";
  @override
  String get regionId => "Region Id";
  @override
  String get referralCodeAppliedSuccesfully =>
      "referral_code applied successfully";
  @override
  String get verified => "Verified";
  @override
  String get doubleTapToExit => "Double tap to exit";
  @override
  String get openSetting => "Open settings";
  @override
  String get send => "Send";
  @override
  String get pleaseEnterValidTitle => "Please enter valid title";
  @override
  String get pleaseEnterValidMessage => "Please enter valid message";
  @override
  String get cameraAccesMessage =>
      "Please Allow Camera access for better experience";
  @override
  String get microphoneAccessMessage =>
      "To use VOIP call feuture, microphone access is required. Please grant permission in the app setting.";
  @override
  String get riderName => "Rider name";
  @override
  String get tax => "Tax";
  @override
  String get suggestedReviews => "Suggested Reviews";
  @override
  String get verify => "Verify";
  @override
  String get startLocation => "Start location";
  //   @override
  // String get verify => "Verify";

  @override
  String get youMissedARide => "You missed a ride";
  @override
  String get youCancelledARide => "You have cancelled a ride";
  @override
  String get youHaveNewRide => "You have new ride";

  @override
  String get waitingTimeCancelledMessage =>
      "You have started moving , cancelled the waiting time";

  @override
  // TODO: implement offerCompleted
  String get offerCompleted => "OFFER COMPLETED";

  @override
  // TODO: implement offerEarning
  String get offerEarning => "OFFER EARNING";

  @override
  // TODO: implement RidedeclinedText
  String get RidedeclinedText => "The ride has been declined.";

  @override
  // TODO: implement systemAlertMessage
  String get systemAlertMessage => "Required system alert permission";

  // TODO: implement declined
}
