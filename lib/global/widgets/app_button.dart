import 'package:flutter/material.dart';
import 'package:rooo_driver/global/constants/Colors.dart';
import 'package:rooo_driver/utils/Constants.dart';

class AppButton extends StatefulWidget {
  final double? height;
  final double? width;
  final Widget? icon;
  final String text;
  final String? subText;
  final Color? backgroundColor;

  final Future Function()? onPressed;
  final bool visible;
  final bool isLoading;
  const AppButton({
    super.key,
    this.icon,
    required this.text,
    required this.onPressed,
    this.visible = true,
    this.isLoading = false,
    this.height,
    this.width,
    this.subText,
    this.backgroundColor,
  });

  @override
  State<AppButton> createState() => _AppButtonState();
}

class _AppButtonState extends State<AppButton> {
  bool _isDoingTask = false;

  void _handleClick() {
    setState(() {
      //
    });
    widget.onPressed!().whenComplete(() {
      setState(() {
        _isDoingTask = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: widget.visible,
      child: MaterialButton(
        color: widget.backgroundColor ?? AppColors.primaryColor(context),
        height: widget.height,
        onPressed: _isDoingTask || widget.onPressed == null
            ? null
            : () {
                  _isDoingTask = true;

                _handleClick();
              },
        child: _isDoingTask || widget.isLoading
              ? Center(
                child: SizedBox(
                    height: 20,
                    width: 20,
                  child: Center(
                      child: CircularProgressIndicator(
                        color: Colors.white,
                      ),
                    ),
                ),
              )
              : SizedBox(
          width: widget.width,
          child:  Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    widget.icon == null
                        ? SizedBox()
                        : Row(
                            children: [
                              widget.icon!,
                              width10,
                            ],
                          ),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(widget.text),
                        widget.subText == null
                            ? SizedBox()
                            : Text(widget.subText!),
                      ],
                    )
                  ],
                ),
        ),
      ),
    );
  }
}
