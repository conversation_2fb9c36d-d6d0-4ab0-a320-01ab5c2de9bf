import 'dart:math';
import 'dart:ui';
import 'package:chucker_flutter/chucker_flutter.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:http/http.dart' as http;
import 'package:location/location.dart' as location;
import 'package:location/location.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:permission_handler/permission_handler.dart' as PH;
import 'package:rooo_driver/background_tasks/BackgroundTasks.dart';
import 'package:rooo_driver/features/bank_info/cubit/bank_info_cubit.dart';
import 'package:rooo_driver/features/online_offline_status/cubit/bank_info_cubit.dart';
import 'package:rooo_driver/features/privacy_center/cubit/push_info_pref_cubit.dart';
import 'package:rooo_driver/features/push_notification_pref/cubit/push_info_pref_cubit.dart';
import 'package:rooo_driver/features/registration/cubit/registration_cubit.dart';
import 'package:rooo_driver/features/reward/cubit/reward_cubit.dart';
import 'package:rooo_driver/features/rushed_area/cubit/rushed_area_cubit.dart';
import 'package:rooo_driver/features/settings/map_setting/cubit/map_setting_cubit.dart';
import 'package:rooo_driver/features/way_bill/cubit/way_bill_cubit.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/globalMethods/one_signal_service.dart';
import 'package:rooo_driver/screens/compulsory_update_app.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:workmanager/workmanager.dart';
import 'package:zego_uikit_prebuilt_call/zego_uikit_prebuilt_call.dart';
import 'Services/ChatMessagesService.dart';
import 'Services/NotificationService.dart';
import 'Services/UserServices.dart';
import 'firebase_options.dart';
import 'global/language/AppLocalizations.dart';
import 'global/language/BaseLanguage.dart';
import 'model/FileModel.dart';
import 'model/LanguageDataModel.dart';
import 'screens/NoInternetScreen.dart';

late FirebaseApp roooFirebaseApp;
late FirebaseAuth roooFirebaseAuth;
AppStore appStore = AppStore();
location.Location appLocation = new location.Location();
StreamSubscription<LocationData>? appLocationStream;
late SharedPreferences sharedPref;
List<LanguageDataModel> localeLanguageList = [];
LanguageDataModel? selectedLanguageDataModel;
late BaseLanguage language;
late List<FileModel> fileList = [];
bool mIsEnterKey = false;
String mSelectedImage = "as0s/default_wallpaper.png";
ChatMessageService chatMessageService = ChatMessageService();
NotificationService notificationService = NotificationService();
UserService userService = UserService();

final navigatorKey = GlobalKey<NavigatorState>();

get getContext => navigatorKey.currentState?.overlay?.context;
late LocationPermission locationPermissionHandle;

Future<void> initialize({
  double? defaultDialogBorderRadius,
  List<LanguageDataModel>? aLocaleLanguageList,
  String? defaultLanguage,
}) async {
  localeLanguageList = aLocaleLanguageList ?? [];
  selectedLanguageDataModel = getSelectedLanguageModel(
    defaultLanguage: default_Language,
  );
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  roooFirebaseApp = await Firebase.initializeApp(
    name: 'rooo-app',
    options: DefaultFirebaseOptions.currentPlatform,
  );
  roooFirebaseAuth = FirebaseAuth.instanceFor(app: roooFirebaseApp);

  sharedPref = await SharedPreferences.getInstance();
  //darkmode from local settings
  IS_DARK_MODE_ON = sharedPref.getBool(IS_DARK_MODE) ?? false;

  await initialize(aLocaleLanguageList: languageList());
  appStore.setLanguage(default_Language);

  await appStore.setLoggedIn(
    sharedPref.getBool(IS_LOGGED_IN) ?? false,
    isInitializing: true,
  );
  await appStore.setUserId(
    sharedPref.getInt(USER_ID) ?? 0,
    isInitializing: true,
  );
  await appStore.setUserEmail(
    sharedPref.getString(USER_EMAIL).validate(),
    isInitialization: true,
  );
  await appStore.setUserProfile(
    sharedPref.getString(USER_PROFILE_PHOTO).validate(),
  );

  MapboxOptions.setAccessToken(AppCred.mapBoxPublicTokenKey);

  WakelockPlus.enable();

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // ChuckerFlutter.showOnRelease = true;

  runApp(const RoooApp());
}

Future<APIResponse> updatePlayerId({
  required String playerId,
  required String timezone,
  required String fcmtoken,
}) async {
  // Map req = {
  //   "player_id": sharedPref.getString(PLAYER_ID),
  // };
  Map req = {
    "player_id": playerId,
    "timezone": timezone,
    "fcm_token": fcmtoken,
  };
  return await updateDriverPlayerIdApi(req);
}

class RoooApp extends StatelessWidget {
  const RoooApp({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => AdvertisementCubit()),
        BlocProvider(create: (context) => OpportunityCubit()),
        BlocProvider(create: (context) => InboxCubit()),
        BlocProvider(create: (context) => HelpCubit()),
        BlocProvider(create: (context) => RegistrationCubit()),
        BlocProvider(create: (context) => VerifyOtpCubit()),
        BlocProvider(create: (context) => LoginCubit()),
        BlocProvider(create: (context) => DocumentCubit()),
        BlocProvider(create: (context) => FaqCubit()),
        BlocProvider(create: (context) => EmergencyContactsCubit()),
        BlocProvider(create: (context) => CareCubit()),
        BlocProvider(create: (context) => EditProfileCubit()),
        BlocProvider(create: (context) => RideFlowCubit()),
        BlocProvider(create: (context) => VehicleCubit()),
        BlocProvider(create: (context) => RewardCubit()),
        BlocProvider(create: (context) => HomeRouteCubit()),
        BlocProvider(create: (context) => MapSettingCubit()),
        BlocProvider(create: (context) => WayBillCubit()),
        BlocProvider(create: (context) => PushNotificationPrefCubit()),
        BlocProvider(create: (context) => BankInfoCubit()),
        BlocProvider(create: (context) => PrivacyCenterCubit()),
        BlocProvider(create: (context) => OnlineOfflineTimeCubit()),
        BlocProvider(create: (context) => RushedAreaCubit()),
      ],
      child: const MyAppContent(),
    );
  }
}

class MyAppContent extends StatefulWidget {
  const MyAppContent({Key? key}) : super(key: key);

  @override
  State<MyAppContent> createState() => _MyAppContentState();
}

// This state was formerly _MyAppState
class _MyAppContentState extends State<MyAppContent> {
  late StreamSubscription<List<ConnectivityResult>> connectivitySubscription;

  @override
  void initState() {
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
    init();
    
    afterBuildCreated(() {
      PH.Permission.notification.status.then((value) {
        if (value == PH.PermissionStatus.granted) {
          OneSignalService.init();
        }
      });
    });
    super.initState();
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  void init() async {
    GlobalMethods.getDarkModeSetting();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard(context);
      },
      child: MaterialApp(
        navigatorObservers: [
          MyRouteObserver(),
          ChuckerFlutter.navigatorObserver,
        ],
        navigatorKey: navigatorKey,
        debugShowCheckedModeBanner: false,
        title: 'ROOO Driver',
        themeMode: ThemeMode.system,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        builder: (context, child) {
          return Stack(
            children: [
              child!,
              ZegoUIKitPrebuiltCallMiniOverlayPage(
                contextQuery: () {
                  return navigatorKey.currentContext!;
                },
              ),
            ],
          );
          // return ScrollConfiguration(behavior: MyBehavior(), child: child!);
        },
        home: SplashScreen(),
        supportedLocales: LanguageDataModel.languageLocales(),
        localizationsDelegates: [
          AppLocalizations(),
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        localeResolutionCallback: (locale, supportedLocales) => locale,
        locale: Locale(
          appStore.selectedLanguage.validate(value: default_Language),
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}

class MyBehavior extends ScrollBehavior {
  @override
  Widget buildOverscrollIndicator(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    return child;
  }
}

Map<String, String> buildHeaderTokensBG(SharedPreferences sharedPrefBG) {
  Map<String, String> header = {
    HttpHeaders.contentTypeHeader: 'application/json; charset=utf-8',
    HttpHeaders.cacheControlHeader: 'no-cache',
    HttpHeaders.acceptHeader: 'application/json; charset=utf-8',
    'Access-Control-Allow-Headers': '*',
    'Access-Control-Allow-Origin': '*',
    "user_type": "driver",
    "language": sharedPrefBG.getString(SELECTED_LANGUAGE_CODE).toString(),
  };

  if (sharedPrefBG.getString(TOKEN) != null) {
    header.putIfAbsent(
      HttpHeaders.authorizationHeader,
      () => 'Bearer ${sharedPrefBG.getString(TOKEN)}',
    );
  }

  header.putIfAbsent('darkMode', () => IS_DARK_MODE_ON.toString());

  header.putIfAbsent(
    'driver_id',
    () => sharedPrefBG.getInt(USER_ID).toString(),
  );

  if (sharedPrefBG.getInt(GLOBAL_REGION_ID) != null) {
    header.putIfAbsent(
      'region_id',
      () => sharedPrefBG.getInt(GLOBAL_REGION_ID).toString(),
    );
  }
  return header;
}

Future update_driver_position_at_serverBG({
  required SharedPreferences sharedPrefBG,
  required LatLng location,
}) async {
  sharedPrefBG.reload();
  bool is_same_loation = false;

  List<int>? ride_ids = await GlobalMethods.getRideIdLocally();

  String locationId = DateTime.now().millisecondsSinceEpoch.toString();
  DateTime currentDate = DateTime.now();
  Map<String, dynamic> request = {
    if (ride_ids.isNotEmpty) "ride_request_id": ride_ids,
    "location_id": locationId,
    "status": 'active',
    "latitude": '${location.latitude}',
    "longitude": '${location.longitude}',
    "is_same_location": is_same_loation,
    "time": '${currentDate}',
  };

  List<String> tempList =
      await sharedPrefBG.getStringList("serverLocations") ?? [];

  List serverLocationList = [];

  tempList.forEach((elelment) {
    serverLocationList.add(jsonDecode(elelment));
  });

  serverLocationList.add(jsonEncode(request));

  if (ride_ids.isNotEmpty) {
    request.putIfAbsent(
      "location_array",
      () => jsonDecode(serverLocationList.toString()),
    );
  }

  await chuckerHttpClient
      .post(
        Uri.parse(AppCred.baseUrl + "update-user-status"),
        headers: buildHeaderTokensBG(sharedPrefBG),
        body: jsonEncode(request),
      )
      .then((value) async {
        if (value.statusCode == 200) {
          var data = jsonDecode(value.body);
          bool isOnline = data["is_online"] ?? true;
          if (GlobalState.isDriverOnline && isOnline == false) {
            BlocProvider.of<RideFlowCubit>(
              navigatorKey.currentContext!,
            ).checkOnlineOfflineStatus(userId: sharedPref.getInt(USER_ID)!);
          }
          List<String> tempList =
              await sharedPrefBG.getStringList("serverLocations") ?? [];
          List<Map<String, dynamic>> serverLocationList = [];

          tempList.forEach((elelment) {
            serverLocationList.add(jsonDecode(elelment));
          });

          serverLocationList = removeMatchingElements(
            serverLocationList ?? [],
            data["location_array"] ?? [],
          );

          List<String> templist = [];
          serverLocationList.forEach((element) {
            templist.add(jsonEncode(element));
          });
          await sharedPrefBG.setStringList("serverLocations", templist);
          // await testLocationBox.put("serverLocations", serverLocationList);

          print("nct-> " + value.body.toString());
          print("nct-> " + buildHeaderTokensBG(sharedPrefBG).toString());

          print("nct-> location sent");
        } else if (value.statusCode == 401) {
          logout(isUnAuthorised: true);
        }
        // toast("nct-> location sent");
      })
      .onError((error, stackTrace) {
        print("nct-> location sent -> Error");
      });

  bool isInProgress = sharedPrefBG.getBool("IS_IN_PROGRESS") ?? false;

  if (isInProgress) {
    bool? isWaitingTimeStarted =
        sharedPrefBG.getBool("isWaitingTimeStarted") ?? false;
    Map<String, dynamic> newLocation = {
      "lattitude": location.latitude,
      "longitude": location.longitude,
      "time": currentDate.toString(),
    };
    print("nct-> newLocaton ${newLocation}");
    sharedPrefBG.setString("newLocation", jsonEncode(newLocation));

    String? lastLocationString = sharedPrefBG.getString("lastLocation");
    if (lastLocationString != null) {
      var lastLocation = jsonDecode(lastLocationString);

      print("nct-> newLocaton ${lastLocation}");
      bool isLessThan10Meters(
        double lat1,
        double lon1,
        double lat2,
        double lon2,
      ) {
        // Haversine formula to calculate distance in meters
        final double dLat = radians(lat2 - lat1);
        final double dLon = radians(lon2 - lon1);

        double radianss(double degrees) => degrees * pi / 180;
        final double a =
            sin(dLat / 2) * sin(dLat / 2) +
            cos(radianss(lat1)) *
                cos(radians(lat2)) *
                sin(dLon / 2) *
                sin(dLon / 2);

        final double c = 2 * atan2(sqrt(a), sqrt(1 - a));

        final double earthRadius =
            6371e3; // Earth radius in meters (adjust if needed)
        final double distanceInMeters = earthRadius * c;

        return distanceInMeters < 30;
      }

      if (isLessThan10Meters(
        lastLocation["lattitude"],
        lastLocation["longitude"],
        newLocation["lattitude"],
        newLocation["longitude"],
      )) {
        //means at same location

        DateTime lastTime = DateTime.parse(lastLocation["time"]);
        DateTime newTime = DateTime.parse(newLocation["time"]);
        print(
          "nct-. time difffrence in minutes" +
              newTime.difference(lastTime).inMinutes.toString(),
        );

        if (newTime.difference(lastTime).inMinutes > 0) {
          if (isWaitingTimeStarted == false) {
            startWaitingTime();
          }
        }
      } else {
        if (isWaitingTimeStarted) {
          endWaitingTime();
        }
        //means location changed ,means moved, means running
        sharedPrefBG.setString("lastLocation", jsonEncode(newLocation));
      }
    } else {
      sharedPrefBG.setString("lastLocation", jsonEncode(newLocation));
    }
  }

  // syncWithServer(
  //     {required Box<List> box,
  //     required Map request,
  //     required SharedPreferences sharedPrefBG}) async {}

  // savingWaitingTime(
  //     {required BackgroundLocationUpdateData newLocationData,
  //     required DateTime currentDate,
  //     required SharedPreferences sharedPrefBG}) async {}

  // Helper function to convert degrees to radians
  // class SharedPrefs {
  //   static final SharedPrefs _instance = SharedPrefs._internal();
  //   SharedPreferences? _prefs;

  //   factory SharedPrefs() {
  //     return _instance;
  //   }

  //   SharedPrefs._internal();

  //   Future<void> init() async {
  //     _prefs = await SharedPreferences.getInstance();
  //   }

  //   SharedPreferences? get prefs => _prefs;
  // }
}

startWaitingTime() async {
  try {
    final SendPort? send = await IsolateNameServer.lookupPortByName('back');
    if (send == null) {
      print("nct-> send port null");
    } else {
      send.send(true);
      print("nct-> send port not null");
    }
  } catch (e) {
    log(e.toString());
    throw e;
  }
}

endWaitingTime() async {
  try {
    final SendPort? send = await IsolateNameServer.lookupPortByName('back');
    if (send == null) {
    } else {
      send.send(false);
    }
  } catch (e) {
    log(e.toString());
    throw e;
  }
}

class MyRouteObserver extends NavigatorObserver {
  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
  }

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
  }
}
