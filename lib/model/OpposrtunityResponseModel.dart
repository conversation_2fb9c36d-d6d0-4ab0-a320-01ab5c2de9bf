import 'package:rooo_driver/global/models/ride_model.dart';
import 'package:rooo_driver/model/PaginationModel.dart';

class OpportunityResponseModel {
  PaginationModel? pagination;
  List<OnRideRequest>? data;
  String? banner_image;
  String? messasge;
  bool? status;

  OpportunityResponseModel({
    this.data,
    this.pagination,
    required this.banner_image,
    this.messasge,
    this.status,
  });

  factory OpportunityResponseModel.fromJson(Map<String, dynamic> json) {
    return OpportunityResponseModel(
        pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
        data: json["data"] != null
            ? (json["data"] as List)
                .map((e) => OnRideRequest.fromJson(e))
                .toList()
            : null,
        banner_image: json["banner_image"],
        status: json["status"],
        messasge: json["message"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas["pagination"] = this.pagination;
    datas["data"] = this.data;
    datas["imageURL"] = this.banner_image;
    datas["message"] = this.messasge;
    return datas;
  }
}
