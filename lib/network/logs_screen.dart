import 'package:flutter/material.dart';

List<String> appLogs = [];

class AppLogs extends StatelessWidget {
  const AppLogs({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Logs')),
      body: ListView.separated(
        separatorBuilder: (context, index) => const Divider(),
        itemCount: appLogs.length,
        reverse: true,
        itemBuilder: (context, index) {
          return ListTile(title: Text(appLogs[index]));
        },
      ),
    );
  }
}
