import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/components/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/utils/Extensions/StringExtensions.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../utils/Constants.dart';
import '../../utils/Extensions/app_common.dart';
import '../main.dart';
import '../model/SettingModel.dart';
import '../utils/Common.dart';

class AboutScreen extends StatefulWidget {
  final SettingModel settingModel;

  AboutScreen({required this.settingModel});

  @override
  AboutScreenState createState() => AboutScreenState();
}

class AboutScreenState extends State<AboutScreen> {
  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    //
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(
        title: language.aboutUs,
      ),
      body: SingleChildScrollView(
        child: Container(
          alignment: Alignment.center,
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
             
              Text(mAppName, style: primaryTextStyle(size: 20)),
             
              height10,
              FutureBuilder<PackageInfo>(
                future: PackageInfo.fromPlatform(),
                builder: (_, snap) {
                  if (snap.hasData) {
                    return Text('v${snap.data!.version}', style: TextStyle());
                  }
                  return SizedBox();
                },
              ),
              SizedBox(height: 10),
              Text(about_us_instruction_text ?? "", // maxLines: 6,
                  overflow: TextOverflow.visible),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.all(16),
        child: Container(
          height: 120,
          child: Column(
            children: [
              Text(language.lblFollowUs, style: TextStyle()),
              SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  inkWellWidget(
                    onTap: () {
                       if (widget.settingModel.instagramUrl !=
                              null &&
                          widget.settingModel.instagramUrl!
                              .isNotEmpty) {
                        launchUrl(
                            Uri.parse(widget
                                .settingModel.instagramUrl
                                .validate()),
                            mode: LaunchMode.externalApplication);
                      } else {
                       GlobalMethods.errorToast(context, "Something went wrong");
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.all(10),
                      child: Image.asset("images/ic_insta.png",
                          height: 35, width: 35),
                    ),
                  ),
                  // inkWellWidget(
                  //   onTap: () {
                  //     launchUrl(Uri.parse('https://twitter.com/'),
                  //         mode: LaunchMode.externalApplication);
                  //   },
                  //   child: Container(
                  //     padding: EdgeInsets.all(10),
                  //     child: Image.asset('images/ic_twitter.png',
                  //         height: 35, width: 35),
                  //   ),
                  // ),
                  inkWellWidget(
                    onTap: () {
                      if (widget.settingModel.linkedinUrl !=
                              null &&
                          widget.settingModel.linkedinUrl!
                              .isNotEmpty) {
                        launchUrl(
                            Uri.parse(widget
                                .settingModel.linkedinUrl
                                .validate()),
                            mode: LaunchMode.externalApplication);
                      } else {
                         GlobalMethods.errorToast(context, "Something went wrong");
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.all(10),
                      child: Image.asset('images/ic_linked.png',
                          height: 35, width: 35),
                    ),
                  ),
                  inkWellWidget(
                    onTap: () {
                     if (widget.settingModel.facebookUrl !=
                              null &&
                          widget.settingModel.facebookUrl!
                              .isNotEmpty) {
                        launchUrl(
                            Uri.parse(widget
                                .settingModel.facebookUrl
                                .validate()),
                            mode: LaunchMode.externalApplication);
                      } else {
                          GlobalMethods.errorToast(context, "Something went wrong");
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.all(10),
                      child: Image.asset('images/ic_facebook.png',
                          height: 35, width: 35),
                    ),
                  ),
                  // inkWellWidget(
                  //   onTap: () {
                  //     launchUrl(Uri.parse('tel:1234567890'),
                  //         mode: LaunchMode.externalApplication);
                  //   },
                  //   child: Container(
                  //     margin: EdgeInsets.only(right: 16),
                  //     padding: EdgeInsets.all(10),
                  //     child: Icon(
                  //       Icons.call,
                  //       color:
                  //            ,
                  //       size: 36,
                  //     ),
                  //   ),
                  // )
                ],
              ),
              SizedBox(height: 8),
              widget.settingModel.siteCopyright != null &&
                      widget.settingModel.siteCopyright!.isNotEmpty
                  ? Text(widget.settingModel.siteCopyright.validate(),
                      style: secondaryTextStyle(), maxLines: 1)
                  : Text(
                      language.CopyrightText +
                          " @${DateTime.now().year} " +
                          language.roooTxt,
                      style: TextStyle()),
            ],
          ),
        ),
      ),
    );
  }
}
