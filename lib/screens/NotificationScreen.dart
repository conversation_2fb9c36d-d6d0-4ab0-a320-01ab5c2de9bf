import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/empty_widget.dart';

import '../model/NotificationListModel.dart';
import 'RideDetailScreen.dart';

class NotificationScreen extends StatefulWidget {
  @override
  NotificationScreenState createState() => NotificationScreenState();
}

class NotificationScreenState extends State<NotificationScreen>
    with TickerProviderStateMixin {
  ScrollController scrollController = ScrollController();
  int currentPage = 1;

  bool mIsLastPage = false;
  List<NotificationData> notificationData = [];
  String? api_message;

  @override
  void initState() {
    super.initState();
    init();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (!mIsLastPage) {
          appStore.setLoading(true);

          currentPage++;
          setState(() {});

          init();
        }
      }
    });
    afterBuildCreated(() {
      appStore.setLoading(true);
      GlobalState.global_notification_count.value = 0;
    });
  }

  void init() async {
    getNotification(page: currentPage).then((value) {
      appStore.setLoading(false);
      api_message = value.message;
      //appStore.setAllUnreadCount(value.allUnreadCount.validate());
      mIsLastPage = value.notificationData!.length < currentPage;
      if (currentPage == 1) {
        notificationData.clear();
      }
      notificationData.addAll(value.notificationData!);
      // if (notificationData.isEmpty) {
      //   notificationData.addAll([
      //     NotificationData(
      //       data: NotificationOrderData(
      //         subject: 'Dummy Notification 1',
      //         message: 'This is a dummy notification',
      //       ),
      //       createdAt: DateTime.now().toString(),
      //       readAt: null,
      //     ),
      //     NotificationData(
      //       data: NotificationOrderData(
      //           subject: 'Dummy Notification 2',
      //           message: 'This is another dummy notification'),
      //       createdAt: DateTime.now().toString(),
      //       readAt: DateTime.now().toString(),
      //     ),
      //   ]);
      // }
      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);
      log(error);
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(
        title: "Notifications",
      ),
      body: Observer(builder: (context) {
        return Stack(
          children: [
            ListView.separated(
              controller: scrollController,
              padding: EdgeInsets.all(16),
              itemCount: notificationData.length,
              itemBuilder: (_, index) {
                NotificationData data = notificationData[index];
                return inkWellWidget(
                  onTap: () {
                    if (data.data!.type == COMPLAIN_COMMENT) {
                      // launchScreen(
                      //     context,
                      //     ComplaintListScreen(
                      //         complaint: data.data!.complaintId!));
                    } else if (data.data!.subject! == 'Completed') {
                      GlobalMethods.pushScreen(
                          context: context,
                          screen: RideDetailScreen(orderId: data.data!.id!),
                          screenIdentifier: ScreenIdentifier.RideDetailScreen);
                    }
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: data.readAt == null
                          ? Colors.grey.shade50
                          : Colors.white,
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.grey.shade200,
                          width: 1,
                        ),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              height: 40,
                              width: 40,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: data.readAt == null
                                    ? AppColors.primaryMustardColr
                                        .withOpacity(0.1)
                                    : Colors.grey.shade100,
                              ),
                              child: ImageIcon(
                                AssetImage(
                                    statusTypeIcon(type: data.data!.type)),
                                size: 20,
                                color: data.readAt == null
                                    ? AppColors.primaryMustardColr
                                    : Colors.grey.shade600,
                              ),
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${data.data!.subject}',
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 16,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    '${data.data!.message}',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.black,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        Text(
                          data.createdAt.validate(),
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) => SizedBox(height: 10),
            ),
            Observer(
              builder: (context) {
                return Visibility(
                  visible: !appStore.isLoading && notificationData.isEmpty,
                  child: RooEmptyWidegt(title: api_message.toString()),
                );
              },
            ),
            Observer(
              builder: (context) {
                return Visibility(
                  visible: appStore.isLoading,
                  child: loaderWidget(),
                );
              },
            )
          ],
        );
      }),
    );
  }
}
