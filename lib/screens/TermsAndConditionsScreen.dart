import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../main.dart';
import '../network/RestApis.dart';
import '../utils/Common.dart';
import '../utils/Extensions/app_common.dart';

class TermsAndConditionsScreen extends StatefulWidget {
  @override
  TermsAndConditionsScreenState createState() =>
      TermsAndConditionsScreenState();
}

class TermsAndConditionsScreenState extends State<TermsAndConditionsScreen> {
  bool webPageLoaded = false;
  String? url;

  WebViewController controller = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted);
  @override
  void initState() {
    hideKeyboard(context);
    init();
    super.initState();
  }

  void init() async {
    await getTAndC().then((value) {
      if (value['data']['link'] != null) {
        setState(() {
          url = value['data']['link'];
        });
        controller.setNavigationDelegate(NavigationDelegate(
          onPageFinished: (url) {
            setState(() {
              webPageLoaded = true;
            });
            if (Theme.of(context).brightness == Brightness.dark) {
              controller.runJavaScript('''
                // Set default dark theme colors
                document.body.style.backgroundColor = '#121212';
                document.body.style.color = '#FFFFFF';
                
                // Target all elements
                document.querySelectorAll('*').forEach(element => {
                  // Set background color for all elements
                  element.style.backgroundColor = '#121212';
                  
                  // Handle text colors based on element type
                  if (element.tagName === 'A') {
                    element.style.color = '#90CAF9';
                  } else if (['H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(element.tagName)) {
                    element.style.color = '#FFFFFF';
                  } else if (['P', 'SPAN', 'DIV', 'LI', 'UL', 'OL', 'TD', 'TH', 'TR', 'LABEL', 'SECTION'].includes(element.tagName)) {
                    element.style.color = '#E0E0E0';
                  } else {
                    element.style.color = '#E0E0E0';
                  }
                  
                  // Override any inline background styles
                  element.style.setProperty('background-color', '#121212', 'important');
                  element.style.setProperty('background', '#121212', 'important');
                });
              ''');
            }
          },
        ));
        controller.loadRequest(Uri.parse(url!));
      }
    }).catchError((error) {
      log("Server error");
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: RoooAppbar(
          title: language.termsConditions,
        ),
        body: SizedBox(
          height: MediaQuery.sizeOf(context).height,
          child: Stack(
            children: [
              (url == null)
                  ? const SizedBox()
                  : WebViewWidget(
                      controller: controller,
                    ),
              webPageLoaded ? const SizedBox() : loaderWidget(),
            ],
          ),
        ));
  }
}
