import 'package:rooo_driver/features/login/screens/roo_login_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';

import '../model/WalkThroughModel.dart';

class WalkThroughScreen extends StatefulWidget {
  @override
  WalkThroughScreenState createState() => WalkThroughScreenState();
}

class WalkThroughScreenState extends State<WalkThroughScreen> {
  PageController pageController = PageController();
  int currentPage = 0;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    //
  }

  List<WalkThroughModel> walkThroughClass = [
    WalkThroughModel(
      name: 'Get Ride Request',
      text: "Get A Ride Request By\nNearest Rider",
      img: 'assets/walkthrough/1.png',
    ),
    WalkThroughModel(
      name: 'Pickup Rider',
      text: "Accept a ride request and pickup\na rider for destination",
      img: 'assets/walkthrough/2.png',
    ),
    WalkThroughModel(
      name: 'Drop Rider',
      text: "Drop A Rider To Destination",
      img: 'assets/walkthrough/3.png',
    ),
    // WalkThroughModel(
    //   name: 'Drop Rider',
    //   text: "Drop A Rider To Destination",
    //   img: 'images/driver3.jpg',
    // )
  ];

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          PageView.builder(
            itemCount: walkThroughClass.length,
            controller: pageController,
            itemBuilder: (context, i) {
              return Stack(
                alignment: Alignment.center,
                children: [
                  Image.asset(
                    walkThroughClass[i].img.toString(),
                    fit: BoxFit.contain,
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height,
                  ),
                ],
              );
            },
            onPageChanged: (int i) {
              currentPage = i;
              setState(() {});
            },
          ),
          Positioned(
            bottom: Platform.isIOS ? 70 : 50,
            right: 0,
            left: 0,
            child: dotIndicator(walkThroughClass, currentPage),
          ),
          Positioned(
            top: 0,
            bottom: 0,
            right: 10,
            child: GestureDetector(
              onTap: () {
                GlobalMethods.pushAndRemoveAll(
                    context: context,
                    screen: LoginScreen(),
                    screenIdentifier: ScreenIdentifier.LoginScreen);
              },
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.fromBorderSide(
                    BorderSide(
                      color: Colors.white,
                      width: 1,
                    ),
                  ),
                ),
                padding: EdgeInsets.all(12),
                child: Icon(
                  Icons.arrow_forward,
                  color: Colors.white,
                  size: 30,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
