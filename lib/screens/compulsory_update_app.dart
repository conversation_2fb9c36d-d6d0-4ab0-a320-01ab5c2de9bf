import 'package:rooo_driver/global/export/app_export.dart';
import 'package:url_launcher/url_launcher.dart';

/* Current app version */
num currentAppVersion = 1.8;

class CompulsoryUpdateApp extends StatelessWidget {
  final bool isCompulsory;
  const CompulsoryUpdateApp({required this.isCompulsory, super.key});

  Widget _buildActionButton() {
    if (isCompulsory) {
      GlobalState.isCompulsoryUpdateOpen = true;
    }
    Widget button = AppButton(
      text: "Update",
      onPressed: () async {
        if (Platform.isIOS) {
          launchUrl(
            Uri.parse("https://apps.apple.com/us/app/rooo-driver/id6502895509"),
          );
        } else {
          launchUrl(
            Uri.parse(
              "https://play.google.com/store/apps/details?id=app.rooo.driver",
            ),
          );
        }
      },
    );

    if (isCompulsory) {
      return Column(mainAxisSize: MainAxisSize.min, children: [button]);
    }
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        button,
        AppButton(
          text: "No, Thanks",
          backgroundColor: Colors.grey.shade300,
          onPressed: () async {
            Navigator.of(navigatorKey.currentContext!).pop();
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              // You can use an icon or an image here
              const Icon(
                Icons.update, // A clear, relevant icon
                size: 80,
                color: Colors.blueAccent,
              ),
              const SizedBox(height: 24),
              const Text(
                'New Update Available',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                isCompulsory
                    ? 'This version is no longer supported. Please update to the latest version to continue using the app.'
                    : 'A new version is available. Please update to the latest version to get the latest features.',
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              _buildActionButton(),
            ],
          ),
        ),
      ),
    );
  }
}

Future<void> appUpdateChecker() async {
  if (GlobalState.isCompulsoryUpdateOpen) {
    return;
  }

  LatestAppVersionResponse response = await getLatestAppVersion(
    platform: Platform.isAndroid ? "android" : "ios",
    appName: "driver",
    currentVersion: currentAppVersion,
  );
  if (response.status && response.data != null) {
    if (response.data!.isUpdateAvailable) {
      GlobalState.isCompulsoryUpdateOpen = true;
      // response.data!.isCompulsory = true;
      /* just show the dialog */
      showDialog(
        context: navigatorKey.currentContext!,
        barrierDismissible: response.data!.isCompulsory ? false : true,
        barrierColor: Colors.grey.withAlpha(50),
        builder: (context) {
          return PopScope(
            canPop: response.data!.isCompulsory ? false : true,
            child: AlertDialog(
              title: Text("Update Available"),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const LinearProgressIndicator(),
                  const SizedBox(height: 10),
                  Text(
                    response.data!.isCompulsory
                        ? "This version is no longer supported. Please update to the latest version to continue using the app."
                        : "A new version is available. Please update to the latest version to get the latest features.",
                  ),
                  const Divider(),
                  AppButton(
                    text: "Update",
                    onPressed: () async {
                      if (Platform.isIOS) {
                        launchUrl(
                          Uri.parse(
                            "https://apps.apple.com/us/app/rooo-driver/id6502895509",
                          ),
                        );
                      } else {
                        launchUrl(
                          Uri.parse(
                            "https://play.google.com/store/apps/details?id=app.rooo.driver",
                          ),
                        );
                      }
                    },
                  ),
                  if (!response.data!.isCompulsory)
                    TextButton(
                      child: Text("No, Thanks"),
                      onPressed: () async {
                        Navigator.of(navigatorKey.currentContext!).pop();
                      },
                    ),
                ],
              ),
            ),
          );
        },
      );
    }
  }
}

class LatestAppVersionResponse {
  bool status;
  String message;
  LatestAppVersionData? data;

  LatestAppVersionResponse({
    required this.status,
    required this.message,
    required this.data,
  });

  factory LatestAppVersionResponse.fromMap(Map<String, dynamic> json) {
    return LatestAppVersionResponse(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? LatestAppVersionData.fromMap(json['data'])
          : null,
    );
  }
}

class LatestAppVersionData {
  bool isUpdateAvailable;
  bool isCompulsory;

  LatestAppVersionData({
    required this.isUpdateAvailable,
    required this.isCompulsory,
  });
  factory LatestAppVersionData.fromMap(Map<String, dynamic> json) {
    return LatestAppVersionData(
      isUpdateAvailable: json['is_new_version_available'],
      isCompulsory: json['is_compulsory'],
    );
  }
}

// Timer appUpdateCheckerTimer = Timer.periodic(Duration(seconds: 10), (v) {
//   // check for app update
// });
