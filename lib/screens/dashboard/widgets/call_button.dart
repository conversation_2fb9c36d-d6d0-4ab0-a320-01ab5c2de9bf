import 'package:permission_handler/permission_handler.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/globalMethods/zego_service.dart';
import 'package:zego_uikit/zego_uikit.dart';
import 'package:zego_uikit_prebuilt_call/zego_uikit_prebuilt_call.dart';

class ZegoCallButton extends StatelessWidget {
  const ZegoCallButton({
    super.key,
    required this.context,
    required this.riderId,
    required this.riderName,
  });

  final String riderId;
  final String riderName;
  final BuildContext context;
  Future<bool> _doCall() async {
    void _notifyMicrophonePermissionDenied() {
      GlobalMethods.showInfoDialog(
        context: context,
        positiveAction: () async {
          await openAppSettings();
        },
        title:
            "For the calling feature, please allow the \"Microphone/Record Audio\" permission.",
      );
    }

    void _notifySystemAlertPermissionDenied() {
      GlobalMethods.showInfoDialog(
        context: context,
        positiveAction: () async {
          await openAppSettings();
        },
        title:
            "For the calling feature, please allow the \"Display over other apps\" permission.",
      );
    }

    BlocProvider.of<RideFlowCubit>(context).emit(RideFlowLoadingState());
    var result = await ZegoVoiceCallService.call();
    if (result == ZegoVoiceCallServiceStatus.microphonePermissionDenied) {
      _notifyMicrophonePermissionDenied();
    } else if (result ==
        ZegoVoiceCallServiceStatus.systemAlertPermissionDenied) {
      _notifySystemAlertPermissionDenied();
    } else if (result != ZegoVoiceCallServiceStatus.initialised) {
      //
    }
    BlocProvider.of<RideFlowCubit>(context).emit(RideFlowInitialState());
    return result == ZegoVoiceCallServiceStatus.initialised;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ZegoSendCallInvitationButton(
          clickableBackgroundColor: AppColors.primaryColor(context),
          onWillPressed: () async {
            // if (ZegoVoiceCallService.isinitialized.value == 0 ||
            //     ZegoVoiceCallService.isinitialized.value == -1) {
            return await _doCall();
            // }
            // else {
            //   return GlobalMethods.showInfoDialog(
            //       context: context,
            //       positiveAction: () {
            //         ZegoVoiceCallService.init(
            //           navigatorKey: navigatorKey,
            //           appId: AppCred.zegoAppId,
            //           appSign: AppCred.zegoAppSign,
            //           callerId: sharedPref.getInt(USER_ID).toString(),
            //           callerName:
            //               sharedPref.getString(FIRST_NAME).toString(),
            //         );
            //       },
            //       title:
            //           "Calling feature is down, please press Ok to make it work");
            // }
          },
          onPressed: (code, message, p2) {
            if (code == AppCred.zegoNoUserError) {
              GlobalMethods.errorToast(context, "The recipient is not online");
              return;
            }
          },
          
          callID: DateTime.now().millisecondsSinceEpoch.toString(),
          timeoutSeconds: 20,
          icon: ButtonIcon(icon: const Icon(Icons.call)),
          buttonSize: const Size.square(50),
          iconSize: const Size.square(50),
          verticalLayout: false,
          isVideoCall: false,
          resourceID: AppCred.zegoResorceId,
          invitees: [ZegoUIKitUser(id: riderId, name: riderName)],
        ),
        // Text("Call")
      ],
    );
  }
}
