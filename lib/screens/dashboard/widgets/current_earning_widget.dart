import 'package:rooo_driver/features/earnings/screens/earning_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class CurrentEarningWidget extends StatelessWidget {
  final GlobalKey<State<StatefulWidget>> keys;
  final num current_earning;
  final bool is_profile_completed;
  final bool isDataLoaded;

  const CurrentEarningWidget({
    super.key,
    required this.current_earning,
    required this.is_profile_completed,
    required this.keys,
    required this.isDataLoaded,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: ElevatedButton(
          key: keys,
          onPressed: isDataLoaded
              ? () {
                  /* it will update the earnigs data  */
                  getAndApplyCounters();
                  GlobalMethods.handleInCompleteProfile(
                      context: context,
                      is_profile_completed: is_profile_completed,
                      positiveAction: () {
                        GlobalMethods.pushScreen(
                            context: context,
                            screen: EarningScreen(),
                            screenIdentifier: ScreenIdentifier.EarningScreen);
                      });
                }
              : () {},
          child: isDataLoaded
              ? SizedBox(
                  child: Text(
                    "\$ ${current_earning}",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 15,
                      color: AppColors.blackColor(context),
                    ),
                  ),
                )
              : const SizedBox(
                  width: 20,
                  height: 20,
                  child: Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  ),
                ),
          style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(32.0)),
              // minimumSize: Size(100, 40),
              elevation: 4,
              padding: EdgeInsets.all(8),
              backgroundColor: AppColors.whiteColor(context) // <-- Button color
              )),
    );
  }
}
