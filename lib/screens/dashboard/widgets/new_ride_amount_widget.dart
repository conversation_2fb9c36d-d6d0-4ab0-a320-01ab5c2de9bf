import 'package:flutter/material.dart';
import 'package:rooo_driver/global/models/ride_model.dart';

class NewRideAmountWidget extends StatelessWidget {
  final OnRideRequest onRideRequest;
  const NewRideAmountWidget({super.key, required this.onRideRequest});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Text("Estimated fare: "),
            Text(
              "AUD " + onRideRequest.normalRideAmount.toString(),
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            if (onRideRequest.peakRideAmount! > 0) ...[
              Text(
                " + AUD " + onRideRequest.peakRideAmount.toString(),
                style: TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        ),
        Row(children: [
          Text("Pickup Distance: "),
          Text(
            onRideRequest.pickupDistance.toString() + " km",
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ],)
      ],
    );
  }
}
