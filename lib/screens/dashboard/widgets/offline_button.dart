import 'package:action_slider/action_slider.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class OfflineButton extends StatefulWidget {
  // final AnimateIconController animateIconController;
  final Function onPress;
  final RideFlowState blocState;
  const OfflineButton(
      {super.key,
      required this.onPress,
      // required this.animateIconController,
      required this.blocState});

  @override
  State<OfflineButton> createState() => _OfflineButtonState();
}

class _OfflineButtonState extends State<OfflineButton> {
  @override
  Widget build(BuildContext context) {
    if (widget.blocState is RideFlowLoadingState) {
      return LinearProgressIndicator(
        color: Colors.red,
      );
    }
    return Padding(
      padding: screenPadding,
      child: ActionSlider.standard(
        height: 60,
        actionThreshold: .7,
        backgroundColor: Colors.red,
        toggleColor: AppColors.blackColor(context),
        icon: Icon(
          Icons.double_arrow,
          color: AppColors.whiteColor(context),
        ),
        borderWidth: 0,
        foregroundBorderRadius: BorderRadius.circular(defaultRadius),
        backgroundBorderRadius: BorderRadius.circular(defaultRadius),
        sliderBehavior: SliderBehavior.stretch,
        child: Text(
          'Go offline',
          style: AppTextStyles.header(color: Colors.white),
        ),
        action: (controller) async {
          controller.loading();
      
          widget.onPress();
          if (widget.blocState is OnlineState) {
            controller.success();
          }
          controller.failure();
        },
      ),
    );
  }
}
