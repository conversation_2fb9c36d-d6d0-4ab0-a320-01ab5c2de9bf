import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';
import 'package:rooo_driver/screens/dashboard/widgets/call_button.dart';

class RideContactAndDetailsWidget extends StatelessWidget {
  final UserData rider;
  final OnRideRequest onRideRequest;
  final VoidCallback onRefreshRideDetails;
  final Function(OnRideRequest rideRequest) onCancelRide;
  final bool showActions;

  const RideContactAndDetailsWidget({
    Key? key,
    required this.rider,
    required this.onRefreshRideDetails,
    required this.onCancelRide,
    required this.onRideRequest,
    this.showActions = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey.shade800
            : Colors.grey.shade200,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              _buildRiderInfoSection(context),
              // if ((onRideRequest.isPool ?? false) == false) ...[
              //   const SizedBox(height: 12.0),
              //   _buildPoolRideIndicator(),
              // ],
            ],
          ),
          if (showActions) ...[
            const SizedBox(height: 8.0),
            _buildActionButtons(context),
          ],
        ],
      ),
    );
  }

  Widget _buildRiderInfoSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: Colors.grey[200],
                child: Icon(Icons.person, color: Colors.grey[600]),
              ),
              const SizedBox(width: 12.0),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    rider.firstName! + " " + rider.lastName!,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 18.0,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.black,
                    ),
                  ),
                  /* Rating */
                  RatingBar.builder(
                    itemSize: 25,
                    ignoreGestures: true,
                    initialRating: rider.rating?.toDouble() ?? 0,
                    direction: Axis.horizontal,
                    glow: false,
                    allowHalfRating: false,
                    wrapAlignment: WrapAlignment.spaceBetween,
                    itemBuilder: (context, _) =>
                        Icon(Icons.star, color: Colors.amber),
                    onRatingUpdate: (rating) {},
                    unratedColor: Colors.grey,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPoolRideIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.group, size: 16.0, color: Colors.blue[700]),
          const SizedBox(width: 6.0),
          Text(
            "Pool Ride",
            style: TextStyle(
              color: Colors.blue[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildActionButton(
            child: ZegoCallButton(
              context: context,
              riderId: rider.id.toString(),
              riderName: rider.firstName! + " " + rider.lastName!,
            ),
          ),
        ),
        const SizedBox(width: 12.0),
        Expanded(
          child: _buildActionButton(
            child: ChatButton(
              onRiderPlayerIdMissing: () {
                GlobalMethods.showConfirmationDialog(
                  context: context,
                  onPositiveAction: onRefreshRideDetails,
                  title: "$errorMessage Please press OK and check again.",
                );
              },
              onRideRequest: onRideRequest,
            ),
          ),
        ),
        if (onRideRequest.status != DRIVERCANCELED &&
            onRideRequest.canDriverCancelTheRide != false) ...[
          const SizedBox(width: 12.0),
          Expanded(
            child: _buildActionButton(
              child: RideCancelButton(
                onTap: () async {
                  onCancelRide(onRideRequest);
                },
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildActionButton({required Widget child}) {
    return child;
    // return Container(
    //   decoration: BoxDecoration(
    //     borderRadius: BorderRadius.circular(8.0),
    //     border: Border.all(color: Colors.grey[200]!),
    //   ),
    //   child: child,
    // );
  }
}
