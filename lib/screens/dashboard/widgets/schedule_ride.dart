import 'package:rooo_driver/features/opportunity/screens/opportunity_screen2.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class ScheduleRide extends StatelessWidget {
  final List<OnRideRequest> scheduleRide;
  const ScheduleRide({super.key, required this.scheduleRide});

  @override
  Widget build(BuildContext context) {
    return scheduleRide.isEmpty
        ? SizedBox()
        : Column(
            children: [
              height10,
              Text("Your Upcoming Rides", style: AppTextStyles.title()),
              SizedBox(
                height: 384,
                width: MediaQuery.sizeOf(context).width,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  separatorBuilder: (context, index) =>
                      const SizedBox(width: 10),
                  padding: EdgeInsets.symmetric(horizontal: screenPaddingValue),
                  // controller: scrollController,
                  shrinkWrap: true,
                  itemCount: scheduleRide.length,
                  itemBuilder: (context, index) {
                    return SizedBox(
                      width: scheduleRide.length == 1
                          ? MediaQuery.sizeOf(context).width - 40
                          : 300,
                      child: InkWell(
                        onTap: () {
                          GlobalMethods.pushScreen(
                            context: context,
                            screen: OpportunityScreen(fromDashboard: true),
                            screenIdentifier:
                                ScreenIdentifier.OpportunityDetailScreen,
                          );
                        },
                        child: OpportunityRideCard(
                          text: "",
                          datetime: scheduleRide[index].datetime,
                          riderName: scheduleRide[index].riderName ?? "",
                          riderRating: scheduleRide[index].riderRating ?? 0,
                          serviceName: scheduleRide[index].serviceName ?? "",
                          endAddress: scheduleRide[index].endAddress.toString(),
                          bookingTime: scheduleRide[index].bookingTime,
                          startAddress: scheduleRide[index].startAddress
                              .toString(),
                          isButton: false,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
  }
}
