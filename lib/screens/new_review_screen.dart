import 'package:rooo_driver/features/advertisements/models/advertisement_model.dart';
import 'package:rooo_driver/features/ride_flow/screen/ride_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/ReviewModel.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import '../utils/Extensions/app_textfield.dart';

class NewReviewScreen extends StatefulWidget {
  final int rideId;
  final OnRideRequest onrideRequest;
  final bool isPooling;

  NewReviewScreen(
      {required this.rideId,
      required this.onrideRequest,
      required this.isPooling});

  @override
  NewReviewScreenState createState() => NewReviewScreenState();
}

class NewReviewScreenState extends State<NewReviewScreen> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  TextEditingController reviewController = TextEditingController();

  num ratingData = 5;
  Payment? paymentData;

  List<ReviewModel> automatedMessagesList = [];

  ReviewModel selectedMessageList = ReviewModel(rating: 5000, messages: []);
  AdvertisementModel? _advertisement;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    // getAutomatedText();
    _getAdvertisement();
    //
  }

  Future<void> userReviewData() async {
    hideKeyboard(context);
    // if (ratingData == 0) returnGlobalMethods.infoToast(context,  language.pleaseSelectRating);
    // formKey.currentState!.save();

    setState(() {
      appStore.setLoading(true);
    });

    // await Future.delayed(Duration(seconds: 34));

    Map req = {
      "ride_request_id": widget.rideId,
      "rating": ratingData,
      "comment": reviewController.text.trim(),
    };
    await ratingReview(request: req).then((value) {
      setState(() {
        appStore.setLoading(false);
      });
      getRiderCheck();
    }).catchError((error) {
      setState(() {
        appStore.setLoading(false);
      });
      GlobalMethods.infoToast(context, "Server error");
      log("Server error");
    });
  }

  Future<void> getRiderCheck() async {
    appStore.setLoading(false);
    await rideDetail(orderId: widget.rideId).then((value) {
      if (value.payment != null && value.payment!.paymentStatus == PENDING) {
        // launchScreen(context, DetailScreen(),
        //     pageRouteAnimation: PageRouteAnimation.Slide, isNewTask: true);
      } else {
        if (widget.isPooling) {
          Navigator.pop(context, true);
        } else {
          GlobalMethods.pushAndRemoveAll(
              context: context,
              screen: RideScreen(),
              screenIdentifier: ScreenIdentifier.RideScreen);
        }

        // launchScreen(context, RideScreen(), isNewTask: true);
      }
    }).catchError((error) {
      appStore.setLoading(false);

      GlobalMethods.infoToast(context, "Server error");
    });
  }

  _getAdvertisement() async {
    setState(() {
      appStore.setLoading(true);
    });

    await getAdvertismentApi(type: "review").then((value) {
      if (value.status) {
        if (value.data != null) {
          _advertisement = value.data;
        } else {
          GlobalMethods.infoToast(context, "No data found");
        }
      } else {
        GlobalMethods.infoToast(context, value.message);
      }

      setState(() {
        appStore.setLoading(false);
      });
    }).onError((error, stackTrace) {
      setState(() {
        appStore.setLoading(false);
      });
      log("error :" + "Server error");

      GlobalMethods.infoToast(context, "Server error");
    });
  }

  getAutomatedText() async {
    setState(() {
      appStore.setLoading(true);
    });

    await getAutomatedTextApi().then((value) {
      setState(() {
        appStore.setLoading(false);
      });

      if (value.data!.isNotEmpty) {
        automatedMessagesList = value.data ?? [];
        List<ReviewModel> filteredAutomatedMessageList =
            automatedMessagesList.where((element) {
          return element.rating == ratingData;
        }).toList();
        selectedMessageList = filteredAutomatedMessageList[0];

        setState(() {});
      } else {
        GlobalMethods.infoToast(context, "no suggested values");
      }
    }).onError((error, stackTrace) {
      setState(() {
        appStore.setLoading(false);
      });
      log("error :" + "Server error");

      GlobalMethods.infoToast(context, "Server error");
    });
  }

  chaeck() async {
    setState(() {
      appStore.setLoading(true);
    });

    await Future.delayed(Duration(seconds: 10)).then((value) {
      setState(() {
        appStore.setLoading(false);
      });
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: language.howWasYourRide),
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: EdgeInsets.only(top: 20, left: 16, right: 16, bottom: 16),
            child: Form(
              key: formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(35),
                        child: commonCachedNetworkImage(
                          widget.onrideRequest.riderProfileImage.validate(),
                          fit: BoxFit.fill,
                          height: 50,
                          width: 50,
                        ),
                      ),
                      SizedBox(width: 16),
                      Text('${widget.onrideRequest.riderName.validate()}',
                          style: boldTextStyle()),
                    ],
                  ),
                  SizedBox(height: 8),
                  RatingBar.builder(
                    initialRating: 5,
                    direction: Axis.horizontal,
                    glow: false,
                    allowHalfRating: true,
                    wrapAlignment: WrapAlignment.spaceBetween,
                    itemCount: 5,
                    itemPadding: EdgeInsets.symmetric(horizontal: 8),
                    itemBuilder: (context, _) =>
                        Icon(Icons.star, color: Colors.amber),
                    onRatingUpdate: (rating) {
                     
                      setState(() {
                        ratingData = rating.toInt();

                      });

                      // List<ReviewModel> filteredAutomatedMessageList =
                      //     automatedMessagesList.where((element) {
                      //   return element.rating == ratingData;
                      // }).toList();

                      // selectedMessageList = filteredAutomatedMessageList[0];

                    },
                  ),
                  SizedBox(height: 4),
                  Text("Add Review", style: boldTextStyle()),
                  SizedBox(height: 4),
                  AppTextField(
                    controller: reviewController,
                    decoration:
                        inputDecoration(context, hint: "Your review..."),
                    textFieldType: TextFieldType.OTHER,
                    minLines: 2,
                    maxLines: 5,
                  ),
                  SizedBox(height: 8),
                  // selectedMessageList.messages.isNotEmpty
                  //     ? Column(
                  //         crossAxisAlignment: CrossAxisAlignment.start,
                  //         children: [
                  //           CustomText(
                  //             data: "Suggested reviews",
                  //             fontweight: FontWeight.bold,
                  //           ),
                  //           height5,
                  //           ListView.separated(
                  //               physics: NeverScrollableScrollPhysics(),
                  //               separatorBuilder: (context, index) {
                  //                 return height10;
                  //               },
                  //               itemCount: selectedMessageList.messages.length,
                  //               shrinkWrap: true,
                  //               itemBuilder: (context, index) {
                  //                 return InkWell(
                  //                     onTap: () {
                  //                       reviewController.clear();

                  //                       reviewController.text =
                  //                           selectedMessageList.messages[index]
                  //                               .toString();
                  //                     },
                  //                     child: Column(
                  //                       crossAxisAlignment:
                  //                           CrossAxisAlignment.start,
                  //                       children: [
                  //                         Chip(
                  //                           label: Text(
                  //                             selectedMessageList
                  //                                 .messages[index]
                  //                                 .toString(),
                  //                                 style: TextStyle(
                  //                                     fontSize: 12,),
                  //                           ),
                  //                         ),
                  //                       ],
                  //                     ));
                  //               }),
                  //         ],
                  //       )
                  //     : SizedBox(),
                  // height20,

                  _advertisement == null
                      ? SizedBox()
                      : getAdWidget(url:_advertisement!.imageURL,
                        width: MediaQuery.of(context).size.width,
                        height: 200,
                      ),
                  height20,
                  AppButton(
                      isLoading: appStore.isLoading,
                      width: double.infinity,
                      text: language.continueD,
                      onPressed: () async {
                        userReviewData();
                        // if (ratingData < 2) {
                        //   if (reviewController.text.isEmpty) {
                        //     GlobalMethods.infoToast(
                        //         context, "please add description");
                        //   } else {
                        //     userReviewData();
                        //   }
                        // } else {
                        //   userReviewData();
                        // }
                      })
                  // AppButtonWidget(
                  //   is_loading: appStore.isLoading,
                  //   text: language.continueD,
                  //   width: MediaQuery.of(context).size.width,
                  //   textStyle: boldTextStyle(color: Colors.white),
                  //   onTap: () {
                  //     // chaeck();

                  //     if (ratingData < 2) {
                  //       if (reviewController.text.isEmpty) {
                  //         GlobalMethods.infoToast(context,  "please add description");
                  //       } else {
                  //         userReviewData();
                  //       }
                  //     } else {
                  //       userReviewData();
                  //     }
                  //   },
                  // )
                ],
              ),
            ),
          ),
          // Observer(builder: (context) {
          //   return Visibility(
          //     visible: appStore.isLoading,
          //     child: loaderWidget(),
          //   );
          // })
        ],
      ),
    );
  }
}
