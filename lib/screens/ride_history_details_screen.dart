import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:rooo_driver/features/care/screens/ComplaintScreen.dart';
import 'package:rooo_driver/features/care/screens/RideHelpScreen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/DriverRatting.dart';
import 'package:rooo_driver/model/RideHistoryDetailsModel.dart';
import 'package:rooo_driver/model/RiderModel.dart';
import 'package:rooo_driver/utils/Extensions/Loader.dart';

class RideHistoryDetailScreen extends StatefulWidget {
  final int id;
  const RideHistoryDetailScreen({super.key, required this.id});

  @override
  State<RideHistoryDetailScreen> createState() =>
      _RideHistoryDetailScreenState();
}

class _RideHistoryDetailScreenState extends State<RideHistoryDetailScreen> {
  RideHistoryDetailsModel? data;

  void getRideHistoryDetailsList() async {
    setState(() {
      appStore.setLoading(true);
    });
    await getRideHistoryDetailApi(id: widget.id)
        .then((value) {
          if (value.data != null) {
            data = value;
          }
          setState(() {
            appStore.setLoading(false);
          });
        })
        .catchError((error) {
          setState(() {
            appStore.setLoading(false);
          });
        });
  }

  @override
  void initState() {
    getRideHistoryDetailsList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(
        title: language.detailScreen,
        actionIconList: [
          /* Raise complaint button*/
          Padding(
            padding: const EdgeInsets.only(right:8.0),
            child: AppButton(
              text: "Raise Complaint",
              backgroundColor: Colors.red,
              onPressed: () async {
                if ((data?.complaint?.status ?? "resolved")
                        .toLowerCase()
                        .trim() ==
                    "resolved") {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => ComplaintScreen(
                        driverRatting: data?.driverRating ?? DriverRatting(),
                        complaintModel: data?.complaint,
                        riderModel: RiderModel(
                          id: data!.data!.id!,
                          riderName: data!.data?.riderName ?? "",
                          riderProfileImage: data!.data?.riderProfileImage ?? "",
                          riderId: data!.data!.riderId!,
                          driverId: data!.data!.driverId!,
                        ),
                        isFromHistory: true,
                      ),
                    ),
                  );
                } else {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) =>
                          RideHelpScreen(indicatorUpdater: () {}),
                    ),
                  );
                }
              },
            ),
          ),
        ],
      ),
      body: appStore.isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Stack(
                children: [
                  Padding(
                    padding: screenPadding,
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          Card(
                            child: Container(
                              padding: screenPadding,
                              child: Column(
                                children: [
                                  inkWellWidget(
                                    onTap: () {},
                                    child: HeaderkeyValue(
                                      key: language.PickupLocationTxt,
                                      value: data!.data!.startAddress!,
                                    ),
                                  ),

                                  Divider(),
                                  HeaderkeyValue(
                                    key: language.destinationLocation,
                                    value: data!.data!.endAddress!,
                                  ),
                                  Divider(),
                                  keyValue(
                                    key: "Ride Id",
                                    value: data!.data!.id.toString(),
                                  ),

                                  keyValue(
                                    key: language.TimeTxt,
                                    value: formatDate(
                                      data!.data!.datetime!,
                                      showTime: true,
                                    ),
                                  ),
                                  keyValue(
                                    key: language.riderName,
                                    value: data!.data?.riderName ?? "",
                                  ),
                                  keyValue(
                                    key: language.statusTxt,
                                    value:
                                        data!.data!.status
                                            .toString()[0]
                                            .toUpperCase() +
                                        data!.data!.status.toString().substring(
                                          1,
                                        ),
                                  ),
                                  keyValue(
                                    key: language.distance,
                                    value:
                                        data!.data!.distance.toString() + " "+
                                        data!.data!.distanceUnit.toString(),
                                  ),

                                  Divider(),

                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      borderRadius: appRadius,
                                      color: Colors.green.withOpacity(.3),
                                    ),
                                    child:
                                        //if canceled ride
                                        data!.data!.status == "canceled"
                                        ? amountkeyValue(
                                            key: "Cancellation charges",
                                            value:
                                                data
                                                    ?.data
                                                    ?.cancelationCharges ??
                                                0,
                                          )
                                        : Column(
                                            children: [
                                              amountkeyValue(
                                                key: language.earning,
                                                value:
                                                    data
                                                        ?.data
                                                        ?.driver_subtract_earning ??
                                                    0,
                                              ),
                                              Text("(Includes Flexi plan deduction and Airport fees added if applicable)",style: TextStyle(
                                                  fontSize: 10,
                                              ),),
                                              amountkeyValue(
                                                key: language.tip,
                                                value: data?.data?.tips ?? 0,
                                              ),

                                              amountkeyValue(
                                                key: language.waitingTime,
                                                value:
                                                    data
                                                        ?.data
                                                        ?.perMinuteWaitingCharge ??
                                                    0,
                                              ),
                                              // amountkeyValue(
                                              //   key: "Subscription Fee charges",
                                              //   value:
                                              //       data?.data?.subscriptionCharges ?? 0,
                                              //       valueColor: Colors.red,
                                              // ),
                                                amountkeyValue(
                                                key: "GST",
                                                value:
                                                    data?.data?.driver_tax ?? 0,
                                              ),

                                              amountkeyValue(
                                                key: language.totalEarning,
                                                value:
                                                    data
                                                        ?.data
                                                        ?.driver_earning ??
                                                    0,
                                              ),
                                            ],
                                          ),
                                  ),
                                  const SizedBox(height: 4),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Observer(
                    builder: (context) {
                      return Visibility(
                        visible: appStore.isLoading,
                        child: Loader(),
                      );
                    },
                  ),
                ],
              ),
            ),
    );
  }

  HeaderkeyValue({required String key, required String value}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(child: CustomText(data: key, size: 20)),
        const SizedBox(width: 20),
        const SizedBox(width: 20),
        Expanded(
          child: CustomText(size: 15, data: value, fontweight: FontWeight.bold),
        ),
      ],
    );
  }

  keyValue({required String key, required String value}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(child: CustomText(data: key, size: 12)),
          const SizedBox(width: 20),
          const SizedBox(width: 20),
          Expanded(
            child: CustomText(
              size: 12,
              data: value,
              fontweight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  amountkeyValue({required String key, required num value,
  Color? valueColor
  
  }) {
    if (value <= 0) {
      return const SizedBox();
    }
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(child: CustomText(data: key, size: 12)),
          CustomText(
            size: 12,
            data: "AUD " + value.toString(),
            fontweight: FontWeight.bold,
            color: valueColor,
          ),
        ],
      ),
    );
  }
}