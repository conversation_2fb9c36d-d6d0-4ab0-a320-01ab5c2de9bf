import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;
import 'package:rooo_driver/features/settings/map_setting/cubit/map_setting_cubit.dart';
import 'package:rooo_driver/features/settings/map_setting/screens/map_setting_screen.dart';
import 'package:rooo_driver/global/constants/app_enums.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/global/state/global_state.dart';

import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vector_math/vector_math.dart' as Math;

import 'package:map_launcher/map_launcher.dart' as map_launcher;
import 'dart:math' as Math;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:rooo_driver/components/custom_text.dart';
import 'package:rooo_driver/screens/dashboard/dashboard_constant.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
// import 'package:flutter_svg/svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:html/parser.dart';

import 'package:rooo_driver/utils/Extensions/Loader.dart';
import 'package:rooo_driver/utils/Extensions/StringExtensions.dart';

import '../main.dart';

import 'Constants.dart';
import 'Extensions/app_common.dart';

Widget dotIndicator(list, i) {
  return SizedBox(
    height: 16,
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(list.length, (ind) {
        return Container(
          height: 8,
          width: 8,
          margin: EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: i == ind ? Colors.white : Colors.grey.withOpacity(0.5),
            borderRadius: BorderRadius.circular(defaultRadius),
          ),
        );
      }),
    ),
  );
}

InputDecoration inputDecoration(
  BuildContext context, {
  String? hint,
  String? label,
  Color? fillColor,
  Widget? prefixIcon,
  Widget? suffixIcon,
  TextStyle? hintTextStyle,
}) {
  return InputDecoration(
    counterText: '',
    prefixIcon: prefixIcon,
    suffixIcon: suffixIcon,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(defaultRadius),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(defaultRadius),
    ),
    disabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(defaultRadius),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(defaultRadius),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(defaultRadius),
    ),
    alignLabelWithHint: true,
    filled: true,
    isDense: true,
    hintText: hint ?? "",
    labelText: label ?? null,
    labelStyle: TextStyle(),
    hintStyle: hintTextStyle ?? TextStyle(),
  );
}

extension BooleanExtensions on bool? {
  /// Validate given bool is not null and returns given value if null.
  bool validate({bool value = false}) => this ?? value;
}

EdgeInsets dynamicAppButtonPadding(BuildContext context) {
  return EdgeInsets.symmetric(vertical: 14, horizontal: 16);
}

Widget inkWellWidget({Function()? onTap, required Widget child}) {
  return InkWell(
    onTap: onTap,
    child: child,
    highlightColor: Colors.transparent,
    hoverColor: Colors.transparent,
    splashColor: Colors.transparent,
  );
}

Widget getAdWidget({
  required String url,
  required double width,
  required double height,
}) {
  return InkWell(
    onTap: () {
      launchUrl(Uri.parse(url));
    },
    child: CachedNetworkImage(
      imageUrl: url,
      placeholder: (context, url) => SizedBox(
        width: width,
        height: height,
        child: Center(child: CircularProgressIndicator()),
      ),
      errorWidget: (context, url, error) => SizedBox(
        width: width,
        height: height,
        child: Center(child: Icon(Icons.error)),
      ),
      imageBuilder: (context, imageProvider) => Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          image: DecorationImage(image: imageProvider, fit: BoxFit.cover),
        ),
      ),
    ),
  );
}

Widget commonCachedNetworkImage(
  String? url, {
  double? height,
  double? width,
  BoxFit? fit,
  AlignmentGeometry? alignment,
  bool usePlaceholderIfUrlEmpty = true,
  double? radius,
  bool showLoading = false,
}) {
  if (url != null && url.isEmpty) {
    return placeHolderWidget(
      height: height,
      width: width,
      fit: fit,
      alignment: alignment,
      radius: radius,
    );
  } else if (url.validate().startsWith('http')) {
    return CachedNetworkImage(
      imageUrl: url!,
      height: height,
      width: width,
      fit: fit,
      alignment: alignment as Alignment? ?? Alignment.center,
      errorWidget: (_, s, d) {
        return placeHolderWidget(
          height: height,
          width: width,
          fit: fit,
          alignment: alignment,
          radius: radius,
        );
      },
      placeholder: (_, s) {
        if (!usePlaceholderIfUrlEmpty) return SizedBox();
        return placeHolderWidget(
          height: height,
          width: width,
          fit: fit,
          alignment: alignment,
          radius: radius,
          showLoading: showLoading,
        );
      },
    );
  } else {
    return Image.network(
      url!,
      height: height,
      width: width,
      fit: fit,
      alignment: alignment ?? Alignment.center,
    );
  }
}

Widget placeHolderWidget({
  double? height,
  double? width,
  BoxFit? fit,
  AlignmentGeometry? alignment,
  double? radius,
  bool showLoading = false,
}) {
  return Stack(
    children: [
      Image.asset(
        'images/placeholder.jpg',
        height: height,
        width: width,
        fit: fit ?? BoxFit.cover,
        alignment: alignment ?? Alignment.center,
      ),
      showLoading
          ? Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              top: 0,
              child: Center(child: CircularProgressIndicator()),
            )
          : const SizedBox(),
    ],
  );
}

List<BoxShadow> defaultBoxShadow({
  Color? shadowColor,
  double? blurRadius,
  double? spreadRadius,
  Offset offset = const Offset(0.0, 0.0),
}) {
  return [
    BoxShadow(
      color: shadowColor ?? Colors.grey.withOpacity(0.2),
      blurRadius: blurRadius ?? 4.0,
      spreadRadius: spreadRadius ?? 1.0,
      offset: offset,
    ),
  ];
}

/// Hide soft keyboard
void hideKeyboard(context) {
  try {
    FocusScope.of(context).requestFocus(FocusNode());
  } catch (e) {
    print('Error hiding keyboard: $e');
  }
}

const double degrees2Radians = pi / 180.0;

double radians(double degrees) => degrees * degrees2Radians;

const default_Language = 'en';

String parseHtmlString(String? htmlString) {
  return parse(parse(htmlString).body!.text).documentElement!.text;
}

Widget loaderWidget({Color? color}) {
  return Container(
    color: Colors.grey[200]!.withOpacity(0.5),
    child: Center(
      child: Container(
        padding: EdgeInsets.all(10),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
        width: 50,
        height: 50,
        child: CircularProgressIndicator(strokeWidth: 3, color: color),
      ),
    ),
  );
}

void afterBuildCreated(Function()? onCreated) {
  makeNullable(
    SchedulerBinding.instance,
  )!.addPostFrameCallback((_) => onCreated?.call());
}

T? makeNullable<T>(T? value) => value;

enum AppDateFormat { yearMonthDay, dayMonthYear }

String formatDate(
  String date, {
  AppDateFormat format = AppDateFormat.dayMonthYear,
  bool showTime = false,
}) {
  try {
    DateTime dateTime = DateTime.parse(date);
    if (format == AppDateFormat.yearMonthDay) {
      if (showTime) {
        return '${dateTime.year}-${dateTime.month}-${dateTime.day}, ${dateTime.hour}:${dateTime.minute > 9 ? dateTime.minute : '0${dateTime.minute}'} ${dateTime.hour >= 12 ? 'PM' : 'AM'}';
      }
      return '${dateTime.year}-${dateTime.month}-${dateTime.day}';
    }
    if (showTime) {
      return '${dateTime.day}-${dateTime.month}-${dateTime.year}, ${dateTime.hour}:${dateTime.minute > 9 ? dateTime.minute : '0${dateTime.minute}'} ${dateTime.hour >= 12 ? 'PM' : 'AM'}';
    }
    return '${dateTime.day}-${dateTime.month}-${dateTime.year}';
  } catch (e) {
    return "Server date error";
  }
}

// Widget emptyWidget({required String message}) {
//   if (!appStore.isLoading) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Image.asset(
//                 IS_DARK_MODE_ON
//                     ? 'images/no_data_white.png'
//                     : 'images/no_data_black.png',
//                 width: 150,
//                 height: 250),
//             height10,
//             CustomText(data: message)
//           ],
//         ),
//       ],
//     );
//   } else {
//     return SizedBox();
//   }
// }

// Future<bool> saveOneSignalPlayerId() async {
//   try {
//     return await OneSignal.shared.getDeviceState().then((value) async {
//       if (value!.userId.validate().isNotEmpty) {
//         return sharedPref.setString(PLAYER_ID, value.userId.validate());
//       } else {
//         return false;
//       }
//     });
//   } catch (e) {
//     return false;
//   }
// }

String dateToString(DateTime date) {
  String day = (date.day < 10)
      ? '0' + date.day.toString()
      : date.day.toString();
  String month = (date.month < 10)
      ? '0' + date.month.toString()
      : date.month.toString();
  return day + '-' + month + '-' + date.year.toString();
}

String dateToInfoString(DateTime datePassed) {
  DateTime date = datePassed.toLocal();
  String day = (date.day < 10)
      ? '0' + date.day.toString()
      : date.day.toString();
  String month = (date.month < 10)
      ? '0' + date.month.toString()
      : date.month.toString();

  String minutes = (date.minute < 10)
      ? '0' + date.minute.toString()
      : date.minute.toString();

  return day +
      '-' +
      month +
      '-' +
      date.year.toString() +
      ' ' +
      date.hour.toString() +
      ':' +
      minutes +
      ' ' +
      (date.hour >= 12 ? 'PM' : 'AM');
}

String timeToString(TimeOfDay time) {
  String hour = (time.hourOfPeriod < 10)
      ? '0' + time.hourOfPeriod.toString()
      : time.hourOfPeriod.toString();
  String minutes = (time.minute < 10)
      ? '0' + time.minute.toString()
      : time.minute.toString();
  return hour + ':' + minutes + ' ' + time.period.name;
}

buttonText({String? status}) {
  if (status == NEW_RIDE_REQUESTED) {
    return language.accepted;
  } else if (status == ACCEPTED) {
    return language.arriving;
  } else if (status == IN_PROGRESS) {
    return 'Reached';
  } else if (status == CANCELED) {
    return language.cancelled;
  } else if (status == ARRIVING) {
    return language.arrived;
  } else if (status == ARRIVED) {
    return 'Start Ride';
  } else if (status == REACHED) {
    return 'End Ride';
  } else if (status == DRIVERCANCELED) {
    return 'You have cancelled the ride , Please wait for admin approval';
  } else if (status == UPLOAD_IMAGES) {
    return 'Start Ride';
  } else {
    return 'Accepted';
  }
}

String statusTypeIcon({String? type}) {
  String icon = 'images/icons/ic_new_ride_requested.png';
  if (type == NEW_RIDE_REQUESTED) {
    icon = 'images/icons/ic_new_ride_requested.png';
  } else if (type == ACCEPTED) {
    icon = 'images/icons/ic_accepted.png';
  } else if (type == ARRIVING) {
    icon = 'images/icons/ic_arriving.png';
  } else if (type == ARRIVED) {
    icon = 'images/icons/ic_arrived.png';
  } else if (type == IN_PROGRESS) {
    icon = 'images/icons/in_progress.png';
  } else if (type == CANCELED) {
    icon = 'images/icons/ic_canceled.png';
  } else if (type == COMPLETED) {
    icon = 'images/icons/ic_completed.png';
  }
  return icon;
}

bool get isRTL => rtlLanguage.contains(appStore.selectedLanguage);

double calculateDistance(lat1, lon1, lat2, lon2) {
  var p = 0.017453292519943295;
  var a =
      0.5 -
      cos((lat2 - lat1) * p) / 2 +
      cos(lat1 * p) * cos(lat2 * p) * (1 - cos((lon2 - lon1) * p)) / 2;
  return (12742 * asin(sqrt(a))).toStringAsFixed(2).toDouble();
}

Widget totalCount({
  required String title,
  required num amount,
  bool isTotal = false,
}) {
  if (amount <= 0) {
    return const SizedBox();
  }

  return Column(
    children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              title!,
              style: isTotal == true ? boldTextStyle() : primaryTextStyle(),
            ),
          ),
          Expanded(
            child: Text(
              appStore.currencyPosition == LEFT
                  ? '${appStore.currencyCode} $amount'
                  : '$amount ${appStore.currencyCode}',
              textAlign: TextAlign.end,
              style: isTotal == true ? boldTextStyle() : primaryTextStyle(),
            ),
          ),
        ],
      ),
      height3,
    ],
  );
}

// Future<bool> checkLocationPermission(BuildContext context) async {
//   LocationPermission locationPermission = await Geolocator.requestPermission();
//   if (locationPermission == LocationPermission.whileInUse ||
//       locationPermission == LocationPermission.always) {
//     if (appStore.isLoggedIn) {
//       GlobalMethods.pushAndRemoveAll(
//           context: context,
//           screen: RideScreen(),
//           screenIdentifier: ScreenIdentifier.InitialScreen);
//       // launchScreen(context, InitialScreen(), isNewTask: true);
//     } else {
//       GlobalMethods.pushAndRemoveAll(
//           context: context,
//           screen: LoginScreen(),
//           screenIdentifier: ScreenIdentifier.LoginScreen);

//       // launchScreen(context, LoginScreen(),
//       //     pageRouteAnimation: PageRouteAnimation.Slide, isNewTask: true);
//     }
//   } else if (locationPermission == LocationPermission.denied ||
//       locationPermission == LocationPermission.deniedForever) {
//     // Open system level location permission
//     Geolocator.openAppSettings().then((value) {}).catchError((onError) {
//       GlobalMethods.errorToast(context, language.errorMsg);
//     });
//   }
//   return false;
// }

Widget snapWidgetHelper<T>(
  AsyncSnapshot<T> snap, {
  Widget? errorWidget,
  Widget? loadingWidget,
  String? defaultErrorMessage,
  @Deprecated('Do not use this') bool checkHasData = false,
  Widget Function(String)? errorBuilder,
}) {
  if (snap.hasError) {
    log(snap.error);
    if (errorBuilder != null) {
      return errorBuilder.call(defaultErrorMessage ?? snap.error.toString());
    }
    return Center(
      child: errorWidget ?? Text(defaultErrorMessage ?? snap.error.toString()),
    );
  } else if (!snap.hasData) {
    return loadingWidget ?? Loader();
  } else {
    return SizedBox();
  }
}

String changeStatusText(String? status) {
  if (status == COMPLETED) {
    return language.completed;
  } else if (status == CANCELED) {
    return language.cancelled;
  } else if (status == REFERRAL_OFFER) {
    return language.offerTxt;
  } else if (status == REFERRAL_STATUS) {
    return language.statusTxt;
  } else if (status == BANK_INFO) {
    return language.bankInfoTxt;
  } else if (status == WALLET) {
    return language.walletTxt;
  } else if (status == CLOSED) {
    return language.closedTxt;
  } else if (status == PENDING) {
    return language.pendingTxt;
  } else if (status == REFERRALS) {
    return language.referralsTxt;
  } else if (status == "closed") {
    return "Closed";
  }
  return status.toString();
}

String changeGender(String? name) {
  if (name == MALE) {
    return language.male;
  } else if (name == FEMALE) {
    return language.female;
  } else if (name == OTHER) {
    return language.other;
  }
  return '';
}

String paymentStatus(String paymentStatus) {
  if (paymentStatus.toLowerCase() == PAYMENT_PENDING.toLowerCase()) {
    return language.pending;
  } else if (paymentStatus.toLowerCase() == PAYMENT_FAILED.toLowerCase()) {
    return language.failed;
  } else if (paymentStatus == PAYMENT_PAID) {
    return language.paid;
  } else if (paymentStatus == CASH) {
    return language.cash;
  }
  return language.pending;
}

Widget loaderWidgetLogIn() {
  return Center(
    child: Card(
      color: IS_DARK_MODE_ON ? Colors.white : null,
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    ),
  );
}

Widget earningWidget({String? text, String? image, num? totalAmount}) {
  return Container(
    width: 160,
    padding: EdgeInsets.all(8),
    decoration: BoxDecoration(
      boxShadow: [
        BoxShadow(color: Colors.black12, blurRadius: 10.0, spreadRadius: 0),
      ],
      borderRadius: BorderRadius.circular(defaultRadius),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(text!, style: boldTextStyle(color: Colors.white)),
            SizedBox(height: 8),
            Text(
              totalAmount.toString(),
              style: boldTextStyle(color: Colors.white),
            ),
          ],
        ),
        Expanded(child: SizedBox(width: 8)),
        Container(
          margin: EdgeInsets.only(left: 2),
          padding: EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(defaultRadius),
          ),
          child: Image.asset(image!, fit: BoxFit.cover, height: 40, width: 40),
        ),
      ],
    ),
  );
}

Widget earningText({String? title, num? amount}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Text(title!, style: primaryTextStyle()),
      Text(
        "AUD " + amount!.toStringAsFixed(digitAfterDecimal),
        style: TextStyle(),
      ),
    ],
  );
}

String intToTimeLeftInSeconds(int value) {
  int h, m, s;

  h = value ~/ 3600;

  m = ((value - h * 3600)) ~/ 60;

  s = value - (h * 3600) - (m * 60);

  String hourLeft = h.toString().length < 2 ? "0" + h.toString() : h.toString();

  String minuteLeft = m.toString().length < 2
      ? "0" + m.toString()
      : m.toString();

  String secondsLeft = s.toString().length < 2
      ? "0" + s.toString()
      : s.toString();

  String result = "$hourLeft:$minuteLeft:$secondsLeft";

  return result;
}

List<Map<String, dynamic>> removeMatchingElements(
  List<Map<String, dynamic>> list1,
  List list2,
) {
  // List<Map<String,dynamic>> data=[];
  // list2.forEach((element){
  //   // data.add(jsonDecode(element));
  // });
  // Iterate over the second list
  for (var item2 in list2) {
    // Find and remove matching elements in the first list
    list1.removeWhere((item1) => item1['location_id'] == item2['location_id']);
  }
  return list1;
}

tabContainer({required List<String> tabs}) {
  return Container(
    height: 40,
    margin: EdgeInsets.only(right: 16, left: 16, top: 16),
    decoration: BoxDecoration(borderRadius: radius()),
    child: Container(
      child: TabBar(
        tabs: tabs.map((e) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [Flexible(child: Text(changeStatusText(e)))],
          );
        }).toList(),
      ),
    ),
  );
}

bool isDarkMode() {
  int hour = DateTime.now().hour;
  return [0, 24, 1, 2, 3, 4, 5, 6].contains(hour);
}

Future<Uint8List> getBytesFromAsset(String path, BuildContext context) async {
  double pixelRatio = MediaQuery.of(context).devicePixelRatio;
  ByteData data = await rootBundle.load(path);
  ui.Codec codec = await ui.instantiateImageCodec(
    data.buffer.asUint8List(),
    targetWidth: pixelRatio.round() * 30,
  );
  ui.FrameInfo fi = await codec.getNextFrame();
  return (await fi.image.toByteData(
    format: ui.ImageByteFormat.png,
  ))!.buffer.asUint8List();
}

navigateToOuterMap({
  required BuildContext context,
  required map_launcher.Coords destinationCoords,
  required map_launcher.Coords sourceCoords,
  required String source,
  required String destination,
  required List<map_launcher.Waypoint> wayPoints,
}) async {
  if (GlobalState.selectedMap == null) {
    await BlocProvider.of<MapSettingCubit>(
      context,
    ).getMapSetting(userId: sharedPref.getInt(USER_ID)!);
  }
  final availableMaps = await map_launcher.MapLauncher.installedMaps;

  if (availableMaps.isEmpty) {
    GlobalMethods.pushScreen(
      context: context,
      screen: MapSettingScreen(),
      screenIdentifier: ScreenIdentifier.MapSettingScreen,
    );
    return;
  }

  bool isThereAnyOfferedMapAvailable = availableMaps.any((element) {
    return AppOfferedMaps.any(
      (offeredMap) => offeredMap.mapType.name == element.mapType.name,
    );
  });
  if (!isThereAnyOfferedMapAvailable) {
    GlobalMethods.pushScreen(
      context: context,
      screen: MapSettingScreen(),
      screenIdentifier: ScreenIdentifier.MapSettingScreen,
    );
    return;
  }
  /* check selected map */
  List<map_launcher.AvailableMap> selectedMap = availableMaps
      .where((elelment) => elelment.mapType.name == GlobalState.selectedMap)
      .toList();

  if (selectedMap.isNotEmpty) {
    selectedMap[0].showDirections(
      destination: destinationCoords,
      destinationTitle: destination,
      origin: sourceCoords,
      originTitle: source,
      directionsMode: map_launcher.DirectionsMode.driving,
      waypoints: wayPoints,
    );
    return;
  } else {
    late map_launcher.AvailableMap targetMap;

    var googleMap = availableMaps
        .where(
          (element) => element.mapType.name == map_launcher.MapType.google.name,
        )
        .toList();

    if (googleMap.isNotEmpty) {
      targetMap = googleMap[0];
    } else {
      /* get the first offered map, that is availabed on the device */
      targetMap = AppOfferedMaps.firstWhere(
        (element) => availableMaps.any(
          (availableMap) => availableMap.mapType.name == element.mapType.name,
        ),
      );
    }
    targetMap.showDirections(
      destination: destinationCoords,
      destinationTitle: destination,
      origin: sourceCoords,
      originTitle: source,
      directionsMode: map_launcher.DirectionsMode.driving,
      waypoints: wayPoints,
    );
  }
}

custom_linear_progressindicator({Color? color}) {
  return LinearProgressIndicator(color: color ?? Colors.green);
}

custom_loader() {
  return SizedBox(height: 25, width: 25, child: CircularProgressIndicator());
}

// loader_bottomsheet({required BuildContext context}) {
//   return Container(
//     height: MediaQuery.of(context).size.height,
//     color: Colors.grey.withOpacity(.3),
//     child: BottomSheet(
//         onClosing: () {},
//         builder: (context) {
//           return Padding(
//             padding: screenPadding,
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 CustomText(
//                   data: "Loading....",
//                 ),
//                 SizedBox(
//                     height: 20,
//                     width: 20,
//                     child: CircularProgressIndicator(
//                       strokeWidth: .5,
//                     ))
//               ],
//             ),
//           );
//         }),
//   );
// }

Future<bool> chechIfSameLocation({required Position event}) {
  double venueLat = driver_current_position.latitude; // Last known lat
  double venueLng = driver_current_position.longitude; // Last known lng

  double latDistance = Math.radians(event.latitude - venueLat);
  double lngDistance = Math.radians(event.longitude - venueLng);
  double a =
      (Math.sin(latDistance / 2) * Math.sin(latDistance / 2)) +
      (Math.cos(Math.radians(event.latitude))) *
          (Math.cos(Math.radians(venueLat))) *
          (Math.sin(lngDistance / 2)) *
          (Math.sin(lngDistance / 2));

  double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  double dist = 6371 * c;
  if (dist < 0.09) {
    log("sameLocation->>>");

    return Future.value(true);

    /* If it's within 10m, we assume we're not moving */
  } else {
    return Future.value(false);
  }
}

Future<void> animateMap({
  required Completer<GoogleMapController> completer,
  double? bearing,
  double? zoom = null,
}) async {
  final GoogleMapController controller = await completer.future;
  controller.animateCamera(
    CameraUpdate.newCameraPosition(
      CameraPosition(
        target: LatLng(
          driver_current_position.latitude,
          driver_current_position.longitude,
        ),
        zoom: zoom ?? await controller.getZoomLevel(),
      ),
    ),
  );
}

String getTitleCase(String text) {
  return text[0].toUpperCase() + text.substring(1);
}
