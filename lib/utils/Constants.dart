import 'package:flutter/material.dart';
import 'package:map_launcher/map_launcher.dart' as map_launcher;

import '../main.dart';

const mAppName = 'ROOO Driver';
var errorThisFieldRequired = 'This field is required';

const hardcoded = true;
const areLogsEnabled = false;

// const DOMAIN_URL =
//     'https://www.rooo.com.au/software'; // Don't add slash at the end of the url
// const mBaseUrl = "$DOMAIN_URL/api/";

// // late final MqttServerClient client;

// const mOneSignalAppIdRider = '************************************';
// const mOneSignalRestKeyRider =
//     '************************************************';

//      const zegoAppId = 697103749;
//    const zegoAppSign =
//       '2f51d4c8da2997643f81c52fa7cf43ee5f5e2636752a4474115ff7ec666a7074';

// const mOneSignalAppIdDriver = '************************************';
// const mOneSignalRestKeyDriver =
//     '************************************************';

const PRESENT_TOP_UP_AMOUNT_CONST = '10|20|30';

const passwordLengthGlobal = 8;
const defaultRadius = 10.0;

const textPrimarySizeGlobal = 16.00;
const textBoldSizeGlobal = 16.00;
const textSecondarySizeGlobal = 14.00;

double tabletBreakpointGlobal = 600.0;
double desktopBreakpointGlobal = 720.0;
double statisticsItemWidth = 230.0;
double defaultAppButtonElevation = 4.0;

bool enableAppButtonScaleAnimationGlobal = true;
int? appButtonScaleAnimationDurationGlobal;
ShapeBorder? defaultAppButtonShapeBorder;

var customDialogHeight = 140.0;
var customDialogWidth = 220.0;

enum ThemeModes { SystemDefault, Light, Dark }

const LoginTypeApp = 'app';
const LoginTypeGoogle = 'google';
const LoginTypeOTP = 'otp';
const LoginTypeApple = 'apple';

const currencySymbol = '\$';
const currencyNameConst = 'usd';
const defaultPhoneCode = '+91';
const defaultCountryIsoCode = 'AU';

const digitAfterDecimal = 2;

/// SharedReference keys

const REMEMBER_ME = 'REMEMBER_ME';
const IS_FIRST_TIME = 'IS_FIRST_TIME';
const IS_LOGGED_IN = 'IS_LOGGED_IN';

const ON_RIDE_MODEL = 'ON_RIDE_MODEL';
const IS_TIME2 = 'IS_TIME2';

const USER_ID = 'USER_ID';
const FIRST_NAME = 'FIRST_NAME';
const LAST_NAME = 'LAST_NAME';
const IS_PROFILE_COMPLETE = 'IS_PROFILE_COMPLETE';
const TOKEN = 'TOKEN';
const USER_EMAIL = 'USER_EMAIL';
const USER_TOKEN = 'USER_TOKEN';
const USER_PROFILE_PHOTO = 'USER_PROFILE_PHOTO';
const USER_TYPE = 'USER_TYPE';
const USER_NAME = 'USER_NAME';
const USER_PASSWORD = 'USER_PASSWORD';
const USER_ADDRESS = 'USER_ADDRESS';
const STATUS = 'STATUS';
const CONTACT_NUMBER = 'CONTACT_NUMBER';
const PLAYER_ID = 'PLAYER_ID';
const UID = 'UID';
const IS_LOCATION_PERMISSION_ASKED = "IS_LOCATION_PERMISSION_ASKED";

const FIRESTORE_ID = 'FIRESTORE_ID';

const ADDRESS = 'ADDRESS';
const REFFERALCODE = 'REFFERALCODE';
const IS_LOCATION_ASKED = 'IS_LOCATION_ASKED';

const IS_OTP = 'IS_OTP';
const IS_GOOGLE = 'IS_GOOGLE';
const GENDER = 'GENDER';
const OTHER_GENDER_TEXT = 'OTHER_GENDER_TEXT';
const IS_ONLINE = 'IS_ONLINE';
const IS_Verified_Driver = 'is_verified_driver';
const ADMIN = 'admin';
const DRIVER = 'driver';
const RIDER = 'rider';
const IS_DARK_MODE = 'IS_DARK_MODE';
const IS_DARK_MODE_SETTING = "";

const CHANGE_DARK_MODE = 'CHANGE_DARK_MODE';
bool IS_DARK_MODE_ON = false;

String DARKMODE_SETTING = "DARKMODE_SETTING";
String DARKMODE = "DARKMODE";

String LIGHTMODE = "LIGHTMODE";
String SYSTEMMODE = "SYSTEMMODE";

String DARKMODE_ALWAYS_ON = "always_on";
String DARKMODE_ALWAYS_OFF = "always_off";
String DARKMODE_AUTOMATIC = "automatic";
String DARKMODE_PHONESETTING = "phone_setting";

/// Taxi Status
const IN_ACTIVE = 'inactive';
const PENDING = 'pending';
const BANNED = 'banned';
const REJECT = 'reject';

/// Wallet keys
const CREDIT = 'credit';
const DEBIT = 'debit';

const PAYMENT_TYPE_STRIPE = 'stripe';
const PAYMENT_TYPE_RAZORPAY = 'razorpay';
const PAYMENT_TYPE_PAYSTACK = 'paystack';
const PAYMENT_TYPE_FLUTTERWAVE = 'flutterwave';
const PAYMENT_TYPE_PAYPAL = 'paypal';
const PAYMENT_TYPE_PAYTABS = 'paytabs';
const PAYMENT_TYPE_MERCADOPAGO = 'mercadopago';
const PAYMENT_TYPE_PAYTM = 'paytm';
const PAYMENT_TYPE_MYFATOORAH = 'myfatoorah';

const CASH = 'cash';

const stripeURL = 'https://api.stripe.com/v1/payment_intents';

/// My Rides Status
const OPPORTUNITY_CANCELED_BY_RIDER = 'booked_ride_cancel';
const UPCOMING = 'upcoming';
const NEW_RIDE_REQUESTED = 'new_ride_requested';
const ACCEPTED = 'accepted';
const ARRIVING = 'arriving';
const UPLOAD_IMAGES = 'upload_images';
const ARRIVED = 'arrived';
const DRIVERCANCELED = "driver_canceled";
const FIRSTOTPVERIFIED = 'firstotpverified';
const SECONDOTPVERIFIED = 'secondotpverified';

const GLOBAL_REGION_ID = 'GLOBAL_REGION_ID';
const GLOBAL_DRIVER_ID = 'GLOBAL_DRIVER_ID';

const IN_PROGRESS = 'in_progress';
const REACHED = 'reached';

const CANCELED = 'canceled';
const COMPLETED = 'completed';

//user status
const ACTIVE = 'active';

//other status
const REFERRAL_STATUS = 'Status';
const REFERRAL_OFFER = 'Offer';
const REFERRALS = 'referrals';

const BANK_INFO = 'Bank Info';
const WALLET = 'Wallet';
const CLOSED = 'Closed';
const PENDING_CARE = 'Pending';
const COMPLAIN_COMMENT = "complaintcomment";

///FireBase Collection Name
const MESSAGES_COLLECTION = "messages";
const USER_COLLECTION = "users";
const CONTACT_COLLECTION = "contact";
const CHAT_DATA_IMAGES = "chatImages";

const IS_ENTER_KEY = "IS_ENTER_KEY";
const SELECTED_WALLPAPER = "SELECTED_WALLPAPER";
const PER_PAGE_CHAT_COUNT = 50;
const PAYMENT_PENDING = 'pending';
const PAYMENT_FAILED = 'failed';
const PAYMENT_PAID = 'paid';
const SELECTED_LANGUAGE_CODE = 'selected_language_code';
const THEME_MODE_INDEX = 'theme_mode_index';
const CHANGE_LANGUAGE = 'CHANGE_LANGUAGE';
const CHANGE_MONEY = 'CHANGE_MONEY';
const LOGIN_TYPE = 'login_type';

const TEXT = "TEXT";
const IMAGE = "IMAGE";

const VIDEO = "VIDEO";
const AUDIO = "AUDIO";

const FIXED_CHARGES = "fixed_charges";
const MIN_DISTANCE = "min_distance";
const MIN_WEIGHT = "min_weight";
const PER_DISTANCE_CHARGE = "per_distance_charges";
const PER_WEIGHT_CHARGE = "per_weight_charges";

const CHARGE_TYPE_FIXED = 'fixed';
const CHARGE_TYPE_PERCENTAGE = 'percentage';
const CASH_WALLET = 'cash_wallet';
const SELECT = 'select';
const MALE = 'male';
const FEMALE = 'female';
const PREFER_NOT_TO_SAY = 'prefer not to say';
const OTHER = 'other';
const LEFT = 'left';

/// app setting key
const CLOCK = 'clock';
const PRESENT_TOPUP_AMOUNT = 'preset_topup_amount';
const PRESENT_TIP_AMOUNT = 'preset_tip_amount';
const MAX_TIME_FOR_RIDER_MINUTE =
    'max_time_for_find_drivers_for_regular_ride_in_minute';
const MAX_TIME_FOR_DRIVER_SECOND =
    'ride_accept_decline_duration_for_driver_in_second';
const MIN_AMOUNT_TO_ADD = 'min_amount_to_add';
const MAX_AMOUNT_TO_ADD = 'max_amount_to_add';
const APPLY_ADDITIONAL_FEE = 'apply_additional_fee';
const RIDE_IDS = 'RIDE_ID';

//chat
List<String> rtlLanguage = ['ar', 'ur'];

enum MessageType { TEXT, IMAGE, VIDEO, AUDIO }

extension MessageExtension on MessageType {
  String? get name {
    switch (this) {
      case MessageType.TEXT:
        return 'TEXT';
      case MessageType.IMAGE:
        return 'IMAGE';
      case MessageType.VIDEO:
        return 'VIDEO';
      case MessageType.AUDIO:
        return 'AUDIO';
      default:
        return null;
    }
  }
}

//signal notification type
const rideAlertNotification = 'ride_alert';
const rideQuietPeiodNotification = 'quite_period';
const rideStartNotification = 'ride_start';

//
const String FromLoginScreen = 'login';
const String FromRegistrationScreen = 'registration';
// const String isFromLoginScreen='login';

late BuildContext savedContext;

String printAmount(String amount) {
  return appStore.currencyPosition == LEFT
      ? '${appStore.currencyCode} $amount'
      : '$amount ${appStore.currencyCode}';
}

const APP_ID = 66547443;
const APP_SIGN =
    '1ca52a38d44994f46d5df26aa6c24df6be79c125d2a00241efd4e0093158b7b1';

const PDF_NAME = 'ROOO Driver  Private Limited';
const PDF_ADDRESS = 'Sarah Street 9, Beijing, Ahmedabad';
const PDF_CONTACT_NUMBER = '+91 7600712311';

var errorMessage = 'Something Went Wrong';
var serverErrorMessage = 'Server error';

TextStyle roooTextStyleBold = const TextStyle(
  fontSize: 18,
  fontWeight: FontWeight.bold,
  color: Colors.black,
);

//sizedboxes===height
SizedBox heightSpace1 = const SizedBox(height: 1);
SizedBox height2 = const SizedBox(height: 2);
SizedBox height3 = const SizedBox(height: 3);
SizedBox height4 = const SizedBox(height: 4);
SizedBox height5 = const SizedBox(height: 5);
SizedBox height10 = const SizedBox(height: 10);
SizedBox height15 = const SizedBox(height: 15);
SizedBox height20 = const SizedBox(height: 20);

//sizedboxes===width
SizedBox width1 = const SizedBox(width: 1);
SizedBox width2 = const SizedBox(width: 2);
SizedBox width3 = const SizedBox(width: 3);
SizedBox width4 = const SizedBox(width: 4);
SizedBox width5 = const SizedBox(width: 5);
SizedBox width10 = const SizedBox(width: 10);
SizedBox width15 = const SizedBox(width: 15);
SizedBox width20 = const SizedBox(width: 20);

List listOfMonths = [
  {"abbreviation": "Jan", "name": "January"},
  {"abbreviation": "Feb", "name": "February"},
  {"abbreviation": "Mar", "name": "March"},
  {"abbreviation": "Apr", "name": "April"},
  {"abbreviation": "May", "name": "May"},
  {"abbreviation": "Jun", "name": "June"},
  {"abbreviation": "Jul", "name": "July"},
  {"abbreviation": "Aug", "name": "August"},
  {"abbreviation": "Sep", "name": "September"},
  {"abbreviation": "Oct", "name": "October"},
  {"abbreviation": "Nov", "name": "November"},
  {"abbreviation": "Dec", "name": "December"},
];

List listOfdays = [
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  16,
  17,
  18,
  19,
  20,
  21,
  22,
  23,
  24,
  25,
  26,
  27,
  28,
  29,
  31,
  32,
];

int global_region_id = -1;

//darkmode Enum
enum DarkLightTimingMode { AlwaysOn, AlwaysOff, Automatic, PhoneSetting, Empty }

//about us instruction text
String? about_us_instruction_text;
//delete account instruction text
String delete_account_instruction_text = '';

String chatMsgNotification = "chat_msg";
int chat_msg_count = 0;
late Function chatCountUpdater;

//emailVerified
String? email_verified_at = null;
//ios//
const APPLE_ID = 'apple_id';
const APPLE_EMAIL = 'apple_email';
const APPLE_FIRST_NAME = 'apple_first_name';
const APPLE_LAST_NAME = 'apple_last_name';

abstract class DRIVER_PENDINGS {
  static const String vehicleAdd = 'add_vehicle';
  static const String vehicleApproval = 'pending_for_approval_vehicle';
  static const String vehicleInactive = 'not_active_vehicle';
  static const String documentUpload = 'add_driver_document';
  static const String documentApproval = 'pending_for_approval_driver_document';
  static const String quiz = 'pending_quiz';
  static const String profileIncomplete = 'profile_incomplete';
  static const String subscriptionPending = 'pending_subscription';
  static const String subscriptionPayment = 'pending_subscription_payment';
  static const String subscriptionExpired = 'subscription_expierd';
  static const String profilePhotoMissing = 'profile_photo_incomplete';
}

final List<map_launcher.AvailableMap> AppOfferedMaps = [
  map_launcher.AvailableMap(
    mapName: "Google Maps",
    mapType: map_launcher.MapType.google,
    icon: "",
  ),
  map_launcher.AvailableMap(
    mapName: "Apple Maps",
    mapType: map_launcher.MapType.apple,
    icon: "",
  ),
  map_launcher.AvailableMap(
    mapName: "Waze Maps",
    mapType: map_launcher.MapType.waze,
    icon: "",
  ),
];
